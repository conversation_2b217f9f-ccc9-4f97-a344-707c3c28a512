#!/usr/bin/env python3
"""
Test script for VSE Host Relay

This script tests the core functionality of the host relay application
without requiring a full GUI or actual X32 hardware.
"""

import sys
import json
import time
import threading
from unittest.mock import Mock, patch
import unittest


class TestVSEHostRelay(unittest.TestCase):
    def setUp(self):
        """Set up test fixtures"""
        # Mock the GUI components
        with patch('tkinter.Tk'), \
             patch('tkinter.ttk.Style'), \
             patch('websocket.WebSocketApp'), \
             patch('xair_api.connect'):
            
            # Import after patching to avoid GUI creation
            from vse_host_relay import VSEHostRelay
            self.relay = VSEHostRelay()
            
            # Mock the GUI elements
            self.relay.root = Mock()
            self.relay.log_text = Mock()
            self.relay.server_status_var = Mock()
            self.relay.x32_status_var = Mock()
            self.relay.session_status_var = Mock()

    def test_initialization(self):
        """Test relay initialization"""
        self.assertEqual(self.relay.version, "1.0.0")
        self.assertEqual(self.relay.app_name, "VSE Host Relay")
        self.assertFalse(self.relay.server_connected)
        self.assertFalse(self.relay.x32_connected)
        self.assertFalse(self.relay.session_active)

    def test_configuration(self):
        """Test configuration management"""
        self.assertIn('server_url', self.relay.config)
        self.assertIn('x32_ip', self.relay.config)
        self.assertIn('x32_port', self.relay.config)
        self.assertEqual(self.relay.config['x32_port'], 10023)

    def test_osc_command_handling(self):
        """Test OSC command processing"""
        # Mock X32 connection
        mock_mixer = Mock()
        self.relay.x32_mixer = mock_mixer
        self.relay.x32_connected = True
        
        # Test mute command
        mute_command = {
            'type': 'mute',
            'channel': 1,
            'action': 'mute'
        }
        
        self.relay._handle_osc_command(mute_command)
        
        # Verify OSC message was sent
        mock_mixer.send.assert_called_with('/ch/01/mix/on', 0)

    def test_fader_command_handling(self):
        """Test fader command processing"""
        # Mock X32 connection
        mock_mixer = Mock()
        self.relay.x32_mixer = mock_mixer
        self.relay.x32_connected = True
        
        # Test fader command
        fader_command = {
            'type': 'fader',
            'channel': 5,
            'value': 0.75
        }
        
        self.relay._handle_osc_command(fader_command)
        
        # Verify OSC message was sent
        mock_mixer.send.assert_called_with('/ch/05/mix/fader', 0.75)

    def test_websocket_message_handling(self):
        """Test WebSocket message processing"""
        # Test valid JSON message
        test_message = json.dumps({
            'type': 'osc_command',
            'command': {
                'type': 'mute',
                'channel': 2,
                'action': 'unmute'
            }
        })
        
        # Mock X32 connection
        mock_mixer = Mock()
        self.relay.x32_mixer = mock_mixer
        self.relay.x32_connected = True
        
        # Process message
        self.relay._on_websocket_message(None, test_message)
        
        # Verify command was processed
        mock_mixer.send.assert_called_with('/ch/02/mix/on', 1)

    def test_invalid_osc_command(self):
        """Test handling of invalid OSC commands"""
        # Mock X32 connection
        mock_mixer = Mock()
        self.relay.x32_mixer = mock_mixer
        self.relay.x32_connected = True
        
        # Test invalid command type
        invalid_command = {
            'type': 'invalid_command',
            'channel': 1
        }
        
        # Should not raise exception
        self.relay._handle_osc_command(invalid_command)
        
        # Should not call mixer.send
        mock_mixer.send.assert_not_called()

    def test_session_join_message(self):
        """Test session join message creation"""
        self.relay.config['session_key'] = '123456'
        self.relay.config['host_user_id'] = 'test_host'
        
        # Mock websocket
        mock_websocket = Mock()
        self.relay.websocket = mock_websocket
        
        self.relay._join_session()
        
        # Verify join message was sent
        mock_websocket.send.assert_called_once()
        
        # Parse the sent message
        sent_args = mock_websocket.send.call_args[0]
        message = json.loads(sent_args[0])
        
        self.assertEqual(message['type'], 'join_session_room')
        self.assertEqual(message['session_key'], '123456')
        self.assertEqual(message['user_id'], 'test_host')
        self.assertEqual(message['role'], 'host')


def run_integration_test():
    """Run integration test with mock server"""
    print("Running integration test...")
    
    # Create a simple mock WebSocket server
    import socket
    import threading
    
    def mock_server():
        """Simple mock server for testing"""
        server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
        server_socket.bind(('localhost', 8765))
        server_socket.listen(1)
        
        print("Mock server listening on localhost:8765")
        
        try:
            conn, addr = server_socket.accept()
            print(f"Connection from {addr}")
            
            # Simple HTTP upgrade response
            response = (
                "HTTP/1.1 101 Switching Protocols\r\n"
                "Upgrade: websocket\r\n"
                "Connection: Upgrade\r\n"
                "\r\n"
            )
            conn.send(response.encode())
            
            # Keep connection alive for a bit
            time.sleep(2)
            conn.close()
            
        except Exception as e:
            print(f"Mock server error: {e}")
        finally:
            server_socket.close()
    
    # Start mock server in background
    server_thread = threading.Thread(target=mock_server, daemon=True)
    server_thread.start()
    
    time.sleep(0.5)  # Let server start
    
    print("✓ Integration test completed")


def main():
    """Main test runner"""
    print("VSE Host Relay Test Suite")
    print("=" * 30)
    
    # Run unit tests
    print("Running unit tests...")
    unittest.main(argv=[''], exit=False, verbosity=2)
    
    print("\n" + "=" * 30)
    
    # Run integration test
    run_integration_test()
    
    print("\n" + "=" * 30)
    print("All tests completed!")


if __name__ == "__main__":
    main()
