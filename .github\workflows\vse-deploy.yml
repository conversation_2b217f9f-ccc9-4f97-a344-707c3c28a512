name: VSE Deploy

on:
  pull_request:
  push:
    branches:
      - main

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      ### Step 1: Checkout Repository
      - name: Checkout code
        uses: actions/checkout@v3

      ### Step 2: Configure AWS Credentials
      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      ### Step 3: Install Terraform
      - name: Install Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: 1.10.5

      ### Step 4: Terraform Destroy (Clean Slate)
      - name: Force Unlock Terraform State
        run: |
          echo "🔓 Force unlocking any stuck Terraform state locks..."
          
          # Force unlock ECS state if locked
          cd terraform/environments/beta/ecs
          terraform init -backend-config="key=beta/ecs/terraform.tfstate" 2>/dev/null || echo "No ECS backend to initialize"
          terraform force-unlock -force ed4a18ba-24be-abb5-94dd-ee332b9711ff 2>/dev/null || echo "No ECS lock to unlock"
          
          # Force unlock ECR state if locked
          cd ../ecr
          terraform init -backend-config="key=beta/ecr/terraform.tfstate" 2>/dev/null || echo "No ECR backend to initialize"
          # Try to unlock any potential ECR locks (we don't have the ID, so this might fail)
          terraform force-unlock -force $(terraform show -json 2>/dev/null | jq -r '.values.root_module.resources[0].values.id // "unknown"') 2>/dev/null || echo "No ECR lock to unlock"
          
          echo "✅ State unlock attempts completed"
        continue-on-error: true

      - name: Terraform Destroy ECS Infrastructure
        working-directory: terraform/environments/beta/ecs
        if: false
        timeout-minutes: 15
        run: |
          echo "🧹 Destroying existing ECS infrastructure for clean slate..."
          
          # Check if backend exists
          if terraform init -backend-config="key=beta/ecs/terraform.tfstate" 2>/dev/null; then
            echo "✅ Backend initialized successfully"
            
            # Check if state exists
            if terraform show 2>/dev/null | head -1; then
              echo "📋 Found existing state, proceeding with destroy..."
              # Provide dummy ECR URL for destroy (not used during destroy) and disable locking
              timeout 10m terraform destroy -auto-approve -lock=false -var "ecr_repository_url=dummy-url-for-destroy" || echo "⚠️  Destroy timed out or failed"
              exit 1
            else
              echo "ℹ️  No existing state found, nothing to destroy"
            fi
          else
            echo "ℹ️  No backend found, nothing to destroy"
          fi

      - name: Terraform Destroy ECR Repository
        working-directory: terraform/environments/beta/ecr
        if: false
        timeout-minutes: 10
        run: |
          echo "🧹 Destroying existing ECR repository for clean slate..."
          
          # Check if backend exists
          if terraform init -backend-config="key=beta/ecr/terraform.tfstate" 2>/dev/null; then
            echo "✅ Backend initialized successfully"
            
            # Check if state exists
            if terraform show 2>/dev/null | head -1; then
              echo "📋 Found existing state, proceeding with destroy..."
              timeout 8m terraform destroy -auto-approve -lock=false || echo "⚠️  Destroy timed out or failed"
              exit 1
            else
              echo "ℹ️  No existing state found, nothing to destroy"
            fi
          else
            echo "ℹ️  No backend found, nothing to destroy"
          fi

      ### Step 5: Deploy ECR Repository
      - name: Terraform Init (ECR)
        working-directory: terraform/environments/beta/ecr
        run: terraform init -backend-config="key=beta/ecr/terraform.tfstate"

      - name: Terraform Apply (ECR)
        working-directory: terraform/environments/beta/ecr
        run: terraform apply -auto-approve

      ### Step 6: Extract ECR Repository URL
      - name: Extract ECR Repository URL
        id: ecr-output
        working-directory: terraform/environments/beta/ecr
        run: |
          echo "ECR_URL=$(terraform output -raw ecr_repository_url)" >> $GITHUB_ENV

      ### Step 7: Build and Push Docker Image
      - name: Authenticate Docker to ECR
        run: |
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ${{ env.ECR_URL }}

      - name: Build, Tag, and Push Docker Image
        run: |
          docker build -f src/dockerfile -t ${{ env.ECR_URL }}:latest src/
          docker push ${{ env.ECR_URL }}:latest

      ### Step 8: Wait for ECR Image
      - name: Wait for ECR Image
        run: |
          echo "Waiting for ECR image to be available..."
          max_attempts=30
          attempt=1
          
          while [ $attempt -le $max_attempts ]; do
            echo "Attempt $attempt/$max_attempts: Checking if ECR image is available..."
            
            if aws ecr describe-images --repository-name vse-app --image-ids imageTag=latest --region us-east-1 --query 'imageDetails[0].imageDigest' --output text 2>/dev/null | grep -q "sha256:"; then
              echo "✅ ECR image is now available"
              break
            fi
            
            if [ $attempt -eq $max_attempts ]; then
              echo "❌ ECR image did not become available after $max_attempts attempts"
              exit 1
            fi
            
            echo "Image not ready yet, waiting 10 seconds..."
            sleep 10
            attempt=$((attempt + 1))
          done

      ### Step 9: Deploy ECS Infrastructure
      - name: Terraform Init (ECS)
        working-directory: terraform/environments/beta/ecs
        run: terraform init -backend-config="key=beta/ecs/terraform.tfstate"

      - name: Terraform Plan (ECS)
        working-directory: terraform/environments/beta/ecs
        env:
          ECR_URL: ${{ env.ECR_URL }}
        run: terraform plan -var "ecr_repository_url=${{ env.ECR_URL }}" -out=tfplan

      - name: Terraform Apply (ECS)
        working-directory: terraform/environments/beta/ecs
        env:
          ECR_URL: ${{ env.ECR_URL }}
        run: terraform apply tfplan

      - name: Verify ECR Image and Force ECS Update
        run: |
          echo "🔍 Verifying ECR image exists..."
          
          # Check if the latest image exists in ECR
          if aws ecr describe-images --repository-name vse-app --image-ids imageTag=latest --region us-east-1 > /dev/null 2>&1; then
            echo "✅ ECR image 'latest' found"
          else
            echo "❌ ECR image 'latest' not found - this may cause deployment issues"
            echo "📋 Available images in ECR:"
            aws ecr describe-images --repository-name vse-app --region us-east-1 --query 'imageDetails[*].imageTags' || echo "No images found"
          fi
          
          echo "🔄 Forcing ECS service to use latest Docker image..."
          aws ecs update-service \
            --cluster vse-cluster \
            --service vse-service \
            --force-new-deployment \
            --region us-east-1
          echo "✅ ECS service update initiated"

      ### Step 10: Wait for ECS Service
      - name: Wait for ECS Service to be Stable
        run: |
          echo "Waiting for ECS service to be stable..."
          aws ecs wait services-stable --cluster vse-cluster --services vse-service --region us-east-1
          echo "ECS service is now stable"

      ### Step 11: Check Certificate Status
      - name: Check Certificate Status
        run: |
          echo "🔍 Checking ACM certificate status..."
          
          # List all certificates
          echo "📋 All ACM certificates:"
          aws acm list-certificates --region us-east-1 --output table
          
          # Check specific certificate for vseaudiobeta.com
          CERT_STATUS=$(aws acm list-certificates --region us-east-1 --query 'CertificateSummaryList[?DomainName==`vseaudiobeta.com`].Status' --output text 2>/dev/null)
          CERT_ARN=$(aws acm list-certificates --region us-east-1 --query 'CertificateSummaryList[?DomainName==`vseaudiobeta.com`].CertificateArn' --output text 2>/dev/null)
          
          echo "📋 Certificate status for vseaudiobeta.com: $CERT_STATUS"
          echo "📋 Certificate ARN: $CERT_ARN"
          
          if [ "$CERT_STATUS" = "PENDING_VALIDATION" ]; then
            echo "⏳ Certificate is pending validation"
            
            # Check Route 53 hosted zone
            ZONE_ID=$(aws route53 list-hosted-zones --query 'HostedZones[?Name==`vseaudiobeta.com.`].Id' --output text | cut -d'/' -f3 2>/dev/null)
            echo "📋 Route 53 Zone ID: $ZONE_ID"
            
            if [ "$ZONE_ID" != "None" ] && [ "$ZONE_ID" != "" ]; then
              echo "📋 DNS validation records in Route 53:"
              aws route53 list-resource-record-sets --hosted-zone-id $ZONE_ID --query 'ResourceRecordSets[?Type==`CNAME`]' --output table 2>/dev/null || echo "No CNAME records found"
              
              echo "📋 All records in hosted zone:"
              aws route53 list-resource-record-sets --hosted-zone-id $ZONE_ID --output table 2>/dev/null || echo "Could not list records"
            else
              echo "❌ No Route 53 hosted zone found for vseaudiobeta.com"
            fi
          elif [ "$CERT_STATUS" = "ISSUED" ]; then
            echo "✅ Certificate is issued and valid!"
          else
            echo "ℹ️  Certificate status: $CERT_STATUS"
          fi

      ### Step 12: Verify Deployment
      - name: Verify Deployment
        run: |
          echo "Verifying deployment..."
          
          # Check ECS service status
          SERVICE_STATUS=$(aws ecs describe-services --cluster vse-cluster --services vse-service --region us-east-1 --query 'services[0].status' --output text)
          echo "ECS Service Status: $SERVICE_STATUS"
          
          # Check NLB health
          NLB_ARN=$(aws elbv2 describe-load-balancers --names vse-nlb --region us-east-1 --query 'LoadBalancers[0].LoadBalancerArn' --output text)
          if [ "$NLB_ARN" != "None" ] && [ "$NLB_ARN" != "" ]; then
            echo "NLB ARN: $NLB_ARN"
            
            # Check target group health (HTTP target group)
            TG_ARN=$(aws elbv2 describe-target-groups --names vse-http-target-group --region us-east-1 --query 'TargetGroups[0].TargetGroupArn' --output text)
            if [ "$TG_ARN" != "None" ] && [ "$TG_ARN" != "" ]; then
              echo "Target Group ARN: $TG_ARN"
              
              # Wait a bit for targets to register
              sleep 30
              
              # Check target health
              TARGET_HEALTH=$(aws elbv2 describe-target-health --target-group-arn $TG_ARN --region us-east-1 --query 'TargetHealthDescriptions[0].TargetHealth.State' --output text)
              echo "Target Health: $TARGET_HEALTH"
            fi
          fi

      ### Step 13: Output Deployment Information
      - name: Output Deployment Info
        working-directory: terraform/environments/beta/ecs
        run: |
          echo "=== DEPLOYMENT SUMMARY ==="
          echo "ECS Cluster: vse-cluster"
          echo "ECS Service: vse-service"
          echo "NLB DNS Name: $(terraform output -raw alb_dns_name)"
          echo "Domain: https://vseaudiobeta.com"
          echo "ECR Repository: ${{ env.ECR_URL }}"
          echo ""
          echo "🔒 HTTPS CONFIGURATION (Let's Encrypt):"
          echo "✅ Traefik reverse proxy deployed"
          echo "✅ Let's Encrypt certificate resolver configured"
          echo "✅ Automatic HTTPS certificate generation"
          echo "✅ HTTP to HTTPS redirection enabled"
          echo ""
          echo "🔧 REQUIRED NEXT STEPS:"
          echo "1. Copy the nameservers shown above"
          echo "2. Update your domain registrar to use these nameservers"
          echo "3. Wait 5-60 minutes for DNS propagation"
          echo "4. Let's Encrypt will automatically issue certificates"
          echo "5. HTTPS will be available at https://vseaudiobeta.com"
          echo ""
          echo "📱 Current access: http://$(terraform output -raw alb_dns_name)"
          echo "📱 Traefik dashboard: $(terraform output -raw traefik_dashboard)"
          echo "📱 Final HTTPS access: https://vseaudiobeta.com (after DNS update)"
          echo "=========================="