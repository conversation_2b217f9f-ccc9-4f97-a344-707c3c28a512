







// NEEDLE HOME - for tablets included
$(document).ready(function () {
    $(".needle-dragger").each(function () {
        var container = $(this).closest('.needle-screen');
        var needle = container.data('needle');

        $(this).draggable({
            axis: "x",
            containment: container,
            drag: function (event, ui) {
                // Function to handle both mouse and touch events
                handleDrag($(this), ui, needle); // Pass needle variable to handleDrag function
            },
            touch: function (event, ui) {
                // Function to handle only touch events
                handleDrag($(this), ui, needle); // Pass needle variable to handleDrag function
            }
        });
    });

    // Function to handle both mouse and touch events
    function handleDrag(element, ui, needle) { // Receive needle as a parameter
        var containerWidth = element.closest('.needle-screen').width();
        var draggerWidth = element.width();
        var draggerPosition = ui.position.left;

        // Calculate the maximum position allowing the needle to go to 105% of the container's width
        var maxPosition = containerWidth * 1 - draggerWidth;

        var revealPercentage = (draggerPosition / maxPosition) * 100;
        // Round to 2 decimal places
        revealPercentage = Math.round(revealPercentage * 100) / 100;
        console.log("Reveal Percentage for needle " + needle + ": " + revealPercentage + "%");
    }
});



// Initialize all knobs on document load DETAILS
// // Initialize all knobs on document load
// document.addEventListener('DOMContentLoaded', initializeKnobs);
// Existing script logic (assumed to be provided earlier)
document.addEventListener('DOMContentLoaded', function () {
    var wheelKnobs = document.querySelectorAll('.wheel-knob');
    wheelKnobs.forEach(function (knob) {
        if (!knob.closest('.knob-nod-wrap') && !knob.closest('.wheel-xtec-stereo-3.wheel-xtec-stereo_full')) {
            var progressPath = knob.closest('.black-circle').querySelector('.progress-path');
            knob.lastAngle = -140; // Initialize the last angle

            function updateProgressPath(angle, progressPath) {
                var percentage = (angle + 140) / 280; // Map angle from -140deg to 140deg to 0 to 1
                var pathLength = 208.4; // Length of the arc for the progress path
                var offset = pathLength - (percentage * pathLength);
                progressPath.style.strokeDashoffset = offset;
            }

            function rotateKnob(event, knob, progressPath) {
                var rect = knob.getBoundingClientRect();
                var centerX = rect.left + rect.width / 2;
                var centerY = rect.top + rect.height / 2;

                var clientX = event.clientX || event.touches[0].clientX;
                var clientY = event.clientY || event.touches[0].clientY;

                var angle = Math.atan2(clientY - centerY, clientX - centerX) * (180 / Math.PI);

                if (angle < -140) {
                    angle = -140;
                } else if (angle > 140) {
                    angle = 140;
                }

                if (Math.abs(angle - knob.lastAngle) > 180) {
                    return;
                }

                knob.lastAngle = angle;

                knob.style.transform = 'rotate(' + angle + 'deg)';
                updateProgressPath(angle, progressPath);
            }

            function onPointerMove(event) {
                rotateKnob(event, knob, progressPath);
            }

            function onPointerUp() {
                document.removeEventListener('mousemove', onPointerMove);
                document.removeEventListener('mouseup', onPointerUp);
                document.removeEventListener('touchmove', onPointerMove);
                document.removeEventListener('touchend', onPointerUp);
            }

            knob.addEventListener('mousedown', function (event) {
                event.preventDefault();
                document.addEventListener('mousemove', onPointerMove);
                document.addEventListener('mouseup', onPointerUp);
            });

            knob.addEventListener('touchstart', function (event) {
                event.preventDefault();
                document.addEventListener('touchmove', onPointerMove);
                document.addEventListener('touchend', onPointerUp);
            });
        }
    });
});

// SCENE Animation

// bullets 1-8
$(document).ready(function () {
    $(".pinned").click(function () {
        // Toggle classes for background and box shadow
        $(this).toggleClass("pinned-active");
    });
});

// accordion
$(document).ready(function () {
    // Function to check if the button is active and has the required classes
    function isEditButtonActive() {
        return $(".editable").hasClass("active-btn-background") &&
            $(".editable").hasClass("active-btn-shadow");
    }

    // Function to hide the accordion if it's open
    function hideAccordionIfOpen() {
        if ($(".accordion-item-nav").hasClass("activate-edit")) {
            $(".accordion-item-nav").removeClass("activate-edit");
            $(".accordion-item-content").removeClass("activate-edit");
        }
    }

    // Event handler for clicking on accordion-item-nav
    $(".accordion-item-nav").click(function () {
        // Check if the button is active and has the required classes
        if (isEditButtonActive()) {
            // Remove activate-edit class from all navigation items and content elements
            $(".accordion-item-nav").removeClass("activate-edit");
            $(".accordion-item-content").removeClass("activate-edit");

            // Add activate-edit class to clicked navigation item and its associated content
            $(this).addClass("activate-edit");
            $(this).next('.accordion-item-content').addClass("activate-edit");
        }
    });

    // Event handler for clicking on the button to check if the accordion needs to be hidden
    $(".editable").click(function () {
        // Check if the button no longer has the required classes
        if (!isEditButtonActive()) {
            // Hide the accordion if it's open
            hideAccordionIfOpen();
        }
    });
});


// ROUTING select in tabs

$(document).ready(function () {
    // Function to handle clicks for any group
    function handleGroupClick(groupClass) {
        $(groupClass + " .scrolled").click(function () {
            var $clickedElement = $(this);
            var $groupElements = $(groupClass + " .scrolled");

            // Deactivate all elements within the group
            $groupElements.removeClass("scrolled-active");

            // Toggle active class for the clicked element
            $clickedElement.toggleClass("scrolled-active");
        });
    }

    // List of group classes
    var groups = [
        ".inputs-1-8",
        ".inputs-9-16",
        ".inputs-17-24",
        ".inputs-25-32",
        ".aux-1-6",
        ".outputs-1-8",
        ".outputs-9-16",
        ".outputs-17-24",
        ".outputs-25-32",
        ".outputs-33-40",
        ".outputs-41-48",
        ".outputs-b-1-8",
        ".outputs-b-9-16",
        ".outputs-b-17-24",
        ".outputs-b-25-32",
        ".outputs-b-33-40",
        ".outputs-b-41-48",
        ".preset-1-100",
        ".output-1-16",
        ".category-1-6",
        ".output-signal",
        ".tap-9",
        ".auxout-1-8",
        ".auxcat-1-6",
        ".aux-signal",
        ".auxtap-9",
        ".p-1-16",
        ".pcat-16",
        ".p16-signal",
        ".card-1-8",
        ".card-9-16",
        ".card-17-24",
        ".card-25-32",
        ".color-sets",
        ".instrument-sets",
        ".channel-sets",
        ".swapper-mode-1",
        ".swapper-type",
        ".swapper-mode-2",
        ".swapper-mode-3",
    ];

    // Apply the click handler to each group
    groups.forEach(function (groupClass) {
        handleGroupClick(groupClass);
    });
});

// SCENE popups
document.addEventListener('DOMContentLoaded', function () {
    const paramSafeButton = document.querySelector('.btn-block-scene.param-safe');
    const chainSafeButton = document.querySelector('.btn-block-scene.chain-safe');
    const paramSafeWrapper = document.querySelector('.param-safe-wrapper');
    const chainSafeWrapper = document.querySelector('.chain-safe-wrapper');

    // Only add event listener if elements exist
    if (paramSafeButton && paramSafeWrapper) {
        paramSafeButton.addEventListener('click', function () {
            paramSafeButton.classList.toggle('active-btn-background');
            paramSafeButton.classList.toggle('active-btn-shadow');
            paramSafeWrapper.classList.toggle('visible_block');

            if (!paramSafeButton.classList.contains('active-btn-background')) {
                paramSafeWrapper.classList.remove('visible_block');
            }

            if (chainSafeButton && chainSafeWrapper && 
                !paramSafeButton.classList.contains('active-btn-background') && 
                chainSafeButton.classList.contains('active-btn-background')) {
                chainSafeButton.classList.remove('active-btn-background', 'active-btn-shadow');
                chainSafeWrapper.classList.remove('visible_block');
            }
        });
    }

    // For chainSafe button - add similar check if needed
});

// MAIN HOME Screen popup
document.addEventListener('DOMContentLoaded', () => {
    const infoButton = document.getElementById('info');
    const popup = document.getElementById('popup-selection');
    const closeButton = document.getElementById('close-pop-btn');
    const mainLogoButton = document.getElementById('main-logo-btn');
    const arrowPopButton = document.getElementById('arrow-pop-btn');

    const classesToToggle = ['active-btn-background', 'active-btn-shadow'];

    // Unique variable for the popup-session-bar
    const popupSessionBar = document.querySelector('.popup-session-bar');

    function updateButtonClasses() {
        if (popup.classList.contains('show')) {
            infoButton.classList.add(...classesToToggle);
            arrowPopButton.style.display = 'none'; // Hide arrow button when popup is visible
        } else {
            infoButton.classList.remove(...classesToToggle);
            arrowPopButton.style.display = 'block'; // Show arrow button when popup is hidden
        }
    }

    function handleInfoButtonClick() {
        if (popup.classList.contains('show')) {
            // If popup is active, remove 'show' and 'activated'
            popup.classList.remove('show');
            popupSessionBar.classList.remove('activated');
        } else {
            // If popup is not active, toggle both 'show' and 'activated'
            popup.classList.add('show');
            popupSessionBar.classList.add('activated');
        }
        updateButtonClasses(); // Update button appearance
    }

    closeButton.addEventListener('click', () => {
        popup.classList.remove('show'); // Close only popup
        updateButtonClasses();
    });

    mainLogoButton.addEventListener('click', () => {
        popup.classList.toggle('show'); // Toggle popup
        updateButtonClasses();
    });

    infoButton.addEventListener('click', handleInfoButtonClick);

    // Add functionality for arrow button
    arrowPopButton.addEventListener('click', () => {
        popup.classList.add('show'); // Show the popup
        popupSessionBar.classList.add('activated'); // Ensure the bar is activated
        updateButtonClasses(); // Update button appearance
    });

    // Initial state check to ensure arrow button visibility is correct
    updateButtonClasses();
});


// toggle modes on circle switch
// function toggleTheme(event) {
//     const targetElement = event.currentTarget;
//     targetElement.classList.toggle('dark-mode');
//     const circleElement = targetElement.querySelector('.circle');
//     if (circleElement) {
//         circleElement.classList.toggle('dark-mode');
//     }
// }

// document.querySelectorAll('.toggle-modes').forEach(element => {
//     element.addEventListener('click', toggleTheme);
// });

// switcher + red button toggle
function toggleTheme(event) {
    const targetElement = event.currentTarget;
    const parentBox = targetElement.closest('.boxed.double-box');

    if (parentBox) {
        // If a common parent is found, toggle classes on both elements inside that parent
        const redButton = parentBox.querySelector('.red-btn-in');
        const toggleButton = parentBox.querySelector('.toggle-modes');

        if (redButton) {
            redButton.classList.toggle('red_active');
        }
        if (toggleButton) {
            toggleButton.classList.toggle('dark-mode');
            const circleElement = toggleButton.querySelector('.circle');
            if (circleElement) {
                circleElement.classList.toggle('dark-mode');
            }
        }
    } else {
        // If no common parent, toggle individually
        targetElement.classList.toggle('dark-mode');
        const circleElement = targetElement.querySelector('.circle');
        if (circleElement) {
            circleElement.classList.toggle('dark-mode');
        }
    }
}

// Attach event listeners to all .toggle-modes elements
document.querySelectorAll('.toggle-modes').forEach(element => {
    element.addEventListener('click', toggleTheme);
});

// Attach event listeners to all .red-btn-in elements
document.querySelectorAll('.red-btn-in').forEach(redButton => {
    redButton.addEventListener('click', function (event) {
        const parentBox = redButton.closest('.boxed.double-box');

        if (parentBox) {
            // If a common parent is found, toggle classes on both elements inside that parent
            const toggleButton = parentBox.querySelector('.toggle-modes');

            if (redButton) {
                redButton.classList.toggle('red_active');
            }
            if (toggleButton) {
                toggleButton.classList.toggle('dark-mode');
                const circleElement = toggleButton.querySelector('.circle');
                if (circleElement) {
                    circleElement.classList.toggle('dark-mode');
                }
            }
        } else {
            // If no common parent, toggle individually
            this.classList.toggle('red_active');
        }
    });
});
// HOME  DCA1-8 EDIT
document.addEventListener('DOMContentLoaded', () => {
    const dcas = [
        { toggleClass: 'toggle-dca1', popupId: 'dca1-pop', closeBtnId: 'close-dca1-btn' },
        { toggleClass: 'toggle-dca2', popupId: 'dca2-pop', closeBtnId: 'close-dca2-btn' },
        { toggleClass: 'toggle-dca3', popupId: 'dca3-pop', closeBtnId: 'close-dca3-btn' },
        { toggleClass: 'toggle-dca4', popupId: 'dca4-pop', closeBtnId: 'close-dca4-btn' },
        { toggleClass: 'toggle-dca5', popupId: 'dca5-pop', closeBtnId: 'close-dca5-btn' },
        { toggleClass: 'toggle-dca6', popupId: 'dca6-pop', closeBtnId: 'close-dca6-btn' },
        { toggleClass: 'toggle-dca7', popupId: 'dca7-pop', closeBtnId: 'close-dca7-btn' },
        { toggleClass: 'toggle-dca8', popupId: 'dca8-pop', closeBtnId: 'close-dca8-btn' },
    ];

    dcas.forEach(dca => {
        const editDca = document.querySelector(`.${dca.toggleClass}`);
        const popup = document.getElementById(dca.popupId);
        const closeButton = document.getElementById(dca.closeBtnId);

        // Only add event listeners if all elements exist
        if (editDca && popup) {
            if (closeButton) {
                closeButton.addEventListener('click', () => {
                    popup.classList.remove('visible');
                    const classesToRemove = ['active-btn-background', 'active-btn-shadow'];
                    classesToRemove.forEach(cls => editDca.classList.remove(cls));
                });
            }

            editDca.addEventListener('click', () => {
                popup.classList.toggle('visible');
            });
        }
    });
});



// DETAILS DROPDOWN Animation

document.addEventListener('click', function (event) {
    // Handle dropdown toggle
    const dropdownDetails = document.querySelectorAll('.dropdown-detail');
    dropdownDetails.forEach(detail => {
        const dropdownArrow = detail.nextElementSibling;
        const dropdownMenu = dropdownArrow ? dropdownArrow.nextElementSibling : null;

        if (detail.contains(event.target)) {
            if (dropdownMenu && dropdownArrow) {
                const isOpen = dropdownMenu.style.display === 'block';
                document.querySelectorAll('.dropdown-menu').forEach(menu => menu.style.display = 'none');
                document.querySelectorAll('.dropdown-arrow').forEach(arrow => arrow.style.display = 'none');

                dropdownMenu.style.display = isOpen ? 'none' : 'block';
                dropdownArrow.style.display = isOpen ? 'none' : 'block';
            }
        } else if (dropdownMenu && !dropdownMenu.contains(event.target)) {
            dropdownMenu.style.display = 'none';
            dropdownArrow.style.display = 'none';
        }
    });

    // Handle dropdown option selection
    if (event.target.classList.contains('dropdown-option')) {
        const selectedOption = event.target.textContent;
        const dropdownDetail = event.target.closest('.source-detail, .gate-detail, .key-source-item, .button-drop_wrapper, .fx1-insert-l-wrap, .fx1-insert-r-wrap, .fx2-insert-wrap, .fx2-insert-r-wrap, .fx3-insert-r-wrap, .fx3-insert-wrap, .fx4-insert-r-wrap, .fx4-insert-wrap, .fx5-insert-r-wrap, .fx5-insert-wrap, .fx6-insert-r-wrap, .fx6-insert-wrap, .fx7-insert-r-wrap, .fx7-insert-wrap, .fx8-insert-r-wrap, .fx8-insert-wrap, .wrap_xover_drop').querySelector('.dropdown-detail');
        if (dropdownDetail) {
            dropdownDetail.textContent = selectedOption;
            const dropdownArrow = dropdownDetail.nextElementSibling;
            const dropdownMenu = dropdownArrow ? dropdownArrow.nextElementSibling : null;
            if (dropdownArrow) dropdownArrow.style.display = 'none'; // Hide arrow
            if (dropdownMenu) dropdownMenu.style.display = 'none'; // Hide menu
        }
    }
});

// DETAILS swap animation
document.addEventListener('DOMContentLoaded', function () {
    const swapButton = document.getElementById('swap');
    const swap1 = document.querySelector('.swap-1');
    const swap2 = document.querySelector('.swap-2');

    // Show the first image by default
    swap1.style.display = 'block';
    swap2.style.display = 'none';

    swapButton.addEventListener('click', function () {
        if (swap1.style.display === 'block') {
            swap1.style.display = 'none';
            swap2.style.display = 'block';
        } else {
            swap1.style.display = 'block';
            swap2.style.display = 'none';
        }
    });
});


// NEW KNOB


// Encapsulate the new knob functionality within a function to avoid conflicts

(function () {
    var lastAngle = -220; // Initialize the last angle

    // Function to handle rotation based on pointer movement
    function rotateNewKnob(event, knobWrap) {
        var rect = knobWrap.getBoundingClientRect();
        var centerX = rect.left + rect.width / 2;
        var centerY = rect.top + rect.height / 2;

        var clientX = event.clientX || event.touches[0].clientX;
        var clientY = event.clientY || event.touches[0].clientY;

        var angle = Math.atan2(clientY - centerY, clientX - centerX) * (180 / Math.PI);

        if (angle < -230) {
            angle = -230;
        } else if (angle > 50) {
            angle = 50;
        }

        // Prevent skipping between 0 and -180 by checking if the angle crosses the defined points
        if ((lastAngle <= -110 && angle >= -33) || (lastAngle >= -33 && angle <= -110)) {
            return;
        }

        lastAngle = angle;

        // Get specific snapping points from the knob
        var knob = knobWrap.querySelector('.wheel-knob');
        var snapPoints = knob.dataset.snappingPoints ? knob.dataset.snappingPoints.split(',').map(Number) : [-180, -148, -110, -68, -33, 0];
        knobWrap.style.transform = 'rotate(' + snapToClosestPoint(angle, snapPoints) + 'deg)';
    }

    // Function to snap the angle to the closest predefined point
    function snapToClosestPoint(angle, points) {
        var closestPoint = points.reduce(function (prev, curr) {
            return (Math.abs(curr - angle) < Math.abs(prev - angle) ? curr : prev);
        });
        return closestPoint;
    }

    // Function to initialize the new wheel knobs
    function initializeNewKnobs() {
        var knobWraps = document.querySelectorAll('.knob-nod-wrap:not(.wheel-xtec-stereo-3)');
        knobWraps.forEach(function (knobWrap) {
            var knob = knobWrap.querySelector('.wheel-knob');

            function onPointerMove(event) {
                rotateNewKnob(event, knobWrap);
            }

            function onPointerUp() {
                document.removeEventListener('mousemove', onPointerMove);
                document.removeEventListener('mouseup', onPointerUp);
                document.removeEventListener('touchmove', onPointerMove);
                document.removeEventListener('touchend', onPointerUp);
            }

            knob.addEventListener('mousedown', function (event) {
                event.preventDefault();
                document.addEventListener('mousemove', onPointerMove);
                document.addEventListener('mouseup', onPointerUp);
            });

            knob.addEventListener('touchstart', function (event) {
                event.preventDefault();
                document.addEventListener('touchmove', onPointerMove);
                document.addEventListener('touchend', onPointerUp);
            });
        });
    }

    // Ensure the new knob initialization function runs after the document is loaded
    document.addEventListener('DOMContentLoaded', initializeNewKnobs);
})();



// ne knob test
(function () {
    // Initialize the last angle for the specific knob
    var lastAngle_XtecStereo3 = -220;

    // Function to handle rotation based on pointer movement for each specific knob
    function rotateKnob_XtecStereo3(event, knobWrap) {
        var rect = knobWrap.getBoundingClientRect();
        var centerX = rect.left + rect.width / 2;
        var centerY = rect.top + rect.height / 2;

        var clientX = event.clientX || (event.touches && event.touches[0].clientX);
        var clientY = event.clientY || (event.touches && event.touches[0].clientY);

        if (clientX === undefined || clientY === undefined) {
            return; // Early return if client coordinates are not available
        }

        var angle = Math.atan2(clientY - centerY, clientX - centerX) * (180 / Math.PI);

        // Clamp the angle to the allowed range
        if (angle < -220) {
            angle = -220;
        } else if (angle > 90) {
            angle = 90;
        }

        // Define snapping points directly for this knob
        var snapPoints_XtecStereo3 = [-185, -168, -143, -112, -80, -42, -5, 30, 52, 75, 90];

        // Prevent jumping from low range to high range and vice versa
        // This time, we prevent large jumps between distant angles by comparing the absolute difference
        if (Math.abs(lastAngle_XtecStereo3 - angle) > 90) {
            console.log(`Jump prevented: lastAngle_XtecStereo3 = ${lastAngle_XtecStereo3}, angle = ${angle}`);
            return;
        }

        // Update lastAngle_XtecStereo3
        lastAngle_XtecStereo3 = angle;

        // Snap to the closest predefined point
        var snappedAngle = snapToClosestPoint_XtecStereo3(angle, snapPoints_XtecStereo3);
        console.log(`Setting transform to snapped angle: ${snappedAngle}`);

        // Rotate the specific knob (wheel-knob) within knobWrap
        knobWrap.style.transform = 'rotate(' + snappedAngle + 'deg)';
    }

    // Function to snap the angle to the closest predefined point
    function snapToClosestPoint_XtecStereo3(angle, points) {
        var closestPoint = points.reduce(function (prev, curr) {
            return (Math.abs(curr - angle) < Math.abs(prev - angle) ? curr : prev);
        });
        console.log(`Snapping ${angle} to closest point: ${closestPoint}`);
        return closestPoint;
    }

    // Function to initialize the specific knobs
    function initializeKnobs_XtecStereo3() {
        // Select all knobs with the specified classes
        var knobWraps = document.querySelectorAll('.wheel-xtec-stereo-3.wheel-xtec-stereo_full');
        if (!knobWraps.length) return; // Early return if no knobs are found

        knobWraps.forEach(function (knobWrap) {
            // Select the knob to interact with
            var knob = knobWrap.querySelector('.wheel-knob');
            if (!knob) return;

            function onPointerMove(event) {
                rotateKnob_XtecStereo3(event, knobWrap);
            }

            function onPointerUp() {
                document.removeEventListener('mousemove', onPointerMove);
                document.removeEventListener('mouseup', onPointerUp);
                document.removeEventListener('touchmove', onPointerMove);
                document.removeEventListener('touchend', onPointerUp);
            }

            knob.addEventListener('mousedown', function (event) {
                event.preventDefault();
                document.addEventListener('mousemove', onPointerMove);
                document.addEventListener('mouseup', onPointerUp);
            });

            knob.addEventListener('touchstart', function (event) {
                event.preventDefault();
                document.addEventListener('touchmove', onPointerMove, { passive: false });
                document.addEventListener('touchend', onPointerUp);
            });
        });
    }

    // Run the initialization function for the specific knobs after the document is loaded
    document.addEventListener('DOMContentLoaded', initializeKnobs_XtecStereo3);
})();

// DSS screen 
document.addEventListener('DOMContentLoaded', function () {
    // Function to handle knob rotation and button class toggling
    function handleKnobRotation(knobClass, buttonClass, defaultRotation) {
        const knobs = document.querySelectorAll(knobClass);

        knobs.forEach(knob => {
            const parentContainer = knob.closest('.vse-limit-col');
            const button = parentContainer.querySelector(buttonClass);

            // Function to update the button based on knob rotation
            function updateButtonBasedOnRotation() {
                // Extract the current rotation angle from the knob's transform style
                const rotation = parseFloat(knob.style.transform.replace(/[^-?\d.]/g, '')) || defaultRotation;

                // Toggle classes based on rotation
                if (rotation < defaultRotation - 0.01 || rotation > defaultRotation + 0.01) {
                    button.classList.add('active-btn-background', 'active-btn-shadow');
                } else {
                    button.classList.remove('active-btn-background', 'active-btn-shadow');
                }
            }

            // Initial check
            updateButtonBasedOnRotation();

            // Monitor changes in the knob's transform attribute
            const observer = new MutationObserver(updateButtonBasedOnRotation);
            observer.observe(knob, { attributes: true, attributeFilter: ['style'] });
        });
    }

    // Initialize the knob controls for both low and high band knobs using classes
    handleKnobRotation('.dss_low_knob', '.lo_btn', -140);
    handleKnobRotation('.dss_hi_knob', '.hi_btn', -140);
});

// screen 7 - vintage
document.addEventListener('DOMContentLoaded', function () {
    // Get all vintage_btns containers
    const vintageContainers = document.querySelectorAll('.vintage_btns');

    vintageContainers.forEach(container => {
        const frontVint = container.querySelector('.front_vint .btn_vint');
        const rearVint = container.querySelector('.rear_vint .btn_vint');

        // Function to toggle active_light class
        function toggleActiveLight(clickedButton, otherButton) {
            if (!clickedButton.classList.contains('active_light')) {
                clickedButton.classList.add('active_light');
                otherButton.classList.remove('active_light');
            }
            // If the clicked button is already active, do nothing
        }

        // Event listeners for the front and rear buttons
        frontVint.parentElement.addEventListener('click', function () {
            toggleActiveLight(frontVint, rearVint);
        });

        rearVint.parentElement.addEventListener('click', function () {
            toggleActiveLight(rearVint, frontVint);
        });
    });
});
document.addEventListener('DOMContentLoaded', function () {
    // Select all vintage_wrap elements with the vintage_btn class
    const vintageButtons = document.querySelectorAll('.vintage_wrap.vintage_btn');

    vintageButtons.forEach(buttonWrap => {
        const btnVint = buttonWrap.querySelector('.btn_vint');

        // Event listener to toggle active_light class
        buttonWrap.addEventListener('click', function () {
            btnVint.classList.toggle('active_light');
        });
    });
});





// end screen 7 vintage

// modulation screen buttons
// Handle the .dly_select buttons within each .modulation_screen
document.querySelectorAll('.modulation_screen').forEach(screen => {
    screen.querySelectorAll('.upper_btn_selector .dly_select').forEach(button => {
        button.addEventListener('click', function () {
            // Check if the clicked button already has the active class
            if (!this.classList.contains('active_module_btn')) {
                // Remove the active class from all buttons within the same .modulation_screen
                screen.querySelectorAll('.upper_btn_selector .dly_select').forEach(btn => {
                    btn.classList.remove('active_module_btn');
                });

                // Add the active class to the clicked button
                this.classList.add('active_module_btn');
            }
        });
    });
});

// Handle the .ser_select button within each .modulation_screen
document.querySelectorAll('.modulation_screen').forEach(screen => {
    const serSelectBtn = screen.querySelector('.ser_select.ser_btn');
    if (serSelectBtn) {
        serSelectBtn.addEventListener('click', function () {
            this.classList.toggle('active_module_btn');
        });
    }
});

// Handle the .third_select buttons within each .modulation_screen
document.querySelectorAll('.modulation_screen').forEach(screen => {
    screen.querySelectorAll('.one_btn_option .third_select').forEach(button => {
        button.addEventListener('click', function () {
            // Check if the clicked button already has the active class
            if (!this.classList.contains('active_module_btn')) {
                // Remove the active class from all buttons within the same .modulation_screen
                screen.querySelectorAll('.one_btn_option .third_select').forEach(btn => {
                    btn.classList.remove('active_module_btn');
                });

                // Add the active class to the clicked button
                this.classList.add('active_module_btn');
            }
        });
    });
});





// video and share screen popups
// Select all existing and new elements
const previewWindows = document.querySelectorAll('.preview-window');
const popupSessionBarVideo = document.getElementById('popup-session-bar-video');
const popupSession = document.getElementById('popup-session');
const closeButtonVideo = document.getElementById('x-session-start'); // Close button for video
const closeButtonScreen = document.getElementById('x-session-start-2'); // Close button for screen-share
const profileWindowButton = document.getElementById('profile-window');
const closePopBtn = document.getElementById('close-pop-btn');
const screenShareButton = document.getElementById('screen-share');
const popupShare = document.getElementById('popup-share');
const appWindow = document.getElementById('app-window'); // App window element
const screenButton = document.getElementById('screen'); // New screen button
const videoButton = document.getElementById('video'); // New video button
const videoWindow = document.getElementById('video-window'); // Video window
const screenShareWindow = document.getElementById('screen-share'); // Screen-share window

const webcamSelector = document.createElement('select'); // Dropdown for webcam selection
const qualitySelector = document.createElement('select'); // Dropdown for quality selection



webcamSelector.id = 'webcam-selector';
qualitySelector.id = 'quality-selector';

// Function to toggle the 'activated' class on #popup-session-bar-video
function togglePopupBar() {
    popupSessionBarVideo.classList.toggle('activated');
}

// Function to manage 'selected', 'activated', and 'active-session' states
function selectWindow(clickedWindow) {
    previewWindows.forEach(window => {
        // Remove 'selected' from all except the clicked one
        if (window !== clickedWindow) {
            window.classList.remove('selected');
        }
    });
    // Add 'selected' class to the clicked window
    clickedWindow.classList.add('selected');

    // Check if the clicked window is #video-window
    if (clickedWindow.id === 'video-window' && popupSessionBarVideo.classList.contains('activated')) {
        openPopup(popupSession);
    }

    // Close all popups if #app-window is selected
    if (clickedWindow.id === 'app-window') {
        closeAllPopups();
    }
}

// Function to close all popups
function closeAllPopups() {
    document.querySelectorAll('.popup-session').forEach(popup => {
        popup.classList.remove('active-session');
    });
}

// Function to open a specific popup
function openPopup(popupElement) {
    closeAllPopups(); // Close any other open popups
    popupElement.classList.add('active-session'); // Open the specified popup
}



// Function to toggle button active classes
function toggleButton(button, isActive) {
        if (isActive) {
            button.classList.add('active-btn-background', 'active-btn-shadow');
        } else {
            button.classList.remove('active-btn-background', 'active-btn-shadow');
        }

}

// Function to handle button clicks and syncing
function handleButtonClick(button, windowElement) {
    // Toggle the selected class for the corresponding window
    const isAlreadySelected = windowElement.classList.contains('selected');
    selectWindow(windowElement);

    // Sync the button active classes
    toggleButton(button, !isAlreadySelected);
}

// Add click event listeners to each preview window
previewWindows.forEach(window => {
    window.addEventListener('click', function () {
        if (!this.classList.contains('selected')) {
            selectWindow(this);

            // Ensure syncing for new buttons
            toggleButton(screenButton, screenShareWindow.classList.contains('selected'));
            toggleButton(videoButton, videoWindow.classList.contains('selected'));
        }
    });
});


async function handleScreenShare(button, windowElement) {
    handleButtonClick(button, windowElement);

    // Open the screen-share popup
    if (windowElement.classList.contains('selected')) {
        openPopup(popupShare);

        // Check if getDisplayMedia is supported
        if (navigator.mediaDevices && navigator.mediaDevices.getDisplayMedia) {
            try {
                const stream = await navigator.mediaDevices.getDisplayMedia({
                    video: true, // Capture video (screen)
                    audio: false, // Optional: Capture audio
                });
            
                // Helper function to create and style video elements
                function createVideoElement(stream) {
                    const videoElement = document.createElement('video');
                    videoElement.srcObject = stream;
                    videoElement.autoplay = true;
                    videoElement.style.width = '100%';
                    videoElement.style.height = '100%';
                    videoElement.style.borderRadius = '8px';
                    return videoElement;
                }
            
                // Add the video element to the popup
                popupShare.innerHTML = ''; // Clear existing content
                popupShare.appendChild(createVideoElement(stream));
            
                // Add a separate video element to the windowElement
                windowElement.innerHTML = ''; // Clear existing content
                windowElement.appendChild(createVideoElement(stream.clone())); // Clone the media stream
            
                // Handle when screen sharing is stopped
                stream.getVideoTracks()[0].addEventListener('ended', () => {
                    console.log('Screen sharing stopped.');
                    popupShare.classList.remove('selected');
                    windowElement.classList.remove('selected');
                    closeAllPopups(); // Close the popup
                });

                console.log('Screen sharing started.');
            } catch (err) {
                console.error('Error starting screen sharing:', err);
            }
        } else {
            console.error('Screen sharing is not supported in this browser.');
            alert('Screen sharing is not supported in this browser. Please try a modern browser like Chrome or Edge.');
        }
    } else {
        closeAllPopups();
    }
}

screenButton.addEventListener('click', async () => {
    handleScreenShare(screenButton, screenShareWindow)
});



// Predefined quality settings
const qualitySettings = {
    high: { width: 1280, height: 720, frameRate: 30 }, // High quality
    medium: { width: 640, height: 480, frameRate: 15 }, // Medium quality
    low: { width: 320, height: 240, frameRate: 10 }, // Low quality
};

// Populate the quality selector dropdown
function populateQualitySelector() {
    qualitySelector.innerHTML = ''; // Clear existing options

    for (const [quality, settings] of Object.entries(qualitySettings)) {
        const option = document.createElement('option');
        option.value = quality;
        option.textContent = quality.charAt(0).toUpperCase() + quality.slice(1);
        qualitySelector.appendChild(option);
    }

    // Append the quality selector to the popupSession
    popupSession.appendChild(qualitySelector);
}

// Helper function to create and style video elements
function createVideoElement(stream) {
    const videoElement = document.createElement('video');
    videoElement.srcObject = stream;
    videoElement.autoplay = true;
    videoElement.style.width = '100%';
    videoElement.style.height = '100%';
    videoElement.style.borderRadius = '8px';
    return videoElement;
}

// Populate the webcam selector dropdown
async function populateWebcamSelector() {
    try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        // Clear existing options
        webcamSelector.innerHTML = '';

        // Add an option for each webcam
        videoDevices.forEach((device, index) => {
            const option = document.createElement('option');
            option.value = device.deviceId;
            option.textContent = device.label || `Camera ${index + 1}`;
            webcamSelector.appendChild(option);
        });

        // Append the selector to the popupSession
        popupSession.appendChild(webcamSelector);
    } catch (error) {
        console.error('Error fetching video devices:', error);
    }
}

// Function to start webcam streaming with the selected device and quality
async function startWebcam(deviceId, quality) {
    try {
        const constraints = {
            video: {
                deviceId: deviceId ? { exact: deviceId } : undefined,
                width: qualitySettings[quality].width,
                height: qualitySettings[quality].height,
                frameRate: qualitySettings[quality].frameRate,
            },
            audio: false, // Optional: Add audio constraints if needed
        };

        const stream = await navigator.mediaDevices.getUserMedia(constraints);

        // Add the video element to the popupSession
        popupSession.innerHTML = ''; // Clear existing content
        popupSession.appendChild(webcamSelector); // Add webcam selector back
        popupSession.appendChild(qualitySelector); // Add quality selector back
        popupSession.appendChild(createVideoElement(stream));

        // Add a separate video element to the videoWindow
        videoWindow.innerHTML = ''; // Clear existing content
        videoWindow.appendChild(createVideoElement(stream.clone())); // Clone the media stream

        // Handle when the webcam stream is stopped
        stream.getVideoTracks()[0].addEventListener('ended', () => {
            console.log('Webcam streaming stopped.');
            popupSession.classList.remove('selected');
            videoWindow.classList.remove('selected');
            closeAllPopups(); // Close the popup
        });
    } catch (error) {
        console.error('Error accessing webcam:', error);
        alert('Unable to access the webcam. Please check your permissions.');
    }
}

// Add click event listener to the video button
videoButton.addEventListener('click', async () => {
    handleButtonClick(videoButton, videoWindow);

    // Populate selectors when the popup opens
    if (videoWindow.classList.contains('selected')) {
        await populateWebcamSelector();
        populateQualitySelector();
        openPopup(popupSession);

        // Start webcam streaming with the selected device and quality
        const selectedQuality = qualitySelector.value || 'high'; // Default to high quality
        const selectedDevice = webcamSelector.value;

        // Start with initial selections
        startWebcam(selectedDevice, selectedQuality);

        // Update stream when webcam selection changes
        webcamSelector.addEventListener('change', () => {
            startWebcam(webcamSelector.value, qualitySelector.value || 'high');
        });

        // Update stream when quality selection changes
        qualitySelector.addEventListener('change', () => {
            startWebcam(webcamSelector.value, qualitySelector.value || 'high');
        });
    } else {
        closeAllPopups();
    }
});
 
// Add click event listener to the screen-share close button
closeButtonScreen.addEventListener('click', () => {
    closeAllPopups();

    // Remove active classes from screen button
    toggleButton(screenButton, false);

    // Remove 'selected' class from screen-share window
    screenShareWindow.classList.remove('selected');
});

// Add click event listener to the video close button
closeButtonVideo.addEventListener('click', () => {
    closeAllPopups();

    // Remove active classes from video button
    toggleButton(videoButton, false);

    // Remove 'selected' class from video window
    videoWindow.classList.remove('selected');
});

// Add click event listener to the screen-share button
screenShareButton.addEventListener('click', function () {
    selectWindow(this);
    openPopup(popupShare);
});

// chat script
document.addEventListener("DOMContentLoaded", () => {
    const chatButton = document.getElementById("chat");
    const chatWindow = document.getElementById("chat-window");

    function toggleChatClasses() {
        chatButton.classList.toggle("active-btn-background");
        chatButton.classList.toggle("active-btn-shadow");
    }

    chatButton.addEventListener("click", toggleChatClasses);
    chatWindow.addEventListener("click", toggleChatClasses);
});



// ACTIVE SESSION BOX
// Select all session buttons
const sessionButtons = document.querySelectorAll('.session_flex .btn_orange_full');
const sessionTitle = document.querySelector(".session-title"); // Adjust selector if needed
// Function to toggle the .active_session class
function toggleActiveSession(clickedButton) {
    sessionButtons.forEach(button => {
        // Remove .active_session from all buttons
        button.classList.remove('active_session');
    });
    // Add .active_session to the clicked button
    clickedButton.classList.add('active_session');
}

// Add event listeners to session buttons (excluding Create Session button)
sessionButtons.forEach((button) => {
    button.addEventListener("click", function () {
        // Skip if this is the Create Session button
        if (this.id === 'create-session-btn' || this.classList.contains('create-session')) {
            return; // Let the create session handler take care of this
        }
        
        // Toggle the active sessionz
        toggleActiveSession(this);

        // Add the 'active-session' class to the popup
        document
            .querySelector(".popup-session-choice")
            .classList.add("active-session");

        // Update the session title based on the clicked button
        const titleText = this.textContent.trim(); // Get the text content of the button
        sessionTitle.textContent = titleText; // Set it as the session title
    });
});

// Close functionality for the popup (optional, to hide popup)
document.querySelectorAll(".x-session-start").forEach((element) => {
    element.addEventListener("click", () => {
        document
            .querySelector(".popup-session-choice")
            .classList.remove("active-session");
    });
});


const verificationForm = document.getElementById("verification-form");
const codeInputs = document.querySelectorAll(".code-input");

// Add event listeners to handle input behavior
codeInputs.forEach((input, index) => {
    input.addEventListener("input", (event) => {
        const value = event.target.value;

        // Ensure only digits are entered
        if (!/^\d$/.test(value)) {
            event.target.value = ""; // Clear non-digit input
            return;
        }

        // Automatically move to the next input
        if (index < codeInputs.length - 1) {
            codeInputs[index + 1].focus();
        }
    });

    input.addEventListener("keydown", (event) => {
        // Handle backspace to move to the previous input
        if (event.key === "Backspace" && input.value === "") {
            if (index > 0) {
                codeInputs[index - 1].focus();
            }
        }
    });
});

verificationForm.addEventListener("submit", function (event) {
    event.preventDefault();

    // Combine the values of all code inputs
    const sessionKey = Array.from(codeInputs)
        .map((input) => input.value)
        .join("");

    // Ensure the session key is exactly 6 digits
    if (sessionKey.length !== 6) {
        alert("Please enter a valid 6-digit session key.");
        return;
    }

    // Validate session with backend
    fetch("/join-session", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ session_key: sessionKey })
    })
    .then(response => response.json())
    .then(data => {
        if (data.error) {
            alert("Error: " + data.error);
        } else {
            alert("Joined session: " + data.session_key);
            localStorage.setItem("session_key", data.session_key);
            localStorage.setItem("user_id", data.user_id);

            // Close the popup
            document.querySelector(".popup-session-choice").classList.remove("active-session");

            // Reset the form inputs
            codeInputs.forEach(input => input.value = "");
            codeInputs[0].focus();
        }
    })
    .catch(error => {
        console.error("Error joining session:", error);
        alert("Failed to connect to session.");
    });
});

// end session key

// dropdown video/audio
function initializeVolumeControls() {
    // Buttons
    const volMuteButton = document.getElementById('vol-mute');
    const volMinButton = document.getElementById('vol-min');
    const volPlusButton = document.getElementById('vol-plus');

    // Check if the old volume control elements exist, if not, skip this initialization
    if (!volMuteButton || !volMinButton || !volPlusButton) {
        console.log('Old volume control elements not found, skipping old volume control initialization');
        return;
    }

    // Track volume percentage (0-100%)
    let volumePercentage = 0; // Start at 0%
    let previousVolume = 50; // Remember the last volume percentage before muting

    // Determine the correct path for images based on URL
    let imagePathPrefix = '';
    if (window.location.pathname.includes('/detail/')) {
        imagePathPrefix = '../';
    } else if (window.location.pathname.includes('/detail-light/')) {
        imagePathPrefix = '../../';
    }

    // Create dynamic volume display element
    const volumeDisplay = document.createElement('div');
    volumeDisplay.classList.add('volume-display'); // Add main class for styling
    volumeDisplay.style.display = 'none'; // Initially hidden

    // Mute icon
    const volumeIconMute = document.createElement('img');
    volumeIconMute.src = `${imagePathPrefix}assets/icons-popups/mute-vol.svg`;
    volumeIconMute.alt = 'vol';
    volumeIconMute.classList.add('volume-icon');

    // Progress container and bar
    const progressContainer = document.createElement('div');
    progressContainer.classList.add('progress-container'); // Add class for the container

    const progressBar = document.createElement('div');
    progressBar.classList.add('progress-bar'); // Add class for the progress bar
    progressBar.style.width = `${volumePercentage}%`;
    progressContainer.appendChild(progressBar);

    // Plus icon
    const volumeIconPlus = document.createElement('img');
    volumeIconPlus.src = `${imagePathPrefix}assets/icons-popups/plus-vol.svg`;
    volumeIconPlus.alt = 'vol';
    volumeIconPlus.classList.add('volume-icon');

    // Percentage text
    const percentageText = document.createElement('div');
    percentageText.classList.add('percentage-text'); // Add class for the percentage text
    percentageText.textContent = 'Mute';

    volumeDisplay.appendChild(volumeIconMute);
    volumeDisplay.appendChild(progressContainer);
    volumeDisplay.appendChild(volumeIconPlus);
    volumeDisplay.appendChild(percentageText);
    document.body.appendChild(volumeDisplay);

    // Add background music
    const audioElement = new Audio(`${imagePathPrefix}assets/music/background.mp3`);
    audioElement.loop = true; // Loop the background music
    audioElement.volume = volumePercentage / 100; // Set initial volume
    audioElement.play(); // Start playing music

    // Function to map volume percentage to position (50px to 330px)
    function mapPercentageToPosition(percentage) {
        return 50 + (percentage / 100) * (330 - 50);
    }

    // Function to update percentage text position and value
    function updatePercentagePosition() {
        const mappedLeft = mapPercentageToPosition(volumePercentage);
        percentageText.style.left = `${mappedLeft}px`; // Dynamically position the text
        percentageText.textContent = volumePercentage === 0 ? 'Mute' : `${volumePercentage}%`; // Update text content
    }

    // Function to show volume display for 3 seconds
    function showVolumeDisplay() {
        volumeDisplay.style.display = 'flex';
        clearTimeout(volumeDisplay.timeout);
        volumeDisplay.timeout = setTimeout(() => {
            volumeDisplay.style.display = 'none';
        }, 3000);
    }

    // Handle mute button
    volMuteButton.addEventListener('click', () => {
        if (volumePercentage > 0) {
            previousVolume = volumePercentage; // Remember current volume
            volumePercentage = 0;
        } else {
            volumePercentage = previousVolume; // Restore previous volume
        }
        progressBar.style.width = `${volumePercentage}%`;
        audioElement.volume = volumePercentage / 100; // Update audio volume
        updatePercentagePosition();
        showVolumeDisplay();
    });

    // Handle volume minus button
    volMinButton.addEventListener('click', () => {
        if (volumePercentage === 0) volumePercentage = previousVolume; // Unmute on volume adjustment
        volumePercentage = Math.max(volumePercentage - 5, 0); // Decrease by 5%, minimum 0%
        previousVolume = volumePercentage > 0 ? volumePercentage : previousVolume; // Update last volume if not muted
        progressBar.style.width = `${volumePercentage}%`;
        audioElement.volume = volumePercentage / 100; // Update audio volume
        updatePercentagePosition();
        showVolumeDisplay();
    });

    // Handle volume plus button
    volPlusButton.addEventListener('click', () => {
        if (volumePercentage === 0) volumePercentage = previousVolume; // Unmute on volume adjustment
        volumePercentage = Math.min(volumePercentage + 5, 100); // Increase by 5%, maximum 100%
        previousVolume = volumePercentage > 0 ? volumePercentage : previousVolume; // Update last volume if not muted
        progressBar.style.width = `${volumePercentage}%`;
        audioElement.volume = volumePercentage / 100; // Update audio volume
        updatePercentagePosition();
        showVolumeDisplay();
    });

    // Initialize position and volume display
    updatePercentagePosition();
}
// Initialize volume controls
initializeVolumeControls();






// redo navigation bar - session
// Select the elements
const popupSessionBarVideo2 = document.getElementById('popup-session-bar-video');
const popupSelection = document.getElementById('popup-selection');
const profileWindowButton2 = document.getElementById('profile-window');
const userButton = document.getElementById('user'); // User button
const infoButton = document.getElementById('info'); // Assuming infoButton exists

// Function to update the bottom position of popup-session-bar-video
function updateBottomPosition() {
    if (popupSessionBarVideo2.classList.contains('activated')) {
        // If activated, check for the show class on popupSelection
        if (popupSelection.classList.contains('show')) {
            popupSessionBarVideo2.style.bottom = '152px';
        } else {
            popupSessionBarVideo2.style.bottom = '0';
        }
    } else {
        // If not activated, set bottom to -152px
        popupSessionBarVideo2.style.bottom = '-152px';
    }
}

// Add click event listener to the close-pop-btn
closePopBtn.addEventListener('click', () => {
    // Remove the 'show' class from popup-selection
    popupSelection.classList.remove('show');

    // Update the bottom position of popup-session-bar-video2
    updateBottomPosition();
});

// Function to toggle the activated class on popup-session-bar-video
function togglePopupBar(button) {
    const isActivated = popupSessionBarVideo2.classList.toggle('activated');
    updateBottomPosition();

    // Handle user button's active classes
    if (button === userButton) {
        if (isActivated) {
            userButton.classList.add('active-btn-background', 'active-btn-shadow');
        } else {
            userButton.classList.remove('active-btn-background', 'active-btn-shadow');
        }
    }
}

// Function to handle syncing of button states
function syncButtons() {
    const isActivated = popupSessionBarVideo2.classList.contains('activated');

    // Sync the user button
    if (isActivated) {
        userButton.classList.add('active-btn-background', 'active-btn-shadow');
    } else {
        userButton.classList.remove('active-btn-background', 'active-btn-shadow');
    }
}

// Initial check for bottom position and syncing the user button
updateBottomPosition();
syncButtons();

// Monitor for changes in popupSessionBarVideo2 dynamically
const observerForVideoBar = new MutationObserver(() => {
    updateBottomPosition();
    syncButtons(); // Ensure buttons stay in sync
});

// Start observing the popupSessionBarVideo2 element for class changes
observerForVideoBar.observe(popupSessionBarVideo2, { attributes: true, attributeFilter: ['class'] });

// Monitor for changes in popupSelection dynamically
const observerForPopupSelection = new MutationObserver(() => {
    updateBottomPosition();
    syncButtons(); // Ensure buttons stay in sync
});

// Start observing the popupSelection element for class changes
observerForPopupSelection.observe(popupSelection, { attributes: true, attributeFilter: ['class'] });

// Add click event listener to profileWindowButton
profileWindowButton2.addEventListener('click', () => {
    // Toggle only the activated class
    popupSessionBarVideo2.classList.toggle('activated');
    updateBottomPosition();
});

// Add click event listener to the #user button
userButton.addEventListener('click', () => togglePopupBar(userButton));

// Add click event listener to the infoButton
infoButton.addEventListener('click', () => {
    const isActivated = popupSessionBarVideo2.classList.contains('activated');

    // Toggle the activated class
    if (isActivated) {
        popupSessionBarVideo2.classList.remove('activated');
    } else {
        popupSessionBarVideo2.classList.add('activated');
    }
    updateBottomPosition();
    syncButtons(); // Ensure buttons are synced
});

// minimize

// Select elements for the min/max functionality
const popupSessionMinMax = document.getElementById('popup-session'); // First popup
const popupShareMinMax = document.getElementById('popup-share'); // Second popup
const minMaxButtonSession = document.getElementById('x-session-minmax-1'); // First button
const minMaxButtonShare = document.getElementById('x-session-minmax-2'); // Second button

// Function to make a popup draggable
function enableMinMaxDraggable(popupElement) {
    let isDragging = false;
    let startX, startY, initialX, initialY;

    const onMouseMove = (e) => {
        if (isDragging) {
            const dx = e.clientX - startX;
            const dy = e.clientY - startY;
            popupElement.style.left = `${initialX + dx}px`;
            popupElement.style.top = `${initialY + dy}px`;
        }
    };

    const onMouseUp = () => {
        isDragging = false;
        popupElement.style.transition = ''; // Re-enable transition after dragging
        document.removeEventListener('mousemove', onMouseMove);
        document.removeEventListener('mouseup', onMouseUp);
    };

    popupElement.addEventListener('mousedown', (e) => {
        isDragging = true;
        startX = e.clientX;
        startY = e.clientY;
        const rect = popupElement.getBoundingClientRect();
        initialX = rect.left;
        initialY = rect.top;
        popupElement.style.transition = 'none'; // Disable transition during dragging

        document.addEventListener('mousemove', onMouseMove);
        document.addEventListener('mouseup', onMouseUp);
    });
}

// Function to toggle the minimized state
function toggleMinMax(popupElement) {
    if (popupElement.classList.contains('minimized')) {
        popupElement.classList.remove('minimized');
        popupElement.style.width = ''; // Reset styles
        popupElement.style.height = '';
        popupElement.style.top = '';
        popupElement.style.left = '';
        popupElement.style.position = 'absolute';
    } else {
        popupElement.classList.add('minimized');
        popupElement.style.width = '510px';
        popupElement.style.height = '300px';

        // Calculate center position
        const viewportWidth = window.innerWidth;
        const viewportHeight = window.innerHeight;
        const popupWidth = 510; // Minimized width
        const popupHeight = 300; // Minimized height
        const centerX = (viewportWidth - popupWidth) / 2;
        const centerY = (viewportHeight - popupHeight) / 2;

        popupElement.style.top = `${centerY}px`;
        popupElement.style.left = `${centerX}px`;
        popupElement.style.position = 'absolute';
    }
}

// Add click listeners to the min/max buttons
minMaxButtonSession.addEventListener('click', () => {
    toggleMinMax(popupSessionMinMax);
    enableMinMaxDraggable(popupSessionMinMax); // Make the popup draggable when minimized
});

minMaxButtonShare.addEventListener('click', () => {
    toggleMinMax(popupShareMinMax);
    enableMinMaxDraggable(popupShareMinMax); // Make the popup draggable when minimized
});


// SVG 


// GATE AND DYN
document.addEventListener('DOMContentLoaded', function () {
    // Utility functions for mapping rotation and axis
    function mapRotationToAxis(angle, minAngle, maxAngle, minValue, maxValue) {
        return ((angle - minAngle) / (maxAngle - minAngle)) * (maxValue - minValue) + minValue;
    }

    function mapAxisToRotation(x, minValue, maxValue, minAngle, maxAngle) {
        return ((x - minValue) / (maxValue - minValue)) * (maxAngle - minAngle) + minAngle;
    }

    function clampRotationAngle(angle) {
        return Math.max(-140, Math.min(140, angle)); // Ensure the angle stays within -140 to 140 degrees
    }

    // Function to calculate the broken diagonal progression and map the wheel to line2's x1 value for EXP SVGs
    function updateLinesAndWheel(limitedX, svgElement, maxLine2X1) {
        const rect = svgElement.getBoundingClientRect();
        const line1 = svgElement.querySelector('#line1');
        const line2 = svgElement.querySelector('#line2');
        const line3 = svgElement.querySelector('#line3');

        // For line1, move its x2 along the X-axis while keeping y2 fixed
        line1.setAttribute('x1', 0);
        line1.setAttribute('y1', rect.height + 30);
        line1.setAttribute('x2', limitedX);
        line1.setAttribute('y2', rect.height + 3);

        // Calculate break points for line2 and line3
        let x1ForLine2 = Math.min(limitedX, maxLine2X1);
        let x2ForLine2 = mapRotationToAxis(x1ForLine2, 0, maxLine2X1, 0, rect.width);
        let y2ForLine2 = mapRotationToAxis(x1ForLine2, 0, maxLine2X1, rect.height, 0);

        // Adjust y2 for a smoother diagonal progression
        line2.setAttribute('x1', x1ForLine2);
        line2.setAttribute('y1', rect.height);
        line2.setAttribute('x2', x2ForLine2);
        line2.setAttribute('y2', y2ForLine2);

        // Update line3 for the diagonal progression
        line3.setAttribute('x1', x2ForLine2);
        line3.setAttribute('y1', y2ForLine2);
        line3.setAttribute('x2', rect.width);
        line3.setAttribute('y2', 0);
    }

    // Function to move lines for gate and duck SVGs diagonally towards the top-right corner
    function updateGateAndDuckLines(limitedX, svgElement) {
        const rect = svgElement.getBoundingClientRect();
        const line1 = svgElement.querySelector('#line1');
        const line2 = svgElement.querySelector('#line2');
        const line3 = svgElement.querySelector('#line3');

        // Move line1 diagonally from bottom-left to top-right
        line1.setAttribute('x2', limitedX);
        line1.setAttribute('y2', rect.height - limitedX);

        // Keep line2 as the connector but move it vertically to connect
        line2.setAttribute('x1', limitedX);
        line2.setAttribute('x2', limitedX);
        line2.setAttribute('y1', rect.height - limitedX);
        line2.setAttribute('y2', rect.height - limitedX + 20); // Adjust height for connection

        // Move line3 diagonally from bottom of line2 to top-right corner
        line3.setAttribute('x1', limitedX);
        line3.setAttribute('y1', rect.height - limitedX + 20);
        line3.setAttribute('x2', rect.width); // Moving towards top-right
        line3.setAttribute('y2', 0); // Top-right corner
    }

    // Function to update both the wheel and lines together
    function updateEverythingFromRotation(rotationAngle, svgElement) {
        const rect = svgElement.getBoundingClientRect();
        const clampedAngle = clampRotationAngle(rotationAngle);
        const limitedX = mapRotationToAxis(clampedAngle, -140, 140, 0, rect.width);

        // Handling for EXP SVGs (unchanged)
        if (svgElement.classList.contains('svg_exp2')) {
            updateLinesAndWheel(limitedX, svgElement, 160); // Exp2: Max x1 for line2 = 160
        } else if (svgElement.classList.contains('svg_exp3')) {
            updateLinesAndWheel(limitedX, svgElement, 215); // Exp3: Max x1 for line2 = 215
        } else if (svgElement.classList.contains('svg_exp4')) {
            updateLinesAndWheel(limitedX, svgElement, 240); // Exp4: Max x1 for line2 = 240
        } else if (svgElement.classList.contains('svg_gate')) {
            updateGateAndDuckLines(limitedX, svgElement); // Gate SVG
        } else if (svgElement.classList.contains('svg_duck')) {
            updateGateAndDuckLines(limitedX, svgElement); // Duck SVG
        }
    }

    // Function to update the wheel and map the wheel's rotation to the lines
    function updateWheelAndPath(rotationAngle, knobX, progressPath) {
        const clampedAngle = clampRotationAngle(rotationAngle);
        knobX.style.transform = `rotate(${clampedAngle}deg)`;

        const percentage = (clampedAngle + 140) / 280; // Convert rotation angle to a percentage
        const pathLength = 208.4;
        const offset = pathLength - (percentage * pathLength);
        progressPath.style.strokeDashoffset = offset;
    }

    // Ensure the wheel and lines are synchronized
    function syncWheelAndLines(rotationAngle, svgElement, knobX, progressPath) {
        updateEverythingFromRotation(rotationAngle, svgElement);
        updateWheelAndPath(rotationAngle, knobX, progressPath);
    }

    // Part 1: For dropdown-controlled multiple SVGs (EXP2, EXP3, etc.)
    const dropdownOptions = document.querySelectorAll('.dropdown-option');
    const svgElements = document.querySelectorAll('.svg_line_pattern');
    let currentSVG = document.querySelector('.svg_exp2'); // Default selected SVG (EXP2)

    // Function to show the selected SVG and hide the others
    function showSelectedSVG(selectedClass) {
        svgElements.forEach(svg => {
            if (svg.classList.contains(selectedClass)) {
                svg.style.display = 'block';
                currentSVG = svg; // Update the current active SVG to the selected one
            } else {
                svg.style.display = 'none';
            }
        });
    }

    // Handle dropdown options to switch between different SVGs
    dropdownOptions.forEach(option => {
        option.addEventListener('click', (e) => {
            const selectedOption = e.target.id;
            switch (selectedOption) {
                case 'type-option-01':
                    showSelectedSVG('svg_exp2');
                    break;
                case 'type-option-02':
                    showSelectedSVG('svg_exp3');
                    break;
                case 'type-option-03':
                    showSelectedSVG('svg_exp4');
                    break;
                case 'type-option-04':
                    showSelectedSVG('svg_gate');
                    break;
                case 'type-option-05':
                    showSelectedSVG('svg_duck');
                    break;
            }
        });
    });

    
    


    // Event listeners and wheel/line control logic scoped by closest 'inner-wrapper-detail'
    const wrappers = document.querySelectorAll('.inner-wrapper-detail');
    wrappers.forEach(wrapper => {
        const knobX = wrapper.querySelector('.wheel-knob');
        const progressPath = wrapper.querySelector('.progress-path');
        let isDragging = false;
        let isRotatingKnob = false;

        // Function to update lines and the wheel together
        wrapper.addEventListener('mousedown', (e) => {
            const rect = currentSVG.getBoundingClientRect();
            if (e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom) {
                isDragging = true;
                const rotationAngle = mapAxisToRotation(e.clientX - rect.left, 0, rect.width, -140, 140);
                syncWheelAndLines(rotationAngle, currentSVG, knobX, progressPath);
            }
        });

        wrapper.addEventListener('mousemove', (e) => {
            if (isDragging) {
                const rect = currentSVG.getBoundingClientRect();
                if (e.clientY >= rect.top && e.clientY <= rect.bottom) {
                    const rotationAngle = mapAxisToRotation(e.clientX - rect.left, 0, rect.width, -140, 140);
                    syncWheelAndLines(rotationAngle, currentSVG, knobX, progressPath);
                }
            }
        });

        wrapper.addEventListener('mouseup', () => {
            isDragging = false;
        });

        // Touch events for lines restricted to 'inner-wrapper-detail'
        wrapper.addEventListener('touchstart', (e) => {
            const rect = currentSVG.getBoundingClientRect();
            if (e.touches[0].clientX >= rect.left && e.touches[0].clientX <= rect.right && e.touches[0].clientY >= rect.top && e.touches[0].clientY <= rect.bottom) {
                isDragging = true;
                const rotationAngle = mapAxisToRotation(e.touches[0].clientX - rect.left, 0, rect.width, -140, 140);
                syncWheelAndLines(rotationAngle, currentSVG, knobX, progressPath);
            }
        });

        wrapper.addEventListener('touchmove', (e) => {
            if (isDragging) {
                const rect = currentSVG.getBoundingClientRect();
                if (e.touches[0].clientY >= rect.top && e.touches[0].clientY <= rect.bottom) {
                    const rotationAngle = mapAxisToRotation(e.touches[0].clientX - rect.left, 0, rect.width, -140, 140);
                    syncWheelAndLines(rotationAngle, currentSVG, knobX, progressPath);
                }
            }
        });

        wrapper.addEventListener('touchend', () => {
            isDragging = false;
        });

        // Event listeners for wheel rotation
        knobX.addEventListener('mousedown', () => {
            isRotatingKnob = true;
        });

        document.addEventListener('mousemove', (e) => {
            if (isRotatingKnob) {
                const rotation = knobX.style.transform.match(/rotate\((-?\d+(\.\d+)?)deg\)/);
                if (rotation) {
                    const currentKnobRotation = clampRotationAngle(parseFloat(rotation[1]));
                    syncWheelAndLines(currentKnobRotation, currentSVG, knobX, progressPath);
                }
            }
        });

        document.addEventListener('mouseup', () => {
            isRotatingKnob = false;
        });

        document.addEventListener('touchmove', (e) => {
            if (isRotatingKnob) {
                const rotation = knobX.style.transform.match(/rotate\((-?\d+(\.\d+)?)deg\)/);
                if (rotation) {
                    const currentKnobRotation = clampRotationAngle(parseFloat(rotation[1]));
                    syncWheelAndLines(currentKnobRotation, currentSVG, knobX, progressPath);
                }
            }
        });

        document.addEventListener('touchend', () => {
            isRotatingKnob = false;
        });
    });

    // Part 2: For the two-option element (COMP/EXP)
    const secondWrapper = document.querySelector('.dyn-tab-wrapper');
    const knobCompExp = secondWrapper.querySelector('.wheel-knob');
    const progressPathCompExp = secondWrapper.querySelector('.progress-path');
    let currentCompExpSVG = document.querySelector('.comp-dyn'); // Default selected SVG for COMP/EXP
    let isDraggingCompExp = false;
    let isRotatingKnobCompExp = false;

    // Initially, COMP is active
    const compType = document.getElementById('comp-type');
    const expType = document.getElementById('comp-exp');
    const compSVG = document.querySelector('.comp-dyn');
    const expSVG = document.querySelector('.exp-dyn');
    compType.classList.add('scrolled-active');
    compSVG.style.display = 'block';
    expSVG.style.display = 'none';

    // Switch between COMP and EXP
    compType.addEventListener('click', () => {
        compType.classList.add('scrolled-active');
        expType.classList.remove('scrolled-active');
        compSVG.style.display = 'block';
        expSVG.style.display = 'none';
        currentCompExpSVG = compSVG; // Set the current active SVG
    });

    expType.addEventListener('click', () => {
        expType.classList.add('scrolled-active');
        compType.classList.remove('scrolled-active');
        expSVG.style.display = 'block';
        compSVG.style.display = 'none';
        currentCompExpSVG = expSVG; // Set the current active SVG
    });

    // Wheel and line control logic for COMP/EXP
    function updateProgressPathCompExp(angle) {
        var percentage = (angle + 140) / 280;
        var pathLength = 208.4;
        var offset = pathLength - (percentage * pathLength);
        progressPathCompExp.style.strokeDashoffset = offset;
    }

    function updateWheelAndPathCompExp(rotationAngle) {
        const clampedAngle = clampRotationAngle(rotationAngle);
        knobCompExp.style.transform = `rotate(${clampedAngle}deg)`;
        updateProgressPathCompExp(clampedAngle);
    }

    function updateLinesAndWheelFromXCompExp(limitedX, svgElement) {
        const rect = svgElement.getBoundingClientRect();
        const line1 = svgElement.querySelector('#line1');
        const line2 = svgElement.querySelector('#line2');
        const line3 = svgElement.querySelector('#line3');

        if (!rect.width || rect.width === 0) return;

        const percentageMoved = limitedX / (rect.width / 2);
        const breakX = rect.width * percentageMoved;
        const breakY = rect.height * (1 - percentageMoved);

        if (isNaN(breakX) || isNaN(breakY)) return;

        line1.setAttribute('x1', 0);
        line1.setAttribute('y1', rect.height + 30);
        line1.setAttribute('x2', limitedX);
        line1.setAttribute('y2', rect.height + 3);

        line2.setAttribute('x1', limitedX);
        line2.setAttribute('y1', rect.height);
        line2.setAttribute('x2', breakX);
        line2.setAttribute('y2', breakY);

        line3.setAttribute('x1', breakX);
        line3.setAttribute('y1', breakY);
        line3.setAttribute('x2', rect.width);
        line3.setAttribute('y2', 0);
    }

    function updateEverythingFromRotationCompExp(rotationAngle, svgElement) {
        const rect = svgElement.getBoundingClientRect();
        const clampedAngle = clampRotationAngle(rotationAngle);
        const limitedX = mapRotationToAxis(clampedAngle, -140, 140, 0, rect.width / 2);

        updateLinesAndWheelFromXCompExp(limitedX, svgElement);
        updateWheelAndPathCompExp(clampedAngle);
    }

    // Event listeners for line dragging for COMP/EXP
    secondWrapper.addEventListener('mousedown', (e) => {
        const rect = currentCompExpSVG.getBoundingClientRect();
        if (e.clientX >= rect.left && e.clientX <= rect.right && e.clientY >= rect.top && e.clientY <= rect.bottom) {
            isDraggingCompExp = true;
            updateEverythingFromRotationCompExp(mapAxisToRotation(e.clientX - rect.left, 0, rect.width / 2, -140, 140), currentCompExpSVG);
        }
    });

    secondWrapper.addEventListener('mousemove', (e) => {
        if (isDraggingCompExp) {
            const rect = currentCompExpSVG.getBoundingClientRect();
            if (e.clientY >= rect.top && e.clientY <= rect.bottom) {
                updateEverythingFromRotationCompExp(mapAxisToRotation(e.clientX - rect.left, 0, rect.width / 2, -140, 140), currentCompExpSVG);
            }
        }
    });

    secondWrapper.addEventListener('mouseup', () => {
        isDraggingCompExp = false;
    });

    // Touch events for lines restricted to 'dyn-tab-wrapper'
    secondWrapper.addEventListener('touchstart', (e) => {
        const rect = currentCompExpSVG.getBoundingClientRect();
        if (e.touches[0].clientX >= rect.left && e.touches[0].clientX <= rect.right && e.touches[0].clientY >= rect.top && e.touches[0].clientY <= rect.bottom) {
            isDraggingCompExp = true;
            updateEverythingFromRotationCompExp(mapAxisToRotation(e.touches[0].clientX - rect.left, 0, rect.width / 2, -140, 140), currentCompExpSVG);
        }
    });

    secondWrapper.addEventListener('touchmove', (e) => {
        if (isDraggingCompExp) {
            const rect = currentCompExpSVG.getBoundingClientRect();
            if (e.touches[0].clientY >= rect.top && e.touches[0].clientY <= rect.bottom) {
                updateEverythingFromRotationCompExp(mapAxisToRotation(e.touches[0].clientX - rect.left, 0, rect.width / 2, -140, 140), currentCompExpSVG);
            }
        }
    });

    secondWrapper.addEventListener('touchend', () => {
        isDraggingCompExp = false;
    });

    // Event listeners for wheel rotation within 'dyn-tab-wrapper'
    knobCompExp.addEventListener('mousedown', () => {
        isRotatingKnobCompExp = true;
    });

    document.addEventListener('mousemove', (e) => {
        if (isRotatingKnobCompExp) {
            const rotation = knobCompExp.style.transform.match(/rotate\((-?\d+(\.\d+)?)deg\)/);
            if (rotation) {
                const currentKnobRotation = clampRotationAngle(parseFloat(rotation[1]));
                updateEverythingFromRotationCompExp(currentKnobRotation, currentCompExpSVG);
            }
        }
    });

    document.addEventListener('mouseup', () => {
        isRotatingKnobCompExp = false;
    });

    document.addEventListener('touchmove', (e) => {
        if (isRotatingKnobCompExp) {
            const rotation = knobCompExp.style.transform.match(/rotate\((-?\d+(\.\d+)?)deg\)/);
            if (rotation) {
                const currentKnobRotation = clampRotationAngle(parseFloat(rotation[1]));
                updateEverythingFromRotationCompExp(currentKnobRotation, currentCompExpSVG);
            }
        }
    });

    document.addEventListener('touchend', () => {
        isRotatingKnobCompExp = false;
    });
});
// END SVG ANIMATIONS

// slope small animation 

document.querySelectorAll('.wheel-container').forEach(container => {
    const wheel = container.querySelector('.slope-item .wheel-knob');
    const slopeItems = container.querySelectorAll('.item-slope');

    if (!wheel) {
        console.error('Wheel knob element not found in container:', container);
        return; // Exit if the wheel knob is not found
    }

    // Function to extract the degree from the wheel's transform property
    const getWheelRotation = () => {
        const transform = window.getComputedStyle(wheel).getPropertyValue('transform');
        if (transform !== 'none') {
            const matrix = transform.match(/matrix\((.+)\)/)[1].split(', ');
            const a = matrix[0];
            const b = matrix[1];
            let angle = Math.round(Math.atan2(b, a) * (180 / Math.PI));
            return angle; // This will give you the rotation angle
        }
        return 0; // Default to 0 if no transform is applied
    };

    // Function to update slope based on the degree
    const updateSlope = (degree) => {
        slopeItems.forEach(item => item.classList.remove('active-slope'));

        if (degree >= -140 && degree < -115) {
            slopeItems[0].classList.add('active-slope');
        } else if (degree >= -115 && degree < -75) {
            slopeItems[1].classList.add('active-slope');
        } else if (degree >= -75 && degree < -40) {
            slopeItems[2].classList.add('active-slope');
        } else if (degree >= -40 && degree <= -1) {
            slopeItems[3].classList.add('active-slope');
        } else if (degree > -1 && degree <= 140) {
            slopeItems[4].classList.add('active-slope');
        }
    };

    // Continuously monitor the wheel's rotation
    const monitorWheel = () => {
        const currentDegree = getWheelRotation();
        updateSlope(currentDegree);

        // Continuously check for changes
        requestAnimationFrame(monitorWheel);
    };

    // Start monitoring the wheel's rotation
    monitorWheel();
});


const muteButton = document.getElementById('mute'); // Mute button element
let isMuted = true; // Track mute status
let audioStream = null; // Store microphone stream
let audioContext = null; // Web Audio API context
let mediaStreamSource = null; // Audio source node

// Function to start the microphone
async function startMicrophone() {
    try {
        // Request access to the microphone
        audioStream = await navigator.mediaDevices.getUserMedia({ audio: true });

        // Create an audio context
        audioContext = new (window.AudioContext || window.webkitAudioContext)();

        // Create a media stream source from the microphone stream
        mediaStreamSource = audioContext.createMediaStreamSource(audioStream);

        // Connect the source to the audio context destination (speakers)
        mediaStreamSource.connect(audioContext.destination);

        console.log('Microphone started');
    } catch (error) {
        console.error('Error accessing microphone:', error);
    }
}

// Function to toggle mute/unmute
function toggleMute() {
    if (!audioStream) {
        console.warn('Microphone has not been started yet.');
        return;
    }

    isMuted = !isMuted;

    // Mute/unmute the microphone
    audioStream.getAudioTracks().forEach(track => {
        track.enabled = !isMuted;
    });

    // Update the mute button icon
    const muteIcon = muteButton.querySelector('img');
    muteIcon.src = isMuted ? 'assets/icons/mute.svg' : 'assets/icons/unmute.svg';
    muteIcon.alt = isMuted ? 'mute' : 'unmute';

    console.log(isMuted ? 'Microphone muted' : 'Microphone unmuted');
}

// Add click event listener to the mute button
muteButton.addEventListener('click', () => {
    if (!audioStream) {
        startMicrophone().then(() => {
            toggleMute(); // Mute/unmute after starting the microphone
        });
    } else {
        toggleMute();
    }
});


document.getElementById("wifi").addEventListener("click", function () {
    let ipAddress = prompt("Enter the IP Address:");

    if (ipAddress) {
        fetch("/set-ip", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ ip: ipAddress })
        })
        .then(response => response.json())
        .then(data => {
            alert(data.message);

            if (data.mute_status && data.fader_values) {
                updateUI(data.mute_status, data.fader_values);
            }
        })
        .catch(error => {
            console.error("Error:", error);
        });
    }
});

function updateUI(muteStatus, faderValues) {
    Object.keys(muteStatus).forEach(channel => {
        let muteButton = document.getElementById(`mute-${channel}`);
        if (muteButton) {
            if (muteStatus[channel] === 0) {
                muteButton.classList.add("active-btn-background"); // Muted
            } else {
                muteButton.classList.remove("active-btn-background"); // Unmuted
            }
        }
    });

    Object.keys(faderValues).forEach(channel => {
        let fader = document.getElementById(`dragger-ch${channel}`);
        let volumeBoard = document.querySelector(`.volume-board[data-channel="${channel}"]`);

        if (fader && volumeBoard) {
            let boardHeight = volumeBoard.getBoundingClientRect().height;
            let draggerHeight = fader.getBoundingClientRect().height;

            // Convert fader value (0.0 - 1.0) to pixel position
            let newY = (1 - faderValues[channel]) * (boardHeight - draggerHeight);
            fader.style.top = `${newY}px`;
        }
    });
}

document.addEventListener("DOMContentLoaded", function() {
    const observer = new MutationObserver(mutations => {
        mutations.forEach(mutation => {
            if (mutation.attributeName === "class") {
                let target = mutation.target;
                if (target.classList.contains("mute-solo-item")) {
                    let buttonId = target.id;
                    let buttonType = buttonId.split("-")[0]; // 'mute' or 'solo'
                    let channelNumber = buttonId.split("-")[1];
                    let isActive = target.classList.contains("active-btn-background");

                    // Check if we have session manager available for OSC commands (dynamically)
                    const hasSessionManager = typeof window.mainAppSessionManager !== 'undefined' && 
                                            window.mainAppSessionManager !== null && 
                                            window.mainAppSessionManager.sessionKey;

                    console.log(`🎛️ ${buttonType.toUpperCase()} button clicked: CH${channelNumber}, isActive: ${isActive}, hasSessionManager: ${!!hasSessionManager}`);

                    // Handle both mute and solo buttons
                    let oscCommand;
                    if (buttonType === 'mute') {
                        oscCommand = {
                            type: 'mute',
                            channel: parseInt(channelNumber),
                            action: isActive ? 'mute' : 'unmute'
                        };
                    } else if (buttonType === 'solo') {
                        oscCommand = {
                            type: 'solo',
                            channel: parseInt(channelNumber),
                            action: isActive ? 'solo' : 'unsolo'
                        };
                    } else {
                        console.log(`⚠️ Unknown button type: ${buttonType}`);
                        return;
                    }

                    // Use session-based OSC system if available, otherwise fallback to old system
                    if (hasSessionManager) {
                        // Send via session relay system with latency measurement
                        const startTime = performance.now();
                        
                        if (window.mainAppSessionManager.sendOSCCommandWithTimestamp(oscCommand, startTime)) {
                            console.log(`Session OSC: Channel ${channelNumber} ${buttonType} → ${oscCommand.action}`);
                        } else {
                            console.error(`Failed to send session OSC command for channel ${channelNumber} ${buttonType}`);
                        }
                    } else {
                        // Fallback to old direct connection system
                        console.log(`🎛️ ${buttonType.toUpperCase()} button clicked: CH${channelNumber}, using direct connection (no session)`);
                        
                        if (buttonType === 'mute') {
                            fetch("/mute", {
                                method: "POST",
                                headers: { "Content-Type": "application/json" },
                                body: JSON.stringify({ mute_number: channelNumber, action: isActive ? "mute" : "unmute" })
                            })
                            .then(response => response.json())
                            .then(data => {
                                console.log(`Direct: Channel ${channelNumber} ${isActive ? "muted" : "unmuted"}`);
                            })
                            .catch(error => console.error("Error:", error));
                        } else if (buttonType === 'solo') {
                            console.log(`⚠️ Solo functionality not available in direct connection mode`);
                        }
                    }
                }
            }
        });
    });

    // Observe all mute buttons for class changes
    document.querySelectorAll(".mute-solo-item").forEach(button => {
        observer.observe(button, { attributes: true });
    });
});


$(document).ready(function () {
    // Define custom tooltip content mapped to slider positions
    var customTooltips = [
        "10dB", "5dB", "0dB", "-5dB", "-10dB", "-20dB", "-30dB", "-40dB", "-50dB", "-60dB"
    ];

    $(".mixer-dragger").each(function () {
        $(this).draggable({
            axis: "y",
            containment: $(this).parent(),
            drag: function (event, ui) {
                handleDrag($(this), ui);
            }
        });
    });

    function handleDrag(element, ui) {
        var channel = element.closest('.mixer-container').find('.volume-board').data('channel');
        var containerHeight = element.parent().height();
        var draggerHeight = element.height();
        var draggerPosition = ui.position.top;

        // Normalize the fader position (0.0 at the bottom, 1.0 at the top)
        var faderValue = 1 - (draggerPosition / (containerHeight - draggerHeight));
        faderValue = Math.max(0.0, Math.min(1.0, faderValue)); // Ensure within 0.0 - 1.0 range

        console.log(`Channel ${channel} → Fader Value: ${faderValue.toFixed(3)}`);

        // Send the normalized fader value to the backend
        sendFaderValue(channel, faderValue);
    }

    function findClosestTooltip(revealPercentage) {
        var tooltipInterval = 10;
        var numTooltips = customTooltips.length;
        for (var i = 0; i < numTooltips; i++) {
            var minRange = i * tooltipInterval - 5;
            var maxRange = i * tooltipInterval + 5;
            if (revealPercentage >= minRange && revealPercentage <= maxRange) {
                return customTooltips[i];
            }
        }
        return customTooltips[customTooltips.length - 1]; // Default to lowest value if no match
    }

    

    function sendFaderValue(channel, faderValue) {
        // Check if we have session manager available for OSC commands (dynamically)
        const hasSessionManager = typeof window.mainAppSessionManager !== 'undefined' && 
                                window.mainAppSessionManager !== null && 
                                window.mainAppSessionManager.sessionKey;
        
        if (hasSessionManager) {
            // Send via session relay system with latency measurement
            console.log(`🎛️ Fader moved: CH${channel}, value: ${faderValue.toFixed(3)}, sending via session`);
            const startTime = performance.now();
            const oscCommand = {
                type: 'fader',
                channel: parseInt(channel),
                value: parseFloat(faderValue.toFixed(3))
            };
            
            if (window.mainAppSessionManager.sendOSCCommandWithTimestamp(oscCommand, startTime)) {
                console.log(`Session OSC: Channel ${channel} fader → ${faderValue.toFixed(3)}`);
            } else {
                console.error(`Failed to send session OSC fader command for channel ${channel}`);
            }
        } else {
            // Fallback to old direct connection system
            console.log(`🎛️ Fader moved: CH${channel}, value: ${faderValue.toFixed(3)}, using direct connection (no session)`);
            fetch("/set-fader", {
                method: "POST",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ channel: channel, value: faderValue.toFixed(3) }) // Send as a string to keep precision
            })
            .then(response => response.json())
            .then(data => console.log(`Direct: Channel ${channel} fader → ${faderValue.toFixed(3)}`))
            .catch(error => console.error("Error:", error));
        }
    }
});

document.addEventListener("DOMContentLoaded", function () {
    let sessionKey = localStorage.getItem("session_key");

    if (sessionKey) {
        validateSessionKey(sessionKey);
    }

    document.getElementById("create-session").addEventListener("click", function () {
        let existingSessionKey = localStorage.getItem("session_key");

        if (existingSessionKey) {
            alert("Your existing session key: " + existingSessionKey);
            document.getElementById("session-key-display").innerText = existingSessionKey;
            return;
        }

        fetch("/create-session", {
            method: "POST",
            headers: { "Content-Type": "application/json" }
        })
        .then(response => response.json())
        .then(data => {
            localStorage.setItem("session_key", data.session_key);
            document.getElementById("session-key-display").innerText = data.session_key;
            alert("Your session key: " + data.session_key);
        })
        .catch(error => console.error("Error creating session:", error));
    });

    document.getElementById("join-session").addEventListener("click", function () {
        let sessionKey = document.getElementById("session-key-input").value;

        if (!sessionKey) {
            alert("Please enter a session key.");
            return;
        }

        joinSession(sessionKey);
    });

    function validateSessionKey(sessionKey) {
        fetch("/validate-session", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ session_key: sessionKey })
        })
        .then(response => response.json())
        .then(data => {
            if (data.valid) {
                document.getElementById("session-key-display").innerText = sessionKey;
            } else {
                console.warn("Session key invalid. Recreating session.");
                localStorage.removeItem("session_key");
            }
        })
        .catch(error => console.error("Error validating session:", error));
    }

    function joinSession(sessionKey) {
        fetch("/join-session", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({ session_key: sessionKey })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                alert("Error: " + data.error);
            } else {
                alert("Joined session: " + data.session_key);
                localStorage.setItem("session_key", data.session_key);
                localStorage.setItem("user_id", data.user_id);
                document.getElementById("session-key-display").innerText = data.session_key;
            }
        })
        .catch(error => console.error("Error joining session:", error));
    }
});

const socket = io("wss://vse-alb-1049316298.us-east-1.elb.amazonaws.com", { path: "/socket.io/" });
// const socket = io("ws://localhost:5000", { path: "/socket.io/" });


socket.on("connect", () => {
    console.log("Connected to WebRTC Signaling Server");
});

socket.on("offer", async (data) => {
    if (!isHost) {
        await peerConnection.setRemoteDescription(data.offer);
        const answer = await peerConnection.createAnswer();
        await peerConnection.setLocalDescription(answer);
        socket.emit("answer", { session_key: sessionKey, answer: answer });
    }
});

socket.on("answer", async (data) => {
    if (isHost) {
        await peerConnection.setRemoteDescription(data.answer);
    }
});

socket.on("ice-candidate", async (data) => {
    await peerConnection.addIceCandidate(data.candidate);
});


// VOLUME Dragger
// dropdown video/audio
// dropdown video/audio
document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.direct_block').forEach(block => {
        // State variables
        let volume = 0;
        let lastNonZero = 50;

        // DOM elements
        const btn = block.querySelector('.btn_org');
        const pctText = block.querySelector('.percentage-text');
        const barCtrl = block.querySelector('.bar_controller');
        const progressBar = block.querySelector('.progress-bar');
        const container = block.querySelector('.progress-container');

        // Hide percentage text initially
        pctText.style.display = 'none';

        // Function to update UI based on volume
        function updateUI(showPercentage = false) {
            // Update progress bar width
            progressBar.style.width = `${volume}%`;

            // Update controller position
            barCtrl.style.left = `${volume}%`;

            // Update percentage text
            pctText.style.left = `${volume}%`;
            pctText.textContent = volume === 0 ? '0' : `${volume}%`;

            // Show/hide percentage text
            pctText.style.display = showPercentage ? 'block' : 'none';

            // Toggle muted button class
            btn.classList.toggle('muted_btn', volume === 0);
        }

        // Function to set volume based on position
        function setVolumeFromPosition(clientX) {
            const rect = container.getBoundingClientRect();
            const x = Math.max(0, Math.min(clientX - rect.left, rect.width));
            const newVolume = Math.round((x / rect.width) * 100);

            // Update volume
            volume = Math.max(0, Math.min(100, newVolume));

            // Remember last non-zero volume
            if (volume > 0) {
                lastNonZero = volume;
            }

            // Update UI
            updateUI(true);
        }

        // Mute/unmute button click handler
        btn.addEventListener('click', () => {
            if (volume > 0) {
                lastNonZero = volume;
                volume = 0;
            } else {
                volume = lastNonZero;
            }
            updateUI();
        });

        // Mouse events for desktop
        barCtrl.addEventListener('mousedown', (e) => {
            // Prevent text selection during drag
            e.preventDefault();

            // Set initial volume based on click position
            setVolumeFromPosition(e.clientX);

            // Function to handle mouse movement
            const handleMouseMove = (moveEvent) => {
                setVolumeFromPosition(moveEvent.clientX);
            };

            // Function to handle mouse up
            const handleMouseUp = () => {
                // Remove event listeners
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);

                // Hide percentage text
                updateUI(false);
            };

            // Add event listeners for drag
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);
        });

        // Touch events for mobile/tablet
        barCtrl.addEventListener('touchstart', (e) => {
            // Prevent scrolling during drag
            e.preventDefault();

            // Set initial volume based on touch position
            setVolumeFromPosition(e.touches[0].clientX);

            // Function to handle touch movement
            const handleTouchMove = (moveEvent) => {
                setVolumeFromPosition(moveEvent.touches[0].clientX);
            };

            // Function to handle touch end
            const handleTouchEnd = () => {
                // Remove event listeners
                document.removeEventListener('touchmove', handleTouchMove);
                document.removeEventListener('touchend', handleTouchEnd);

                // Hide percentage text
                updateUI(false);
            };

            // Add event listeners for drag
            document.addEventListener('touchmove', handleTouchMove);
            document.addEventListener('touchend', handleTouchEnd);
        });

        // Click on container to jump to position
        container.addEventListener('click', (e) => {
            // Ignore clicks on the controller itself
            if (e.target === barCtrl) return;

            // Set volume based on click position
            setVolumeFromPosition(e.clientX);

            // Hide percentage text after a short delay
            setTimeout(() => updateUI(false), 1000);
        });

        // Initialize UI
        updateUI();
    });
}); // END SVG ANIMATIONS


// Add RTA functionality with Python integration API
$(document).ready(function () {
    console.log("Document ready, initializing RTA functionality");

    // Global object to expose RTA API for Python integration
    window.RTAVisualizer = {
        // Array to store all RTA elements for easy access
        elements: [],

        // Method to update RTA visualization with real data
        updateVisualization: function (dataArray) {
            // dataArray should be an array of 220 values between 0-100 representing heights
            if (!Array.isArray(dataArray) || dataArray.length !== 220) {
                console.error("Invalid data format. Expected array of 220 values.");
                return;
            }

            // Update each element with the provided height
            this.elements.forEach((element, index) => {
                if (index < dataArray.length) {
                    // Apply height with a max of 100%
                    const height = Math.min(100, Math.max(0, dataArray[index]));
                    element.style.height = `${height}%`;
                }
            });
        },

        // Method to show the RTA visualization
        show: function () {
            const rtaContainer = document.querySelector('.rta-container');
            if (rtaContainer) {
                rtaContainer.style.display = 'block';
                $('#channel-one-eq-rta').addClass('active-button');
            }
        },

        // Method to hide the RTA visualization
        hide: function () {
            const rtaContainer = document.querySelector('.rta-container');
            if (rtaContainer) {
                rtaContainer.style.display = 'none';
                $('#channel-one-eq-rta').removeClass('active-button');
            }
        },

        // Method to toggle the RTA visualization
        toggle: function () {
            const rtaContainer = document.querySelector('.rta-container');
            if (rtaContainer) {
                if (rtaContainer.style.display === 'none') {
                    this.show();
                } else {
                    this.hide();
                }
            }
        }
    };

    // Create RTA elements
    function createRTAElements() {
        console.log("Creating RTA elements");
        const movementBoard = document.querySelector('.movement-board');
        if (!movementBoard) {
            console.error("Movement board not found when creating RTA elements");
            return null;
        }

        // Create RTA container
        const rtaContainer = document.createElement('div');
        rtaContainer.className = 'rta-container';
        rtaContainer.style.position = 'absolute';
        rtaContainer.style.top = '0';
        rtaContainer.style.left = '0';
        rtaContainer.style.width = '100%';
        rtaContainer.style.height = '100%';
        rtaContainer.style.display = 'none'; // Hidden by default
        rtaContainer.style.zIndex = '1'; // Below the markers but above the background

        // Calculate element width
        const boardWidth = 847; // Fixed width as specified
        const elementWidth = boardWidth / 220;

        // Create 220 RTA elements
        for (let i = 0; i < 220; i++) {
            const rtaElement = document.createElement('div');
            rtaElement.className = 'rta-element';
            rtaElement.id = `rta-element-${i}`;
            rtaElement.style.position = 'absolute';
            rtaElement.style.bottom = '0';
            rtaElement.style.left = `${i * elementWidth}px`;
            rtaElement.style.width = `${elementWidth}px`;
            rtaElement.style.height = '100%'; // Static mode: all elements at 100% height
            rtaElement.style.backgroundColor = 'rgb(20, 42, 76)'; // Using the specified color
            rtaElement.style.transition = 'height 0.1s ease-out';

            // Add to container
            rtaContainer.appendChild(rtaElement);

            // Store element in the global array for easy access
            window.RTAVisualizer.elements.push(rtaElement);
        }

        // Add container to movement board
        movementBoard.appendChild(rtaContainer);

        return rtaContainer;
    }

    // Create RTA elements if movement board exists
    const movementBoard = document.querySelector('.movement-board');
    if (movementBoard) {
        createRTAElements();
    }

    // Toggle RTA display when button is clicked
    $(document).on('click', '#channel-one-eq-rta', function () {
        // Create RTA elements if they don't exist yet
        let rtaContainer = document.querySelector('.rta-container');
        if (!rtaContainer) {
            rtaContainer = createRTAElements();
            if (!rtaContainer) return;
        }

        // Toggle display using the API
        window.RTAVisualizer.toggle();
    });

    // Check if the movement board is created dynamically
    if (!movementBoard) {
        const observer = new MutationObserver(function (mutations) {
            mutations.forEach(function (mutation) {
                if (mutation.type === 'childList') {
                    const addedNodes = mutation.addedNodes;
                    for (let i = 0; i < addedNodes.length; i++) {
                        if (addedNodes[i].nodeType === 1) { // Element node
                            if (addedNodes[i].classList && addedNodes[i].classList.contains('movement-board')) {
                                createRTAElements();
                                observer.disconnect();
                                return;
                            }

                            // Check children of added node
                            const boardInChild = addedNodes[i].querySelector('.movement-board');
                            if (boardInChild) {
                                createRTAElements();
                                observer.disconnect();
                                return;
                            }
                        }
                    }
                }
            });
        });

        // Start observing the document body for DOM changes
        observer.observe(document.body, { childList: true, subtree: true });
    }
});
