// NOTE: Currently, signaling messages are polled every second using setInterval.
// For improved performance and reduced latency, consider switching to WebSockets in future iterations.

// ================================
// DOM Elements for core functionality
// ================================
const webcamButton = document.getElementById("webcamButton");
const createSessionButton = document.getElementById("createSessionButton");
const callButton = document.getElementById("callButton");
const joinSessionInput = document.getElementById("joinSessionInput");
const joinSessionButton = document.getElementById("joinSessionButton");
const answerButton = document.getElementById("answerButton");
const hangupButton = document.getElementById("hangupButton");
const sessionKeyDisplay = document.getElementById("sessionKeyDisplay");
const webcamVideo = document.getElementById("webcamVideo");
const remoteVideo = document.getElementById("remoteVideo");

// ================================
// DOM Elements for new controls
// ================================
const micButton = document.getElementById("micButton");
const micDropdownButton = document.getElementById("micDropdownButton");
const micDropdownMenu = document.getElementById("micDropdownMenu");
const cameraButton = document.getElementById("cameraButton");
const presentButton = document.getElementById("presentButton");
const chatToggleButton = document.getElementById("chatToggleButton");
const moreOptionsButton = document.getElementById("moreOptionsButton");
const speakerButton = document.getElementById("speakerButton");
const speakerDropdownButton = document.getElementById("speakerDropdownButton");
const speakerDropdownMenu = document.getElementById("speakerDropdownMenu");
const moreOptionsDropdown = document.getElementById("moreOptionsDropdown");
const testSpeakerButton = document.getElementById("testSpeakerButton");

const chatSidebar = document.getElementById("chatSidebar");
const chatInput = document.getElementById("chatInput");
const chatMessages = document.getElementById("chatMessages");
const emojiButton = document.getElementById("emojiButton");
const emojiPicker = document.getElementById("emojiPicker");

// ================================
// WebRTC and Chat variables
// ================================
let localStream;
let peerConnection;
let chatChannel; // Data channel for chat messaging
let sessionKey = null; // Will hold the session key for signaling
const iceServers = {
  iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
};

// Variables for toggle controls
let audioEnabled = true;
let videoEnabled = true;

// ================================
// Start webcam (using default devices) with audio processing constraints
// and mute local playback to prevent feedback
// ================================
webcamButton.addEventListener("click", async () => {
  try {
    // Request default audio and video devices with echo cancellation, noise suppression, and auto gain control
    localStream = await navigator.mediaDevices.getUserMedia({
      video: true,
      audio: {
        echoCancellation: false,
        noiseSuppression: false,
        autoGainControl: false,
      },
    });
    // Attach stream to local video element and mute it to avoid feedback
    webcamVideo.srcObject = localStream;
    webcamVideo.muted = true; // Prevent local audio playback feedback
    callButton.disabled = false;
  } catch (error) {
    console.error("getUserMedia error:", error);
    alert("Error accessing media devices: " + error.name + " - " + error.message);
  }
});

// ================================
// Session creation and joining
// ================================
createSessionButton.addEventListener("click", async () => {
  const response = await fetch("/create-session", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
  });
  const data = await response.json();
  sessionKey = data.session_key;
  sessionKeyDisplay.textContent = sessionKey;
  alert("Share this session key with the other device to join the call.");
});

joinSessionButton.addEventListener("click", async () => {
  sessionKey = joinSessionInput.value.trim();
  if (!sessionKey) return alert("Enter a valid session key.");
  const response = await fetch("/join-session", {
    method: "POST",
    body: JSON.stringify({ session_key: sessionKey }),
    headers: { "Content-Type": "application/json" },
  });
  const data = await response.json();
  if (data.error) {
    alert(data.error);
  } else {
    alert("Joined session successfully.");
    answerButton.disabled = false;
  }
});

// ================================
// Initialize Peer Connection and Data Channel for chat messaging
// ================================
function initPeerConnection(isCaller = false) {
  peerConnection = new RTCPeerConnection(iceServers);
  localStream.getTracks().forEach((track) => {
    peerConnection.addTrack(track, localStream);
  });

  // Create data channel on the caller side
  if (isCaller) {
    chatChannel = peerConnection.createDataChannel("chat");
    setupChatChannel();
  }

  // For the answerer, listen for incoming data channel
  peerConnection.ondatachannel = (event) => {
    if (event.channel.label === "chat") {
      chatChannel = event.channel;
      setupChatChannel();
    }
  };

  peerConnection.onicecandidate = (event) => {
    if (event.candidate) {
      fetch("/candidate", {
        method: "POST",
        body: JSON.stringify({
          session_key: sessionKey,
          candidate: event.candidate,
        }),
        headers: { "Content-Type": "application/json" },
      });
    }
  };

  peerConnection.ontrack = (event) => {
    remoteVideo.srcObject = event.streams[0];
  };
}

// ================================
// Setup Chat Data Channel
// ================================
function setupChatChannel() {
  chatChannel.onopen = () => {
    console.log("Chat data channel is open.");
    chatInput.disabled = false;
  };

  chatChannel.onmessage = (event) => {
    addChatMessage("Remote", event.data);
  };

  chatChannel.onerror = (error) => {
    console.error("Chat data channel error:", error);
  };

  chatChannel.onclose = () => {
    console.log("Chat data channel closed.");
    chatInput.disabled = true;
  };
}

// ================================
// Utility to add messages to the chat sidebar
// ================================
function addChatMessage(sender, message) {
  const msgDiv = document.createElement("div");
  msgDiv.classList.add("chat-message");
  msgDiv.classList.add(sender === "You" ? "sent" : "received");
  const messageContent = document.createElement("div");
  messageContent.textContent = message;
  const timeSpan = document.createElement("span");
  timeSpan.classList.add("chat-timestamp");
  const now = new Date();
  timeSpan.textContent = now.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
  msgDiv.appendChild(messageContent);
  msgDiv.appendChild(timeSpan);
  chatMessages.appendChild(msgDiv);
  chatMessages.scrollTop = chatMessages.scrollHeight;
}

// ================================
// Create Call (Offer) and Answer Call functionality
// ================================
callButton.addEventListener("click", async () => {
  if (!sessionKey) return alert("Session key not set.");
  initPeerConnection(true);
  const offer = await peerConnection.createOffer();
  await peerConnection.setLocalDescription(offer);
  fetch("/offer", {
    method: "POST",
    body: JSON.stringify({
      session_key: sessionKey,
      offer: offer,
    }),
    headers: { "Content-Type": "application/json" },
  });
  hangupButton.disabled = false;
});

answerButton.addEventListener("click", async () => {
  if (!sessionKey) return alert("Session key not set.");
  if (!peerConnection) {
    initPeerConnection();
  }
  // Answer will be created once an offer is received via polling.
});

// Hangup call
hangupButton.addEventListener("click", () => {
  if (peerConnection) {
    peerConnection.close();
    peerConnection = null;
  }
  hangupButton.disabled = true;
});

// ================================
// Polling for incoming signaling messages
// ================================
setInterval(async () => {
  if (!sessionKey) return;
  const response = await fetch("/get-messages", {
    method: "POST",
    body: JSON.stringify({ session_key: sessionKey }),
    headers: { "Content-Type": "application/json" },
  });
  const data = await response.json();
  if (data.messages && data.messages.length > 0) {
    data.messages.forEach(async (msg) => {
      if (msg.type === "offer") {
        if (!peerConnection) {
          initPeerConnection();
        }
        await peerConnection.setRemoteDescription(new RTCSessionDescription(msg.data));
        const answer = await peerConnection.createAnswer();
        await peerConnection.setLocalDescription(answer);
        fetch("/answer", {
          method: "POST",
          body: JSON.stringify({
            session_key: sessionKey,
            answer: answer,
          }),
          headers: { "Content-Type": "application/json" },
        });
        hangupButton.disabled = false;
      } else if (msg.type === "answer") {
        await peerConnection.setRemoteDescription(new RTCSessionDescription(msg.data));
        hangupButton.disabled = false;
      } else if (msg.type === "candidate") {
        try {
          await peerConnection.addIceCandidate(new RTCIceCandidate(msg.data));
        } catch (e) {
          console.error("Error adding received ice candidate", e);
        }
      }
    });
  }
}, 1000);

// ================================
// New Controls: Microphone and Camera Toggle (existing functionality)
// ================================
micButton.addEventListener("click", () => {
  if (!localStream) return alert("Start your webcam first.");
  localStream.getAudioTracks().forEach((track) => (track.enabled = !track.enabled));
  audioEnabled = !audioEnabled;
  micButton.textContent = audioEnabled ? "Microphone" : "Unmute";
});

cameraButton.addEventListener("click", () => {
  if (!localStream) return alert("Start your webcam first.");
  localStream.getVideoTracks().forEach((track) => (track.enabled = !track.enabled));
  videoEnabled = !videoEnabled;
  cameraButton.textContent = videoEnabled ? "Camera" : "Turn Camera On";
});

// Screen sharing
presentButton.addEventListener("click", async () => {
  try {
    const displayStream = await navigator.mediaDevices.getDisplayMedia({ video: true });
    webcamVideo.srcObject = displayStream;
  } catch (error) {
    alert("Screen sharing cancelled or not supported.");
  }
});

// Chat sidebar toggle
chatToggleButton.addEventListener("click", () => {
  chatSidebar.style.display = chatSidebar.style.display === "flex" ? "none" : "flex";
});

// ================================
// More Options Dropdown Toggle
// ================================
moreOptionsButton.addEventListener("click", (e) => {
  e.stopPropagation();
  moreOptionsDropdown.style.display = moreOptionsDropdown.style.display === "block" ? "none" : "block";
});

// ================================
// Chat message sending via the data channel
// ================================
chatInput.addEventListener("keydown", (event) => {
  if (event.key === "Enter") {
    const message = chatInput.value.trim();
    if (!message) return;
    if (chatChannel && chatChannel.readyState === "open") {
      chatChannel.send(message);
      addChatMessage("You", message);
      chatInput.value = "";
    } else {
      alert("Chat channel is not open yet.");
    }
  }
});

// ================================
// Emoji Picker Functionality
// ================================
const emojis = [
  "😀", "😂", "😍", "😎", "😭", "👍", "🙏", "🎉", "😡", "😢",
  "🎵", "🎶", "🎧", "🎤", "🎷", "🎸", "🥁", "🎹", "🎺", "🎻",
  "🎚️", "🎛️", "🎙️", "💿", "📀", "🪘", "🕺", "💃", "🎼", "🎊",
  "🤘", "👌", "🔥", "💥", "💫", "✨"
];

emojis.forEach((emoji) => {
  const emojiSpan = document.createElement("span");
  emojiSpan.textContent = emoji;
  emojiSpan.addEventListener("click", () => {
    chatInput.value += emoji;
  });
  emojiPicker.appendChild(emojiSpan);
});

emojiButton.addEventListener("click", (event) => {
  event.stopPropagation();
  emojiPicker.style.display = emojiPicker.style.display === "block" ? "none" : "block";
});

document.addEventListener("click", (event) => {
  if (!emojiPicker.contains(event.target) && event.target !== emojiButton) {
    emojiPicker.style.display = "none";
  }
});

// ================================
// New Controls: Adjust Microphone and Speaker Devices
// ================================

// Populate available audio input devices (microphones)
async function populateAudioInputDevices() {
  const devices = await navigator.mediaDevices.enumerateDevices();
  const audioInputs = devices.filter((device) => device.kind === "audioinput");
  micDropdownMenu.innerHTML = "";
  audioInputs.forEach((device) => {
    const option = document.createElement("div");
    option.textContent = device.label || `Microphone ${device.deviceId}`;
    option.addEventListener("click", () => {
      selectAudioInputDevice(device.deviceId);
      micDropdownMenu.style.display = "none";
    });
    micDropdownMenu.appendChild(option);
  });
}

// Populate available audio output devices (speakers)
async function populateAudioOutputDevices() {
  const devices = await navigator.mediaDevices.enumerateDevices();
  const audioOutputs = devices.filter((device) => device.kind === "audiooutput");
  speakerDropdownMenu.innerHTML = "";
  audioOutputs.forEach((device) => {
    const option = document.createElement("div");
    option.textContent = device.label || `Speaker ${device.deviceId}`;
    option.addEventListener("click", () => {
      selectAudioOutputDevice(device.deviceId);
      speakerDropdownMenu.style.display = "none";
    });
    speakerDropdownMenu.appendChild(option);
  });
}

// Switch the microphone by requesting a new media stream with the selected deviceId
async function selectAudioInputDevice(deviceId) {
  if (localStream) {
    localStream.getAudioTracks().forEach((track) => track.stop());
  }
  try {
    if (deviceId) {
      localStream = await navigator.mediaDevices.getUserMedia({
        video: true,
        audio: { deviceId: { exact: deviceId } },
      });
    } else {
      localStream = await navigator.mediaDevices.getUserMedia({ video: true, audio: true });
    }
    webcamVideo.srcObject = localStream;
    webcamVideo.muted = true;
    console.log("Switched microphone to device:", deviceId || "default device");
  } catch (error) {
    console.error("Error switching microphone:", error);
    alert("Error switching microphone: " + error.message);
  }
}

// Change the speaker for the remote video element (if supported)
async function selectAudioOutputDevice(deviceId) {
  if (typeof remoteVideo.setSinkId === "function") {
    try {
      await remoteVideo.setSinkId(deviceId);
      console.log("Switched speaker to device:", deviceId);
    } catch (error) {
      alert("Error switching speaker: " + error.message);
    }
  } else {
    alert("Audio output device selection is not supported by your browser.");
  }
}

// Test the speaker by generating a test tone using the Web Audio API
function testSpeaker() {
  const audioContext = new (window.AudioContext || window.webkitAudioContext)();
  const oscillator = audioContext.createOscillator();
  oscillator.type = "sine";
  oscillator.frequency.setValueAtTime(440, audioContext.currentTime);
  oscillator.connect(audioContext.destination);
  oscillator.start();
  setTimeout(() => {
    oscillator.stop();
    audioContext.close();
  }, 1000);
}

// Event listener for microphone dropdown button
micDropdownButton.addEventListener("click", async (e) => {
  e.stopPropagation();
  micDropdownMenu.style.display = micDropdownMenu.style.display === "block" ? "none" : "block";
  await populateAudioInputDevices();
});

// Event listener for speaker dropdown button
speakerDropdownButton.addEventListener("click", async (e) => {
  e.stopPropagation();
  speakerDropdownMenu.style.display = speakerDropdownMenu.style.display === "block" ? "none" : "block";
  await populateAudioOutputDevices();
});

// Event listener for testing speaker output
testSpeakerButton.addEventListener("click", () => {
  testSpeaker();
});
