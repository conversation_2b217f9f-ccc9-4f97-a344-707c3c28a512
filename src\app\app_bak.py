from flask import (
    Flask,
    render_template,
    jsonify,
    request,
    send_from_directory,
    abort,
    redirect,
    url_for,
    session,
    flash,
)
import os
import boto3
import random
import xair_api
from botocore.exceptions import NoCredentialsError, PartialCredentialsError

from OpenSSL import crypto

# Create a key pair
key = crypto.PKey()
key.generate_key(crypto.TYPE_RSA, 2048)

# Create a self-signed cert
cert = crypto.X509()
cert.get_subject().C = "US"
cert.get_subject().ST = "MyState"
cert.get_subject().L = "MyCity"
cert.get_subject().O = "MyCompany"
cert.get_subject().OU = "MyOrganizationalUnit"
cert.get_subject().CN = "localhost"
cert.set_serial_number(1)
cert.gmtime_adj_notBefore(0)
cert.gmtime_adj_notAfter(10 * 365 * 24 * 60 * 60)  # 10 years validity
cert.set_issuer(cert.get_subject())
cert.set_pubkey(key)
cert.sign(key, "sha256")

# Save the key and cert
cert_file = "local.crt"
key_file = "local.key"

with open(cert_file, "wt") as f:
    f.write(crypto.dump_certificate(crypto.FILETYPE_PEM, cert).decode("utf-8"))

with open(key_file, "wt") as f:
    f.write(crypto.dump_privatekey(crypto.FILETYPE_PEM, key).decode("utf-8"))

print(f"Created {cert_file} and {key_file}")


def get_parameter(ssm_client, name):
    try:
        response = ssm_client.get_parameter(Name=name, WithDecryption=True)
        return response["Parameter"]["Value"]
    except (NoCredentialsError, PartialCredentialsError):
        print(
            "AWS credentials not found. Ensure the application is configured correctly."
        )
        # return('some_dummy_cant_configure_boto3')
        raise
    except Exception as e:
        print(f"Error retrieving parameter '{name}': {e}")
        # return ('some_dummy_cant_configure_boto3')
        raise


# Initialize Boto3 client for SSM
ssm_client = boto3.client("ssm", region_name="us-east-1")

# create the flask app
app = Flask(__name__, template_folder=".")

# Fetch secret key and password from SSM Parameter Store
app.secret_key = get_parameter(ssm_client, "/vse/app-secret-key")
PASSWORD = get_parameter(ssm_client, "/vse/app-password")

# Store active sessions (key -> list of users)
active_sessions = {}


def generate_session_key():
    return str(random.randint(100000, 999999))  # 6-digit numeric key


@app.route("/create-session", methods=["POST"])
def create_session():
    session_key = generate_session_key()
    active_sessions[session_key] = []
    return jsonify({"session_key": session_key})


@app.route("/validate-session", methods=["POST"])
def validate_session():
    data = request.get_json()
    session_key = data.get("session_key")

    if not session_key or not session_key.isdigit() or len(session_key) != 6:
        return jsonify({"valid": False, "message": "Invalid session key format"}), 400

    if session_key in active_sessions:
        return jsonify({"valid": True})

    # Re-add the session if it was lost
    active_sessions[session_key] = []
    return jsonify({"valid": True, "message": "Session key restored"})


@app.route("/join-session", methods=["POST"])
def join_session():
    data = request.get_json()
    session_key = data.get("session_key")

    if not session_key or not session_key.isdigit() or len(session_key) != 6:
        return jsonify({"error": "Invalid session key format"}), 400

    if session_key not in active_sessions:
        return jsonify({"error": "Session key does not exist"}), 404

    user_id = str(random.randint(10000000, 99999999))  # 8-digit numeric user ID
    active_sessions[session_key].append(user_id)

    return jsonify(
        {"message": "Joined session", "session_key": session_key, "user_id": user_id}
    )


@app.route("/assets/<path:filename>")
def custom_static(filename):
    return send_from_directory("assets", filename)


@app.route("/detail/<path:filename>")
def custom_detail(filename):
    return send_from_directory("detail", filename)


@app.route("/detail-light/<path:filename>")
def custom_detail_light(filename):
    return send_from_directory("detail-light", filename)


@app.route("/")
def home():
    # Check if the user is authenticated
    if "authenticated" not in session:
        return redirect(url_for("login"))  # Redirect to login if not authenticated
    return render_template("index.html")  # board-login


# Render other HTML pages dynamically
@app.route("/<page_name>")
def render_page(page_name):
    # Check if the user is authenticated
    if "authenticated" not in session:
        return redirect(url_for("login"))  # Redirect to login if not authenticated

    # Construct the file path
    file_path = f"{page_name}"

    # Check if the file exists
    if os.path.exists(file_path):
        return render_template(file_path)
    else:
        # If the file does not exist, return a 404 error
        abort(404)


# Login page
@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        password = request.form.get("password")
        if password == PASSWORD:
            session["authenticated"] = True  # Mark the user as authenticated
            return redirect(url_for("home"))  # Redirect to the home page
        else:
            flash("Incorrect password. Please try again.", "danger")
    return render_template("dummy-login.html")


# Logout route
@app.route("/logout")
def logout():
    session.pop("authenticated", None)  # Remove authentication from session
    return redirect(url_for("login"))  # Redirect to login page


# Handle 404 errors
@app.errorhandler(404)
def not_found(error):
    return render_template("404.html"), 404


# Health check route
@app.route("/health")
def health_check():
    """
    Health check endpoint to verify if the application is running.
    Returns a JSON response with status and an optional message.
    """
    return jsonify({"status": "healthy", "message": "Application is running"}), 200


@app.route("/set-ip", methods=["POST"])
def set_ip():
    kind_id = "X32"
    data = request.get_json()
    ip_address = data.get("ip")

    if not ip_address:
        return jsonify({"error": "No IP provided"}), 400

    global mixer
    mixer = xair_api.connect(kind_id, ip=ip_address, port=10023)

    try:
        mixer.__enter__()  # Establish connection

        # Get mute statuses for channels 1-32
        mute_status = {}
        fader_values = {}

        for i in range(1, 33):
            channel_id = str(i).zfill(2)

            # Fetch mute status
            mute_path = f"/ch/{channel_id}/mix/on"
            mute_value = mixer.query(mute_path)
            mute_status[i] = (
                int(mute_value[0]) if mute_value else 1
            )  # Default to unmuted if no response

            # Fetch fader value (0.0 - 1.0 scale)
            fader_path = f"/ch/{channel_id}/mix/fader"
            fader_value = mixer.query(fader_path)
            fader_values[i] = (
                round(float(fader_value[0]), 3) if fader_value else 0.0
            )  # Default to 0.0 if no response

        return jsonify(
            {
                "message": f"Connected successfully to {ip_address}",
                "mute_status": mute_status,
                "fader_values": fader_values,
            }
        )

    except Exception as e:
        return jsonify({"message": f"Failed to connect to {ip_address}: {e}"})


@app.route("/mute", methods=["POST"])
def mute_channel():
    data = request.get_json()
    mute_number = data.get("mute_number")
    action = data.get("action")

    if not mute_number or not action:
        return jsonify({"error": "Missing mute number or action"}), 400

    try:
        mute_number = int(mute_number)
        if mute_number < 1 or mute_number > 32:
            return jsonify({"error": "Invalid channel number"}), 400
    except ValueError:
        return jsonify({"error": "Invalid mute number format"}), 400

    mute_state = 0 if action == "mute" else 1

    try:
        mixer.send(f"/ch/{str(mute_number).zfill(2)}/mix/on", mute_state)
        print(f"Channel {mute_number} is now {action}")
        return jsonify(
            {
                "mute_number": mute_number,
                "action": action,
                "message": "Command sent successfully",
            }
        )
    except Exception as e:
        print(f"Error sending mute command: {e}")
        return jsonify({"error": "Failed to send mute command"}), 500


@app.route("/set-fader", methods=["POST"])
def set_fader():
    data = request.get_json()
    channel = data.get("channel")
    value = data.get("value")

    if not channel or value is None:
        return jsonify({"error": "Missing channel or value"}), 400

    try:
        channel = int(channel)
        value = float(value)

        if channel < 1 or channel > 32:
            return jsonify({"error": "Invalid channel number"}), 400
        if value < 0.0 or value > 1.0:
            return jsonify({"error": "Fader value out of range (0.0 - 1.0)"}), 400

        mixer.send(f"/ch/{str(channel).zfill(2)}/mix/fader", value)
        print(f"Set fader for channel {channel} to {value} (0.0 - 1.0 scale)")

        return jsonify(
            {
                "channel": channel,
                "value": value,
                "message": "Fader updated successfully",
            }
        )

    except Exception as e:
        return jsonify({"error": f"Failed to set fader: {e}"}), 500


if __name__ == "__main__":
    context = ("local.crt", "local.key")
    app.run(host="0.0.0.0", debug=True, ssl_context=context)
