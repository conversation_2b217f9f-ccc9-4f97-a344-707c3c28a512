# VSE Host Relay - Installation Guide

Quick installation guide for the VSE Host Relay application.

## Quick Start

### Windows
1. Double-click `setup.bat`
2. Follow the prompts
3. Application will start automatically

### macOS/Linux
1. Open terminal in this directory
2. Run: `./setup.sh`
3. Follow the prompts
4. Application will start automatically

## Manual Installation

### Prerequisites
- Python 3.7 or higher
- Network access to VSE server
- X32 mixer on local network

### Steps
1. Install Python dependencies:
   ```bash
   pip install -r requirements.txt
   ```

2. Run the application:
   ```bash
   python vse_host_relay.py
   ```

## Building Executable

To create a standalone executable:

```bash
python build_executable.py
```

This will create platform-specific executables in the `dist/` folder.

## Configuration

On first run, configure:
- **Server URL**: Your VSE server address
- **X32 IP**: Your mixer IP (or use Auto Discover)
- **Session Key**: From the web interface

## Troubleshooting

- Ensure Python 3.7+ is installed
- Check network connectivity to both server and X32
- Verify X32 is configured for OSC on port 10023
- Check firewall settings if auto-discovery fails

For detailed troubleshooting, see README.md.
