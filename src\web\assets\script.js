document.addEventListener('DOMContentLoaded', function () {
    const preloader = document.getElementById('preloader');
    const preloaderLogo = document.getElementById('preloader-logo');

    // Determine which logo to use based on URL
    // const logoPath = window.location.pathname.includes('/detail/')
    //     ? '../assets/icons-popups/VSE-logo-light-big.svg'
    //     : 'assets/icons-popups/VSE-logo-light-big.svg';
    const logoPath = /\/detail(-light)?\//.test(window.location.pathname)
        ? '../assets/icons-popups/VSE-logo-light-big.svg'
        : 'assets/icons-popups/VSE-logo-light-big.svg';
    // Set the logo source
    preloaderLogo.src = logoPath;

    // Show the logo with fade-in effect
    setTimeout(() => {
        preloaderLogo.style.opacity = '1';
    }, 100);

    // Ensure the preloader stays visible for at least 1 second
    const minDuration = 1000; // Minimum time to show the preloader (in milliseconds)
    const startTime = new Date().getTime();

    window.onload = function () {
        const loadTime = new Date().getTime() - startTime;
        const remainingTime = minDuration - loadTime;

        setTimeout(() => {
            preloader.style.opacity = '0';
            setTimeout(() => {
                preloader.style.display = 'none'; // Hide completely after fade-out
            }, 500);
        }, remainingTime > 0 ? remainingTime : 0); // Ensure minimum time
    };
});


document.addEventListener('DOMContentLoaded', () => {
    // Elements to track
    const popupSelection = document.getElementById('popup-selection');
    const popupSessionBarVideo2 = document.getElementById('popup-session-bar-video');

    // Function to save the states to sessionStorage
    function saveState() {
        const popupSelectionState = popupSelection.classList.contains('show') ? 'show' : '';
        const popupSessionBarState = popupSessionBarVideo2.classList.contains('activated') ? 'activated' : '';

        // Save the states to sessionStorage
        sessionStorage.setItem('popupSelectionState', popupSelectionState);
        sessionStorage.setItem('popupSessionBarState', popupSessionBarState);
    }

    // Function to restore the states from sessionStorage
    function restoreState() {
        const isFirstVisit = sessionStorage.getItem('firstVisit') === null; // Check if it's the first visit
        const savedPopupSelectionState = sessionStorage.getItem('popupSelectionState');
        const savedPopupSessionBarState = sessionStorage.getItem('popupSessionBarState');

        if (isFirstVisit) {
            // First visit: Show both popups
            popupSelection.classList.add('show');
            popupSessionBarVideo2.classList.add('activated');
            popupSessionBarVideo2.style.bottom = '152px';

            // Mark that the user has visited
            sessionStorage.setItem('firstVisit', 'false');
        } else {
            // Restore popup-selection state
            if (savedPopupSelectionState === 'show') {
                popupSelection.classList.add('show');
            } else {
                popupSelection.classList.remove('show');
            }

            // Restore popup-session-bar-video state
            if (savedPopupSessionBarState === 'activated') {
                popupSessionBarVideo2.classList.add('activated');
                popupSessionBarVideo2.style.bottom = popupSelection.classList.contains('show') ? '152px' : '0';
            } else {
                popupSessionBarVideo2.classList.remove('activated');
                popupSessionBarVideo2.style.bottom = '-152px';
            }
        }
    }

    // Add event listeners to track state changes
    const observer = new MutationObserver(() => {
        saveState(); // Save state whenever attributes change
    });

    observer.observe(popupSelection, { attributes: true, attributeFilter: ['class'] });
    observer.observe(popupSessionBarVideo2, { attributes: true, attributeFilter: ['class'] });

    // Restore state on page load
    restoreState();
});

// full screen storage
// document.addEventListener("DOMContentLoaded", () => {
//     // Create the button dynamically
//     const fullscreenBtn = document.createElement("button");
//     fullscreenBtn.id = "fullscreen-btn";
//     fullscreenBtn.textContent = "Enable Fullscreen";
//     document.body.appendChild(fullscreenBtn);

//     const enterFullscreen = () => {
//         document.documentElement.requestFullscreen()
//             .then(() => {
//                 console.log("Fullscreen enabled");
//                 fullscreenBtn.style.display = "none"; // Hide button
//                 localStorage.setItem("fullscreenEnabled", "true"); // Save preference
//             })
//             .catch((error) => {
//                 console.error("Failed to enable fullscreen:", error.message);
//                 alert("Fullscreen permission denied or unsupported.");
//             });
//     };

//     // Check user preference
//     const fullscreenEnabled = localStorage.getItem("fullscreenEnabled");

//     if (fullscreenEnabled === "true") {
//         // Notify the user that fullscreen was previously enabled
//         fullscreenBtn.textContent = "Re-enable Fullscreen";
//         fullscreenBtn.style.display = "block";
//     }

//     fullscreenBtn.addEventListener("click", enterFullscreen);

//     // Monitor fullscreen exit and re-show button if exited
//     document.addEventListener("fullscreenchange", () => {
//         if (!document.fullscreenElement) {
//             console.log("Exited fullscreen mode");
//             fullscreenBtn.style.display = "block";
//             localStorage.setItem("fullscreenEnabled", "false"); // Update preference
//         }
//     });
// });
// end fullscreen
// ALL buttons clicks toggle bg and shadow Working
$(document).ready(function () {
    $(".btn-block:not(#info, #user, #screen, #video, #chat)").click(function () {
        // Toggle classes for background and box shadow
        $(this).toggleClass("active-btn-background active-btn-shadow");
    });
});

// send faders

document.addEventListener("DOMContentLoaded", function () {
    const fadersButton = document.querySelector('.faders');
    const fadersOptions = document.querySelectorAll('.send-faders-active-options');
    const activatedFaders = document.querySelector('.activated-faders');
    const needleScreen = document.querySelectorAll('.needle-screen-one');
    const mixingItems = document.querySelectorAll('.sidebar-item.mixing');

    // Only add event listener if all elements exist
    if (fadersButton) {
        fadersButton.addEventListener('click', function () {
            if (fadersOptions) fadersOptions.forEach(option => option.classList.toggle('show-flex'));
            if (activatedFaders) activatedFaders.classList.toggle('show-flex');
            if (needleScreen) needleScreen.forEach(screen => screen.classList.toggle('hide-if-faders'));
            if (mixingItems) mixingItems.forEach(item => item.classList.toggle('hide-if-faders'));
        });
    }
});


// Tabs switcher on HOME screen

$(document).ready(function () {
    $(".screen-channels-item").click(function () {
        var tabId = $(this).attr("id");
        var tabNumber = tabId.replace("channel-select-", "");

        // Remove active class from all tab selectors
        $(".screen-channels-item").removeClass("tab-active");
        // Add active class to the clicked tab selector
        $(this).addClass("tab-active");

        // Remove active class from all tab contents
        $(".main-item.draggers").removeClass("tab-active");
        // Add active class to the corresponding tab content
        $("#channel-" + tabNumber).addClass("tab-active");
    });
});

// MUTE disabled
$(document).ready(function () {
    $("#mute-locks").click(function () {
        // Toggle the state of elements with the class 'muted'
        $(".muted").toggleClass("disabled");

        // Disable contenteditable for div elements
        $(".muted").each(function () {
            var isDisabled = $(this).hasClass("disabled");
            if (isDisabled) {
                $(this).removeAttr("contenteditable");
            } else {
                $(this).attr("contenteditable", "false");
            }
        });
    });
});

// SOLOS HOME

$(document).ready(function () {
    $("#solo-on").click(function () {
        // Show all elements with class mute-solo-item
        $(".soloed").show();
        
        // Set the width of all elements with class muted to 56px
        $(".muted").css("width", "56px");

        // Remove active classes from solo-off
        $("#solo-off").removeClass("active-btn-background active-btn-shadow");

        // Add active classes to solo-on
        $(this).addClass("active-btn-background active-btn-shadow");
    });

    $("#solo-off").click(function () {
        // Remove active classes from solo-on
        $("#solo-on").removeClass("active-btn-background active-btn-shadow");

        // Hide all elements with class mute-solo-item
        $(".soloed").hide();

        // Add active classes to solo-off
        $(this).addClass("active-btn-background active-btn-shadow");

        // Set the width of all elements with class muted to 100%
        $(".muted").css("width", "100%");
    });
});


// FADER HOME hidden for certain screen
$(document).ready(function () {
    // When a div with class 'no-faders-tab' is clicked
    $(".no-faders-tab").click(function () {
        // Hide the element with id 'faders'
        $("#faders").css("visibility", "hidden");
    });

    // When a div with class 'yes-faders-tab' is clicked
    $(".yes-fader-tab").click(function () {
        // Show the element with id 'faders'
        $("#faders").css("visibility", "visible");
    });

    // When the element with id 'faders' is clicked
    $("#faders").click(function () {
        // Toggle the disabled state of elements with class 'no-faders-tab'
        $(".no-faders-tab").toggleClass("disabled");
    });
});


// DETAIL PAGE

// Tabs switcher Detail

$(document).ready(function () {
    // Initialize EQ tab first by temporarily activating it
    function initializeEQFirst() {
        // Store the currently active tab
        const activeTabId = $(".details-tab-nav.active-detail-tab").attr("id");
        
        // Only proceed if EQ tab is not already active
        if (activeTabId !== "eq-tab") {
            // Temporarily activate EQ tab (without animation)
            $(".details-tab-nav").removeClass("active-detail-tab");
            $("#eq-tab").addClass("active-detail-tab");
            
            $(".tab-content-hidden").removeClass("active-detail-tab");
            $("#eq-tab-content").addClass("active-detail-tab");
            
            // Force redraw of EQ visualizer
            if (window.eqVisualizer && typeof window.eqVisualizer.draw === 'function') {
                window.eqVisualizer.draw();
            }
            
            // Wait a short time for initialization to complete
            setTimeout(function() {
                // Switch back to the original tab
                if (activeTabId) {
                    $(".details-tab-nav").removeClass("active-detail-tab");
                    $("#" + activeTabId).addClass("active-detail-tab");
                    
                    $(".tab-content-hidden").removeClass("active-detail-tab");
                    $("#" + activeTabId + "-content").addClass("active-detail-tab");
                }
            }, 300);
        }
    }
    
    // Run the initialization after a short delay
    setTimeout(initializeEQFirst, 500);
});

$(document).ready(function () {
    $(".details-tab-nav").click(function () {
        var tabId = $(this).attr("id");
        var tabNumber = tabId.replace("-tab", ""); // Remove the "-tab" suffix to get the corresponding tab number

        // Remove active class from all tab navigation items
        $(".details-tab-nav").removeClass("active-detail-tab");
        // Add active class to the clicked tab navigation item
        $(this).addClass("active-detail-tab");

        // Remove active class from all tab content elements
        $(".tab-content-hidden").removeClass("active-detail-tab");
        // Add active class to the corresponding tab content element
        $("#" + tabId + "-content").addClass("active-detail-tab");
        
        // Special handling for EQ tab
        if (tabId === "eq-tab") {
            // Force redraw of EQ visualizer when EQ tab is clicked
            setTimeout(function() {
                if (window.eqVisualizer && typeof window.eqVisualizer.draw === 'function') {
                    // Force reflow using a more reliable method
                    const movementBoard = document.querySelector('.movement-board');
                    if (movementBoard) {
                        // Force reflow by getting computed style and modifying layout
                        const width = movementBoard.offsetWidth;
                        movementBoard.style.width = (width - 1) + 'px';
                        
                        // Force browser to process the change
                        void movementBoard.offsetWidth;
                        
                        // Restore original width
                        setTimeout(() => {
                            movementBoard.style.width = width + 'px';
                            
                            // Redraw after width is restored
                            setTimeout(() => {
                                // Redraw the visualizer
                                window.eqVisualizer.draw();
                                
                                // Update marker positions
                                const markers = document.querySelectorAll('.filter-marker');
                                markers.forEach((marker, index) => {
                                    if (window.eqVisualizer.bands[index]) {
                                        const freq = window.eqVisualizer.bands[index].freq;
                                        const minLog = Math.log10(20);
                                        const maxLog = Math.log10(20000);
                                        const normalizedX = (Math.log10(freq) - minLog) / (maxLog - minLog);
                                        marker.style.left = (normalizedX * movementBoard.clientWidth - 10) + 'px';
                                    }
                                });
                                
                                // Remove existing marker overlays and recreate them
                                document.querySelectorAll('.marker-overlay').forEach(overlay => overlay.remove());
                                
                                // Use a separate function to create marker overlays
                                function createMarkerOverlays() {
                                    const visualizer = window.eqVisualizer;
                                    const response = visualizer.calculateResponse();
                                    
                                    // Create overlays for each enabled band
                                    visualizer.bands.forEach((band, index) => {
                                        if (!band.enabled) return;
                                        
                                        // Calculate position based on frequency and response
                                        const x = visualizer.freqToX(band.freq);
                                        let closestIndex = 0;
                                        let minDistance = Number.MAX_VALUE;
                                        
                                        for (let i = 0; i < visualizer.frequencies.length; i++) {
                                            const freqX = visualizer.freqToX(visualizer.frequencies[i]);
                                            const distance = Math.abs(freqX - x);
                                            if (distance < minDistance) {
                                                minDistance = distance;
                                                closestIndex = i;
                                            }
                                        }
                                        
                                        const y = visualizer.dbToY(response[closestIndex]);
                                        
                                        // Create overlay element
                                        const overlay = document.createElement('div');
                                        overlay.className = `marker-overlay marker-overlay-${index + 1}`;
                                        overlay.setAttribute('data-band-index', index);
                                        overlay.style.position = 'absolute';
                                        overlay.style.width = '20px';
                                        overlay.style.height = '20px';
                                        overlay.style.borderRadius = '50%';
                                        overlay.style.left = `${x - 10}px`;
                                        overlay.style.top = `${y - 10}px`;
                                        overlay.style.cursor = 'ns-resize';
                                        overlay.style.zIndex = '10';
                                        overlay.style.border = '2px solid transparent';
                                        overlay.style.backgroundColor = 'rgba(255, 149, 0, 0.2)';
                                        overlay.style.transition = 'background-color 0.2s, transform 0.1s';
                                        
                                        movementBoard.appendChild(overlay);
                                        
                                        // Add mouse event listeners for vertical gain adjustment
                                        overlay.addEventListener('mousedown', function(e) {
                                            e.preventDefault();
                                            e.stopPropagation();
                                            
                                            const bandIndex = parseInt(this.getAttribute('data-band-index'));
                                            const band = visualizer.bands[bandIndex];
                                            
                                            // Skip for bands that don't support gain adjustment
                                            if (band.type === 'highpass' || band.type === 'lowpass') {
                                                return;
                                            }
                                            
                                            const initialY = e.clientY;
                                            const initialGain = band.gain;
                                            const boardHeight = movementBoard.clientHeight;
                                            
                                            function moveHandler(moveEvent) {
                                                // Vertical movement for gain (up = increase, down = decrease)
                                                const dy = initialY - moveEvent.clientY;
                                                
                                                // Scale movement to gain range
                                                // Full height movement = -15 to +15 dB (30dB range)
                                                const gainDelta = (dy / boardHeight) * 30;
                                                let newGain = Math.max(-15, Math.min(15, initialGain + gainDelta));
                                                
                                                // Update band gain
                                                visualizer.updateBand(bandIndex, { gain: newGain });
                                                
                                                // Update the marker overlay position
                                                updateOverlayPosition(overlay, bandIndex);
                                            }
                                            
                                            function upHandler() {
                                                document.removeEventListener('mousemove', moveHandler);
                                                document.removeEventListener('mouseup', upHandler);
                                            }
                                            
                                            document.addEventListener('mousemove', moveHandler);
                                            document.addEventListener('mouseup', upHandler);
                                        });
                                    });
                                }
                                
                                // Function to update a single marker overlay position
                                function updateOverlayPosition(overlay, bandIndex) {
                                    const visualizer = window.eqVisualizer;
                                    const band = visualizer.bands[bandIndex];
                                    const response = visualizer.calculateResponse();
                                    
                                    const x = visualizer.freqToX(band.freq);
                                    
                                    // Find closest response value
                                    let closestIndex = 0;
                                    let minDistance = Number.MAX_VALUE;
                                    for (let i = 0; i < visualizer.frequencies.length; i++) {
                                        const freqX = visualizer.freqToX(visualizer.frequencies[i]);
                                        const distance = Math.abs(freqX - x);
                                        if (distance < minDistance) {
                                            minDistance = distance;
                                            closestIndex = i;
                                        }
                                    }
                                    
                                    const y = visualizer.dbToY(response[closestIndex]);
                                    overlay.style.left = `${x - 10}px`;
                                    overlay.style.top = `${y - 10}px`;
                                }
                                
                                // Create the marker overlays
                                createMarkerOverlays();
                                
                                // Make updateOverlayPosition available globally
                                window.updateOverlayPosition = updateOverlayPosition;
                                
                                // Add touch functionality if available
                                if (typeof addTouchFunctionalityToMarkers === 'function') {
                                    addTouchFunctionalityToMarkers();
                                }
                            }, 50);
                        }, 50);
                    }
                }
            }, 100);
        }
    });
    
    // If EQ tab is active by default, trigger a redraw
    if ($("#eq-tab").hasClass("active-detail-tab")) {
        setTimeout(function() {
            if (window.eqVisualizer && typeof window.eqVisualizer.draw === 'function') {
                window.eqVisualizer.draw();
            }
        }, 500);
    }
});
$(document).ready(function () {
    $(".orange-detail-btn").not(".auto-flex .orange-detail-btn").click(function () {
        // Toggle classes for background and box shadow
        $(this).toggleClass("detail-btn-active");
    });
});

// EFFECTS inner tabs
$(document).ready(function () {
    initializeInnerTabs();
});

function initializeInnerTabs() {
    $(".inner-tab-control .btn-block").click(function () {
        var innerTabId = $(this).attr("id");

        // Find the closest tab button container
        var buttonContainer = $(this).closest('.effect-column.inner-tab-control');

        // Find the larger parent for the content swapping
        var parentContainer = $(this).closest('.inactive_effect-screen, .active_effect-screen');

        // Check if the clicked button is already active
        if ($(this).hasClass("active-detail-tab")) {
            return; // Do nothing if the button is already active
        }

        // Remove active classes from all buttons within the buttonContainer (closest column)
        buttonContainer.find(".btn-block").removeClass("active-detail-tab active-btn-background active-btn-shadow").removeAttr("disabled");

        // Add active classes to the clicked button and disable it
        $(this).addClass("active-detail-tab active-btn-background active-btn-shadow").attr("disabled", true);

        // Remove active class from all tab content elements within the parentContainer (section)
        parentContainer.find(".tab-inner-content-hidden").removeClass("active-detail-tab");

        // Add active class to the corresponding tab content element
        $("#" + innerTabId + "-content").addClass("active-detail-tab");
    });
}


// DETAILS name fetch
function updateChannelNameSet() {
    // Find the element with class 'scrolled-active' under #channel-sets
    const activeElement = document.querySelector('#channel-sets .scrolled-active .channel_name_set');
    // Get the content of the active element
    const content = activeElement ? activeElement.textContent : '';
    // Update the 'channel-name-set' div with the content
    document.getElementById('channel-name-set').textContent = content;
}

// Call the function to set the initial content
updateChannelNameSet();

// Add event listeners to the children of #channel-sets
document.querySelectorAll('#channel-sets .scrolled').forEach(item => {
    item.addEventListener('click', function () {
        // Remove 'scrolled-active' class from all elements under #channel-sets
        document.querySelectorAll('#channel-sets .scrolled').forEach(el => el.classList.remove('scrolled-active'));
        // Add 'scrolled-active' class to the clicked element
        this.classList.add('scrolled-active');
        // Update the channel name set
        updateChannelNameSet();
    });
});


// EFFECTS Drag
// Existing code for draggable functionality






// AUTO MIX X AND Y
document.addEventListener('DOMContentLoaded', () => {
    const autoFlexContainer = document.querySelector('.auto-flex');
    const buttons = autoFlexContainer.querySelectorAll('.detail-btn');
    const mixBoard = document.querySelector('.auto-mix-board-xy');
    const boardX = mixBoard.querySelector('.board-x');
    const boardY = mixBoard.querySelector('.board-y');
    const boardPoly = mixBoard.querySelector('img[alt="mixer"]');

    function updateBoardVisibility(button) {
        if (button.id === 'auto-1-config') {
            mixBoard.style.visibility = 'hidden';
            boardX.style.display = 'none';
            boardY.style.display = 'none';
            boardPoly.style.display = 'none';
        } else if (button.id === 'auto-2-config') {
            mixBoard.style.visibility = 'visible';
            boardX.style.display = 'block';
            boardY.style.display = 'none';
            boardPoly.style.display = 'block';
        } else if (button.id === 'auto-3-config') {
            mixBoard.style.visibility = 'visible';
            boardX.style.display = 'none';
            boardY.style.display = 'block';
            boardPoly.style.display = 'block';
        }
    }

    buttons.forEach(button => {
        button.addEventListener('click', () => {
            if (!button.classList.contains('detail-btn-active')) {
                buttons.forEach(btn => btn.classList.remove('detail-btn-active'));
                button.classList.add('detail-btn-active');
                updateBoardVisibility(button);
            }
        });
    });

    // Initial state
    updateBoardVisibility(document.querySelector('#auto-1-config'));
});

document.addEventListener('DOMContentLoaded', () => {
    const triangleAutomix = document.querySelector('.triangle-automix');
    const wheelWeight = document.getElementById('wheel-weight');

    function updateTrianglePosition() {
        const transform = wheelWeight.style.transform;
        const rotation = parseFloat(transform.replace('rotate(', '').replace('deg)', ''));
        const bottom = ((rotation + 140) / 280) * 65; // Calculate bottom percentage
        triangleAutomix.style.bottom = `${bottom}%`;
    }

    // Initial update
    updateTrianglePosition();

    // Observe changes to the knob's transform style
    const observer = new MutationObserver(updateTrianglePosition);
    observer.observe(wheelWeight, { attributes: true, attributeFilter: ['style'] });
});

// Function to hide specified elements SINGLE DETAILS
// Function to hide specified elements
function hideElements() {
    document.querySelector('.swapper-mode.swapper-mode-3').style.visibility = 'hidden';
    document.querySelector('.wheel-item.attack-dyn').style.visibility = 'hidden';
    document.querySelector('.wheel-item.dyn-hold').style.visibility = 'hidden';
    document.querySelector('.wheel-item.dyn-release').style.visibility = 'hidden';
}

// Function to show specified elements
function showElements() {
    document.querySelector('.swapper-mode.swapper-mode-3').style.visibility = 'visible';
    document.querySelector('.wheel-item.attack-dyn').style.visibility = 'visible';
    document.querySelector('.wheel-item.dyn-hold').style.visibility = 'visible';
    document.querySelector('.wheel-item.dyn-release').style.visibility = 'visible';
}

// Function to set up the MutationObserver
function setupObserver() {
    const compAuto = document.getElementById('comp-auto');
    const observer = new MutationObserver(function (mutations) {
        mutations.forEach(function (mutation) {
            if (mutation.type === 'attributes' && mutation.attributeName === 'class') {
                if (compAuto.classList.contains('scrolled-active')) {
                    hideElements();
                } else {
                    showElements();
                }
            }
        });
    });

    observer.observe(compAuto, {
        attributes: true
    });
}

// Initialize the observer setup
setupObserver();



$(document).ready(function () {
    // Ensure this works independently of the other click function
    $(".select-drag-btns-eq .detail-btn").click(function () {
        // Remove the active state from all buttons
        $(".select-drag-btns-eq .detail-btn").removeClass("detail-btn-active");

        // Add the active state only to the clicked button
        $(this).addClass("detail-btn-active");
    });
});


// tab 4
// move lines and connect the buttons
$(document).ready(function () {
    let activeLineClass = '.wrap_line_low'; // Default active line
    let isDraggingWheel = false; // Flag for wheel dragging

    // Function to make lines draggable within the parent container with restrictions
    function makeDraggable(element, maxLeft) {
        var isDraggingLine = false;
        var startX, startLeft;

        $(element).on('mousedown touchstart', function (e) {
            if (isDraggingWheel) return; // Prevent line dragging if the wheel is being controlled

            isDraggingLine = true;
            startX = e.pageX || e.originalEvent.touches[0].pageX;
            startLeft = parseInt($(this).css('left'), 10);
        });

        $(document).on('mousemove touchmove', function (e) {
            if (isDraggingLine) {
                var moveX = e.pageX || e.originalEvent.touches[0].pageX;
                var deltaX = moveX - startX;
                var newLeft = startLeft + deltaX;

                // Restrict movement between 0 and maxLeft
                newLeft = Math.max(0, Math.min(newLeft, maxLeft));
                $(element).css('left', newLeft + 'px');

                // Update the wheel and path while dragging the line
                updateWheelAndPath($(element), $('#wheel-ch1-freq'), '.wheel-freq-cont .progress-path');
            }
        });

        $(document).on('mouseup touchend', function () {
            isDraggingLine = false;
        });
    }

    // Activate the correct button and line
    function activateButtonAndLine(buttonId, lineClass, maxLeft) {
        $(".select-drag-btns-eq .detail-btn").removeClass("detail-btn-active");
        $(".movement-board .wrapper_lines").removeClass("active-line");

        $("#" + buttonId).addClass("detail-btn-active");
        $("." + lineClass).addClass("active-line");

        activeLineClass = '.' + lineClass; // Update the active line class
        makeDraggable('.' + lineClass, maxLeft); // Make line draggable
    }

    // Default state (Low button and corresponding line are active)
    activateButtonAndLine('channel-one-eq-low', 'wrap_line_low', 366);

    // Handle button click to activate corresponding line
    $(".select-drag-btns-eq .detail-btn").click(function () {
        var buttonId = $(this).attr('id');
        var lineClass = $(this).attr('id').replace('channel-one-eq-', 'wrap_line_');
        var maxLeft = (lineClass === 'wrap_line_cutlo') ? 366 : 848;  // Adjust maxLeft for other lines
        activateButtonAndLine(buttonId, lineClass, maxLeft);
    });

    // Handle line click to activate corresponding button
    $(".movement-board .wrapper_lines").click(function () {
        var lineClass = $(this).attr('class').split(' ').filter(c => c.startsWith('wrap_line_'))[0];
        var buttonId = lineClass.replace('wrap_line_', 'channel-one-eq-');
        var maxLeft = (lineClass === 'wrap_line_cutlo') ? 366 : 848;
        activateButtonAndLine(buttonId, lineClass, maxLeft);
    });

    // Wheel connection to lines
    const maxLeft = 848; // Maximum left position for the line
    const minLeft = 0; // Minimum left position for the line
    const maxDegrees = 140; // Maximum rotation for the wheel
    const minDegrees = -140; // Minimum rotation for the wheel
    const maxDashOffset = 208.4; // Max stroke-dashoffset for the path

    // Map rotation angle to line position (0 to 848px)
    function mapRotationToAxis(angle) {
        const percentage = (angle - minDegrees) / (maxDegrees - minDegrees);
        return percentage * (maxLeft - minLeft) + minLeft;
    }

    // Map line position (0 to 848px) to rotation angle (-140 to 140 degrees)
    function mapAxisToRotation(x) {
        const percentage = (x - minLeft) / (maxLeft - minLeft);
        return percentage * (maxDegrees - minDegrees) + minDegrees;
    }

    // Update the progress path based on the wheel's rotation
    function updateProgressPath(angle, pathElement) {
        const percentage = (angle + 140) / 280;
        const pathLength = maxDashOffset;
        const offset = pathLength - (percentage * pathLength);
        $(pathElement).css('stroke-dashoffset', offset);
    }

    // Update the wheel and path based on the line's position
    function updateWheelAndPath(lineElement, wheelElement, pathElement) {
        const lineLeft = parseFloat($(lineElement).css('left'));
        const rotationAngle = mapAxisToRotation(lineLeft);

        $(wheelElement).css('transform', `rotate(${rotationAngle}deg)`);
        updateProgressPath(rotationAngle, pathElement);
    }

    // Update the line based on the wheel's rotation
    function updateLineFromWheel(wheelElement, lineElement, pathElement) {
        const transformStyle = $(wheelElement).css('transform');
        const match = transformStyle.match(/matrix\(([-\d.,\s]+)\)/);

        if (!match) {
            console.log('No valid matrix or rotation found on the wheel element.');
            return;
        }

        const rotation = getRotationFromMatrix(transformStyle);
        const lineX = mapRotationToAxis(rotation);

        $(lineElement).css('left', `${lineX}px`);
        updateProgressPath(rotation, pathElement);
    }

    // Extract rotation from matrix
    function getRotationFromMatrix(matrix) {
        const values = matrix.match(/matrix\(([-\d.,\s]+)\)/)[1].split(', ');
        const a = parseFloat(values[0]); // cos(theta)
        const b = parseFloat(values[1]); // sin(theta)

        const rotation = Math.atan2(b, a) * (180 / Math.PI);
        return rotation;
    }

    // Make the wheel draggable and update the active line, without affecting Y-movement of the mouse
    // Function to make the wheel draggable and update the line based only on rotation
    function makeWheelDraggable(wheelElement, pathElement) {
        let isRotating = false; // Track whether we're rotating the wheel

        $(wheelElement).on('mousedown touchstart', function (e) {
            isRotating = true;
        });

        $(document).on('mousemove touchmove', function (e) {
            if (isRotating) {
                // Extract the wheel's rotation angle without relying on mouse movement
                const transformStyle = $(wheelElement).css('transform');
                const match = transformStyle.match(/matrix\(([-\d.,\s]+)\)/);

                if (match) {
                    const rotation = getRotationFromMatrix(transformStyle);

                    // Rotate the wheel based on its actual angle, ignore mouse Y movement
                    updateLineFromWheel(wheelElement, $(activeLineClass), pathElement);
                }
            }
        });

        $(document).on('mouseup touchend', function () {
            isRotating = false;
        });
    }

    // Initialize for the specific wheel and path
    makeWheelDraggable('#wheel-ch1-freq', '.wheel-freq-cont .progress-path');
});

// waves animations for EQ

$(document).ready(function () {
    let isDraggingLine = false;
    let startX, startY, startHeight, waveHeight = 0;
    const waveElement = $('.wrap_line_low_wave');
    const waveMaxHeight = 150; // Maximum height for the wave above or below the line

    // Function to move the line and update the wave
    function makeLineDraggable(lineElement, maxLeft) {
        $(lineElement).on('mousedown touchstart', function (e) {
            isDraggingLine = true;
            startX = e.pageX || e.originalEvent.touches[0].pageX;
            startY = e.pageY || e.originalEvent.touches[0].pageY;
            startHeight = waveHeight; // Track starting height
        });

        $(document).on('mousemove touchmove', function (e) {
            if (isDraggingLine) {
                const moveX = e.pageX || e.originalEvent.touches[0].pageX;
                const moveY = e.pageY || e.originalEvent.touches[0].pageY;

                // Horizontal movement
                const deltaX = moveX - startX;
                let newLeft = parseInt($(lineElement).css('left'), 10) + deltaX;
                newLeft = Math.max(0, Math.min(newLeft, maxLeft)); // Clamp left between 0 and maxLeft
                $(lineElement).css('left', newLeft + 'px');

                // Vertical movement for wave
                const deltaY = startY - moveY;
                waveHeight = Math.max(-waveMaxHeight, Math.min(waveMaxHeight, startHeight + deltaY));
                waveElement.css('height', Math.abs(waveHeight) + 'px');
                waveElement.css('bottom', waveHeight >= 0 ? '150px' : `calc(150px - ${Math.abs(waveHeight)}px)`);

                // Update wave position 100px ahead of the line
                waveElement.css('left', (newLeft + 100) + 'px');
            }
        });

        $(document).on('mouseup touchend', function () {
            isDraggingLine = false;
        });
    }

    // Initialize line movement for the first line (active-line)
    makeLineDraggable('.wrap_line_low', 848);
});
