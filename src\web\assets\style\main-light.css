  /* cream f2ede9 - charocal #2d2d2f */
  html,
  * {
      box-sizing: border-box;
  }
.arrow-pop-btn img {
    filter: brightness(0);
}
.volume-display {
    position: absolute;
    bottom: 25%;
    left: 50%;
    transform: translateX(-50%);
    width: 385px;
    text-align: center;
    z-index: 1000;
    border-radius: 20px;
    display: flex;
    gap: 20px;
    align-items: center;
    background: #111;
    padding: 20px 15px 15px 15px;
}
  body,
  a,
  p,
  div {
      font-family: "Open Sans", sans-serif;
  }

  body {
      background: #eeeded;
      margin: 0;
      padding: 0;
  }

  .page-wrapper {
      padding: 25px 20px;
      max-height: 100vh;
      max-width: 100%;
  }

  .container {
      display: flex;
      gap: 25px;
      flex-wrap: nowrap;
  }

  .left-sidebar {
      width: 10%;
      text-align: center;
  }

  .main {
      width: 90%;
  }

  /* buttons */

  .btn-block {
      border-radius: 4px;
      border: 3px solid #2d2d2f;
      background: #fff;
      color: #2d2d2f;
      text-transform: uppercase;
      font-size: 14px;
      height: 38px;
      width: 42px;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
  }

  .btn-block img {
      filter: invert(1);
  }

  #wifi img {
      filter: invert(0);
  }

  .btn-block p,
  .left-sidebar p {
      color: #2d2d2f;
      font-weight: 600;
      text-transform: uppercase;
      font-size: 14px;
  }

  /* main part header */
  .main-item.navbar {
      display: flex;
      justify-content: space-around;
      align-items: center;
  }

  .main-item.navbar a {
      display: flex;
      flex-direction: column;
      gap: 10px;
      justify-content: center;
      align-items: center;
      text-decoration: none;
      text-transform: uppercase;
      color: #2d2d2f;
      font-size: 14px;
      font-weight: bold;
  }

  .navbar-item-img {
      background: #000;
      height: 40px;
      width: 40px;
      border-radius: 100px;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 0 0 3px #00000014;
  }

  .navbar-item-img.active_item {
      background: #fff;
      box-shadow: none;
  }

  .scenes img {
      margin-top: 7px;
      margin-right: 7px;
  }

  .meters img {
      margin-bottom: 7px;
      margin-left: 5px;
      width: 32px;
  }

  /* sidebar items */
  .sidebar-item.controls {
      width: 100%;
      max-width: 130px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin: 15px auto;
      border: 3px solid #2d2d2f;
      border-radius: 4px;
      gap: 0 !important;
  }

  .sidebar-item.controls .btn-block {
      border: none;
      width: 32%;
      border-radius: 0;
  }

  .sidebar-item.controls .btn-block:nth-of-type(2) {
      border-left: 3px solid #2d2d2f;
      border-right: 3px solid #2d2d2f;
      border-radius: 0 !important;
      width: 36%;
      margin: 0;
  }

  /* siebar - 4 buttons */
  .sidebar-item.addons {
      width: 100%;
      max-width: 134px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 20px auto;
      gap: 20px;
  }

  .sidebar-item.addons .btn-block {
      width: 42%;
      max-width: 56px;
      max-height: 36px;
  }

  /* fader */
  .activated-faders {
      width: 100%;
      max-width: 130px;
      min-height: 151px;
      margin: auto;
      background: #2d2d2f;
      box-shadow: 0 0 9.2px 1px #2d2d2f;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      gap: 10px;
      display: none;
  }

  .activated-faders img {
      width: 100%;
      max-width: 60%;
  }

  .btn-block.sidebar-item.faders {
      width: 100%;
      max-width: 134px;
      height: 46px;
      display: flex;
      align-items: center;
      margin: 20px auto;
  }

  .btn-block.sidebar-item.faders p {
      font-size: 12px;
  }

  /* 1-2 when fader activated */
  .send-faders-active-options {
      width: 100%;
      max-width: 120px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      display: none;
  }

  .send-faders-active {
      width: 46%;
      max-width: 55px;
      height: 46px;
      margin-bottom: 20px;
  }

  /* X Y auto mixing */
  .sidebar-item.mixing {
      width: 100%;
      max-width: 134px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 45px auto;
  }

  .sidebar-item.mixing p {
      width: 100%;
  }

  .btn-block.mixing-item {
      width: 46%;
      max-width: 55px;
      height: 46px;
  }

  /* mute groups */
  .sidebar-item.mute-group {
      width: 100%;
      max-width: 134px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      flex-wrap: wrap;
      margin: 20px auto;
      gap: 15px;
  }

  .sidebar-item.mute-group .btn-block {
      width: 46%;
      max-width: 55px;
      height: 55px;
  }

  .sidebar-item.mute-group p {
      width: 100%;
      margin-bottom: 0;
  }

  .sidebar-item.mute-group .last-mute {
      width: 100%;
      max-width: 100%;
  }

  /* solos  */
  .sidebar-item.show-solos {
      width: 100%;
      max-width: 134px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-direction: column;
      margin: 20px auto;
  }

  .sidebar-item.show-solos p {
      width: 100%;
  }

  .show-solos-item-wrap {
      max-width: 134px;
      width: 100%;
      display: flex;
      border: 3px solid #2d2d2f;
      border-radius: 4px;
  }

  .show-solos-item {
      border: none;
      width: 50%;
      border-radius: 0;
      background: #eeeded;
      color: #2d2d2f;
      text-transform: uppercase;
      font-size: 14px;
      height: 38px;
      cursor: pointer;
      display: flex;
      justify-content: center;
      align-items: center;
  }

  .show-solos-item .btn-block:last-of-type {
      border-left: 3px solid #2d2d2f;
      border-radius: 0 !important;
      margin: 0;
  }

  /* lock mutes big */
  .btn-block.sidebar-item.lock-mute {
      width: 100%;
      max-width: 134px;
      height: 46px;
      display: flex;
      align-items: center;
      flex-wrap: wrap;
      margin: 20px auto;
  }

  .btn-block.sidebar-item.lock-mute p {
      margin: 0;
  }

  /* MAIN */
  /* board equalizer */
  .screen-channels-wrapper {
      margin-top: 30px;
      display: flex;
      justify-content: space-between;
      max-width: 97%;
  }

  .screen-channels-item {
      max-width: 96px;
      text-align: center;
      display: flex;
      flex-direction: column;
      justify-content: center;
      gap: 0;
      border-radius: 4px;
      overflow: hidden;
      box-shadow: 0 0 0 3px #00000014;
      cursor: pointer;
  }

  .equalizer-screens {
      display: flex;
      justify-content: center;
      align-items: flex-end;
  }

  .bottom-screen-info {
      background: #000;
      color: #fff;
      height: 32px;
      font-size: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
  }

  .equalizer-board {
      position: relative;
      height: 138px;
      background: linear-gradient(rgba(206, 24, 30, 1) 0%,
              rgba(236, 140, 14, 1) 18%,
              rgba(248, 215, 6, 1) 38%,
              rgba(136, 195, 59, 1) 100%);
      overflow: hidden;
      z-index: -2;
  }

  .tab-channel-active {
      border: 3px solid #fff;
      box-shadow: none;
      max-width: 102px;
  }

  .equalizer-board img {
      width: 100%;
  }

  /* mixer */
  .main-item.draggers {
      margin-top: 25px;
      margin-bottom: 20px;
      display: flex;
  }

  .mixer-wrap {
      width: 12.5%;
      min-width: 120px;
  }

  .mixer-board {
      max-width: 88px;
  }

  .mixer-container {
      max-width: 120px;
      display: flex;
      gap: 5px;
      align-items: flex-end;
  }

  .mixer-container .inner-container {
      max-width: 88px;
      position: relative;
  }

  .mixer-container .inner-container .tooltip {
      position: absolute;
      z-index: 3;
      top: -12px !important;
      left: 16%;
      background: url(assets/icons/Union.svg);
      background-repeat: no-repeat;
      color: #fff;
      font-size: 12px;
      background-size: cover;
      width: 85px;
      height: 33px;
      display: flex;
      justify-content: center;
      align-items: center;
      padding-bottom: 5px;
      font-weight: bold;
  }

  .number-mixer {
      color: #2d2d2f;
      font-size: 20px;
      line-height: 1;
  }

  .volume-board {
      max-width: 24px;
      width: 100%;
      height: 319px;
      border-radius: 10px;
      margin-bottom: 20px;
      background: linear-gradient(rgba(206, 24, 30, 1) 0%,
              rgba(236, 140, 14, 1) 18%,
              rgba(248, 215, 6, 1) 38%,
              rgba(136, 195, 59, 1) 100%);
      position: relative;
      overflow: hidden;
      z-index: -2;
  }

  .volume-board img {
      max-width: 24px;
      width: 100%;
  }

  .mixer-dragger {
      position: absolute;
      z-index: 2;
      bottom: -5px;
      left: 26px;
      max-width: 59px;
  }

  /* mixer channels */
  .channel-screen {
      width: 120px;
      height: 71px;
      background: #eeeded;
      color: #2d2d2f;
      border: 3px solid #2d2d2f;

      display: flex;
      justify-content: center;
      align-items: flex-end;
      padding-bottom: 10px;
      margin-bottom: 20px;
      border-radius: 4px;
      font-weight: 600;
      text-decoration: none;
  }

  /* mixer mute buttons */
  .main-board-buttons.mute-solo {
      max-width: 120px;
      display: flex;
      margin-top: 30px;
      justify-content: center;
      gap: 20px;
  }

  .main-board-buttons.mute-solo .mute-solo-item {
      width: 56px;
      height: 40px;
  }

  /* needle board and needle */

  .needle-screen {
      max-width: 120px;
      margin-bottom: 30px;
      position: relative;
      overflow: hidden;
      /* max-height: 55px;
        cursor: pointer;
        transition: max-height 0.4s ease-out; */
      max-height: 55px;
      /* Initial height */
      cursor: pointer;
      transition: 0.3s ease-in-out;
      border-radius: 4px;
  }

  .needle-screen:hover {
      max-height: 81px;
      overflow: visible;
      transition: height 0.3s ease;
  }

  .needle-path {
      width: 100%;
  }

  .needle-dragger {
      position: absolute;
      z-index: 1;
      top: 0;
      left: 46%;
  }

  /* ANIMATIONS */
  .btn-activated {
      background: #2d2d2f;
  }

  .overlay {
      position: absolute;
      border-radius: 0;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #2a2a2a;
      /* Adjust opacity as needed */
      z-index: -1;
  }

  /* btn animation */
  .active-btn-shadow {
      box-shadow: 0 0 9.2px 1px #2d2d2f;
  }

  .active-btn-background {
      background: #2d2d2f;
      color: #fff;
      font-weight: 600;
  }

  .btn-block.active-btn-background p {
      color: #fff;
      font-weight: 600;
  }

  .btn-block.active-btn-background img {
      filter: invert(1);
  }

  .btn-block.active-btn-background.active-btn-shadow img {
      filter: invert(0);
  }

  #wifi.btn-block.active-btn-background img {
      filter: invert(1);
  }

  /* send on faders */
  .send-faders-active-options.show-flex,
  .activated-faders.show-flex {
      display: flex;
  }

  .needle-screen.hide-if-faders,
  .sidebar-item.mixing.hide-if-faders {
      display: none;
  }

  .activated-faders.show-flex img {
      filter: invert(1);
  }

  /* TABS Switch channels */

  .main-item.draggers {
      display: none;
  }

  .main-item.draggers.tab-active {
      display: flex;
  }

  .screen-channels-item.tab-active {
      box-shadow: 0 0 0 3px #2d2d2f;
  }

  .btn-block.edit-dca-item {
      width: 100%;
      max-width: 120px;
      height: 44px;
      margin-bottom: 15px;
  }

  .edit-dca-wrap {
      height: 55px;
      margin-bottom: 30px;
  }

  /* tab content colors change */
  #channel-aux .channel-screen,
  #channel-fx .channel-screen {
      background: #c8ffa5;
      color: #171100;
  }

  #channel-bus18 .channel-screen,
  #channel-bus916 .channel-screen {
      background: #b9ffff;
      color: #171100;
  }

  #channel-mtx .channel-screen,
  #channel-dca .channel-screen {
      background: #ffc2ff;
      color: #171100;
  }

  #channel-mtx .mixer-wrap:nth-of-type(8) .channel-screen,
  #channel-mtx .mixer-wrap:nth-of-type(7) .channel-screen {
      background: #f2ede9;
      color: #171100;
  }

  #channel-mtx .mixer-wrap .needle-screen {
      visibility: hidden;
  }

  #channel-mtx .mixer-wrap:last-of-type .needle-screen {
      visibility: visible;
  }

  #channel-dca .mixer-wrap .needle-screen {
      display: none;
  }

  /*  TABS END  */
  /* MUTE disable */
  .muted.disabled,
  .no-faders-tab.disabled {
      pointer-events: none;
      opacity: 0.6;
  }

  /*  MUTE Disable end */

        /* Routing PAGE */
        .below-main {
            background-color: #eeeded;
            padding: 10px 6px;
        }
    
        .tab-content-hidden {
            display: none;
        }
    
        .tab-content-hidden.active-detail-tab {
            display: block;
        }
    
        .detail-tabs-navigation {
            width: 100%;
            display: flex;
            margin-top: 25px;
            gap: 0.5%;
        }
    
        .detail-tabs-navigation .details-tab-nav {
            width: 14.2%;
            background: #C7C7C7;
            color: #2D2D2F;
            text-align: center;
            height: 49px;
            border-radius: 4px 4px 0 0;
            font-weight: bold;
            display: flex;
            justify-content: center;
            align-items: center;
            cursor: pointer;
        }
    
        .details-tab-nav.active-detail-tab {
            background: #dddddd;
        }
    
        .tab-content-hidden {
            background: #dddddd;
            width: 100%;
            min-height: 671px;
            padding: 5px 30px 30px 20px;
        }
    
        /* meters page */
        .top-signal-flex .volume-board {
            max-width: 24px;
            width: 100%;
            height: 319px;
            border-radius: 10px;
            margin-bottom: 20px;
            background: linear-gradient(rgba(206, 24, 30, 1) 10%, rgba(236, 140, 14, 1) 25%, rgba(248, 215, 6, 1) 50%, rgba(136, 195, 59, 1) 100%);
            position: relative;
            overflow: hidden;
            z-index: -2;
        }
    
        .three-meters-column {
            display: flex;
            max-width: 1060px;
            margin: auto;
            gap: 50px;
            justify-content: space-between;
        }
    
        .inner-wrapper-meter {
            display: flex;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            padding-top: 65px;
        }
    
        .main-title-1-32 {
            position: relative;
            color: #2D2D2F;
            font-weight: 600;
        }
    
        .inner-wrapper-meter .main-title-1-32:nth-of-type(3),
        .inner-wrapper-meter .main-title-1-32:nth-of-type(5) {
            margin-top: 50px;
        }
    
        /* .main-title-1-32::before {
            content: url("assets/icons-meters/left-line-border.svg");
            position: absolute;
            right: 110%;
            top: 50%;
        }
    
        .main-title-1-32::after {
            content: url("assets/icons-meters/right-line-border.svg");
            position: absolute;
            left: 110%;
            top: 50%;
        } */
    
        .top-signal-flex {
            display: flex;
            width: 100%;
            max-width: 1140px;
        }
    
        .signals-1-16,
        .signals-17-32 {
            display: flex;
            gap: 9px;
            padding-top: 12px;
        }
    
        .signals-1-16 {
            padding-left: 15px;
        }
    
        .signals-17-32 {
            padding-left: 50px;
        }
    
        .numeric-column div {
            font-size: 12px;
            font-weight: 600;
            color: #2D2D2F;
        }
    
        .numeric-column div:nth-of-type(1) {
            margin-top: 3px;
        }
    
        .numeric-column div:nth-of-type(2),
        .numeric-column div:nth-of-type(4) {
            margin-bottom: 9px;
        }
    
        .numeric-column div:nth-of-type(3),
        .numeric-column div:nth-of-type(5) {
            margin-bottom: 2px;
        }
    
        .gated-1-32 .numeric-column div:nth-of-type(2),
        .gated-1-32 .numeric-column div:nth-of-type(4) {
            margin-bottom: 0;
        }
    
        .top-signal-flex .mixer-container {
            width: 24px;
            max-width: 24px;
            align-items: center;
            flex-direction: column;
            justify-content: center;
            position: relative;
            z-index: 2;
            gap: 8px;
        }
    
        .top-signal-flex .volume-board {
            height: 126px;
        }
    
        .top-signal-flex .volume-board {
            margin: 0;
        }
    
        .number-signal {
            color: #2D2D2F;
            font-size: 14px;
            font-weight: bold;
        }

/* effects */
.insert-btn-drop {
        color: #2d2d2f !important;
}
.effect-lr-block img,
.line-connection img,
.wheel-board .wheel-numbers,
.feed_right_icon,
.reverse.reset_eq img {
    filter: invert(1);
}
.active-btn-background {
    background: #2d2d2f !important;
    color: #fff !important;
}
.fx_heading_ce181e .effect-inner-row.main-effect-board,
.fx_heading_b9ffff .effect-inner-row.main-effect-board,
.fx_heading_72bf44 .effect-inner-row.main-effect-board,
.fx_heading_F2A40A .effect-inner-row.main-effect-board,
.fx_heading_FAD705 .effect-inner-row.main-effect-board,
#fx3-tab-content .effect-inner-row.main-effect-board,
#fx5-tab-content .effect-inner-row.main-effect-board,
#fx6-tab-content .effect-inner-row.main-effect-board,
#fx7-tab-content .effect-inner-row.main-effect-board,
#fx8-tab-content .effect-inner-row.main-effect-board,
.darken_bg .effect-inner-row.main-effect-board,
.left_wall,
.right_wall {
    background: #BEBEBE !important;
}
.bottom-heading-effect,
.fx5-title,
.solo_mode .single_preset_item,
.wheel-box .detail-txt,
.j-fx6,
.a-fx6,
.vse_xtec .dot-right,
.vse_xtec .dot-left,
.toggle-box .text-detail,
.black-circle > div,
.wheel-board > div,
.sm-title-box,
.title-box,
.below_row_fx8 .black-circle::after,
.below_row_fx8 .black-circle::before,
.screen_note,
.bottom_control,
.top_control,
.green_control_vintage .bottom_sm_title,
.red_control_vintage .bottom_sm_title,
.vintage_up .vintage_wrap,
.vintage_wrap,
.vintage_rev .title_wrap,
.col-1_fx3.inputoutput .detail-txt,
.title_wrap,
.two_btns_flex_tap,
.mood_switcher_wrap .text-detail,
.stereo_dry_mode,
.off_4_mode,
.power_mode,
.points_text,
.bott_title_lean,
.triple_btn_flex,
.knobs_wrap.btns_design,
.xover_option,
.combinator_stereo .auto_mode_on,
.dropdown-detail,
.float_top_right,
.progress_bar_wrap .top_bar_lab,
.bar_wrap,
.fx1_49_label,
.title_wrap_ex,
.red_btn_text,
.title_out_wrap > div,
.stero_eq_and_duals .empty_null  {
    color: #2d2d2f !important;
}
.fx6_inner_knobs .vse-limit-col .black-circle::before,
.fx6_inner_knobs .vse-limit-col .black-circle::after,
.black-circle.transf-knob::before,
.black-circle.transf-knob::after,
.effects_double_down.wheel-item .black-circle::after,
.effects_double_down.wheel-item .black-circle::before,
.d_vint_wrap.vintage_up .red_control_vintage .bottom_sm_title::before,
.bottom_sm_title::before,
.points_drag_stops div,
.attsel-twenty::before,
.attsel-five::before,
.attsel-ten::before {
background: #2d2d2f !important;
}
.vintage_wrap {
    border: 1px solid #2d2d2f !important;
}
.vintage_up .vintage_wrap {
    border: none !important
}
.btn_dry_tap {
    border: 3px solid #2d2d2f;
        background: #fff;
}
.stereo_btn,
.dry_btn,
.mode_btn,
.power_btn,
.dss_wrap .single_preset_item>.btn_knob_control,
.dss_wrap .single_preset_item>.btn-block.male_btn {
    border: 3px solid #2d2d2f;
        background: #fff;
}
.upper_btn_selector>div,
.bottom_btn_selector .ser_select,
.one_btn_option>div {
    background: #2d2d2f;
}
.active_module_btn {
    background: #fff !important;
}
.reverse.reset_eq {
    border: 3px solid #2d2d2f;
}
.title_wrap.band_gain_title {
    color: #fff !important;
}


/* details */

.single-detail .mixer-board,
.swap-option,
.item-slope img,
.select-label::before,
.select-label::after {
    filter: brightness(0);
}
.single-detail .text-detail,
.single-detail .detail-txt,
.swap-detail,
.title-main,
.second-row-config .insert-container .detail-btn,
.mixer-container .inner-container .tooltip,
.right-config-tab .title-main,
.active-gate-set.column_one_left,
.type-set.column_one_left  {
    color: #2D2D2F;
}
.single-detail #copy-config {
    color: #fff;
}
.detail-btn {
background: #f7f7f7;
color:#2d2d2f;
font-weight: 700;
    border: 3px solid #2d2d2f;
}
.dropdown-option {
    background-color: #f7f7f7;
    color: #2D2D2F;
}
.dropdown-option:nth-of-type(even) {
    background-color: #d5d5d5;
    color: #2D2D2F;
}
.weight-detail-board .wheel-board::before,
.weight-detail-board .wheel-board::after {
    color: #2D2D2F;
}

.single-detail .heading-list,
.single-detail .scrolled,
.single-detail .select-label,
.single-detail .single-list.preset-first .single_preset_item {
    color: #2D2D2F;
    }
        .single-list.preset-third .btn-block {
            font-weight: 700;
        }
 .single-detail .single-list.preset-first .active-btn-background {
     background-color: #ff9b04 !important;
     border: 3px solid #2d2d2f;
 }
 .single-detail .single-item:nth-last-of-type(odd) {
     background: #ababab;
 }
  .single-detail .single-item:nth-last-of-type(even) {
     background: #C7C7C7;
 }
  .single-detail .scrolled.scrolled-active {
     background: #f7f7f7 !important;
     color: #383838;
 }

 