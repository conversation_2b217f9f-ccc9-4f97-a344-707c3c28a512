<svg width="87" height="87" viewBox="0 0 87 87" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_890_393841)">
<circle cx="43.5" cy="39.5" r="39.5" fill="#494949"/>
<circle cx="43.5" cy="39.5" r="39.5" fill="url(#paint0_linear_890_393841)" fill-opacity="0.5"/>
</g>
<defs>
<filter id="filter0_d_890_393841" x="0" y="0" width="87" height="87" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_890_393841"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_890_393841" result="shape"/>
</filter>
<linearGradient id="paint0_linear_890_393841" x1="43.5" y1="0" x2="43.5" y2="79" gradientUnits="userSpaceOnUse">
<stop stop-opacity="0"/>
<stop offset="1"/>
</linearGradient>
</defs>
</svg>
