   @font-face {
       font-family: "Digital-1";
       src: url(assets/fonts/digital-7.ttf) format("truetype");
   }

   html,
   * {
       box-sizing: border-box;
   }

   body,
   a,
   p,
   div {
       font-family: "Open Sans", sans-serif;
   }

   body {
       background: #626262;
       margin: 0;
       padding: 0;
   }

   .page-wrapper {
       padding: 25px 20px;
       max-height: 100vh;
       max-width: 100%;
   }

   .container {
       display: flex;
       gap: 25px;
       flex-wrap: nowrap;
   }

   .left-sidebar {
       width: 10%;
       text-align: center;
   }

   .main {
       width: 90%;
   }

   /* buttons */

   .btn-block {
       border-radius: 4px;
       border: 3px solid #f5f5f5;
       background: #2d2d2f;
       color: #fff;
       text-transform: uppercase;
       font-size: 14px;
       height: 38px;
       width: 42px;
       display: flex;
       align-items: center;
       justify-content: center;
       cursor: pointer;
   }

   .btn-block p,
   .left-sidebar p {
       color: #fff;
       font-weight: 600;
       text-transform: uppercase;
       font-size: 14px;
   }

   /* main part header */
   .main-item.navbar {
       display: flex;
       justify-content: space-around;
       align-items: center;
   }

   .main-item.navbar a {
       display: flex;
       flex-direction: column;
       gap: 10px;
       justify-content: center;
       align-items: center;
       text-decoration: none;
       text-transform: uppercase;
       color: #fff;
       font-size: 14px;
       font-weight: bold;
   }

   .navbar-item-img {
       background: #000;
       height: 40px;
       width: 40px;
       border-radius: 100px;
       display: flex;
       align-items: center;
       justify-content: center;
       box-shadow: 0 0 0 3px #00000014;
   }

   .navbar-item-img.active_item {
       background: #fff;
       box-shadow: none;
   }

   .navbar-item-img.active_item img {
       filter: invert(1);
   }

   .navbar-item-img.homed img {
       margin-bottom: 2px;
       margin-left: 2px;
   }

   .scenes img {
       margin-top: 7px;
       margin-right: 7px;
   }

   .meters img {
       margin-bottom: 7px;
       margin-left: 5px;
       width: 32px;
   }

   /* sidebar items */
   .sidebar-item.controls {
       width: 100%;
       max-width: 130px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       margin: 15px auto;
       border: 3px solid #f5f5f5;
       border-radius: 4px;
       gap: 0 !important;
   }

   .sidebar-item.controls .btn-block {
       border: none;
       width: 32%;
       border-radius: 0;
   }

   .sidebar-item.controls .btn-block:nth-of-type(2) {
       border-left: 3px solid #f5f5f5;
       border-right: 3px solid #f5f5f5;
       border-radius: 0 !important;
       width: 36%;
       margin: 0;
   }

   /* siebar - 4 buttons */
   .sidebar-item.addons {
       width: 100%;
       max-width: 134px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       flex-wrap: wrap;
       margin: 20px auto;
       gap: 20px;
   }

   .sidebar-item.addons .btn-block {
       width: 42%;
       max-width: 56px;
       max-height: 36px;
   }

   /* fader */
   .activated-faders {
       width: 100%;
       max-width: 130px;
       min-height: 151px;
       margin: auto;
       background: #f5f5f5;
       box-shadow: 0 0 9.2px 1px #f5f5f5;
       display: flex;
       align-items: center;
       justify-content: center;
       flex-direction: column;
       gap: 10px;
       display: none;
   }

   .activated-faders img {
       width: 100%;
       max-width: 60%;
   }

   .btn-block.sidebar-item.faders {
       width: 100%;
       max-width: 134px;
       height: 46px;
       display: flex;
       align-items: center;
       margin: 20px auto;
   }

   .btn-block.sidebar-item.faders p {
       font-size: 12px;
   }

   /* 1-2 when fader activated */
   .send-faders-active-options {
       width: 100%;
       max-width: 120px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       display: none;
   }

   .send-faders-active {
       width: 46%;
       max-width: 55px;
       height: 46px;
       margin-bottom: 20px;
   }

   /* X Y auto mixing */
   .sidebar-item.mixing {
       width: 100%;
       max-width: 134px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       flex-wrap: wrap;
       margin: 45px auto;
   }

   .sidebar-item.mixing p {
       width: 100%;
   }

   .btn-block.mixing-item {
       width: 46%;
       max-width: 55px;
       height: 46px;
   }

   /* mute groups */
   .sidebar-item.mute-group {
       width: 100%;
       max-width: 134px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       flex-wrap: wrap;
       margin: 20px auto;
       gap: 15px;
   }

   .sidebar-item.mute-group .btn-block {
       width: 46%;
       max-width: 55px;
       height: 55px;
   }

   .sidebar-item.mute-group p {
       width: 100%;
       margin-bottom: 0;
   }

   .sidebar-item.mute-group .last-mute {
       width: 100%;
       max-width: 100%;
   }

   /* solos  */
   .sidebar-item.show-solos {
       width: 100%;
       max-width: 134px;
       display: flex;
       align-items: center;
       justify-content: center;
       flex-direction: column;
       margin: 20px auto;
   }

   .sidebar-item.show-solos p {
       width: 100%;
   }

   .show-solos-item-wrap {
       max-width: 134px;
       width: 100%;
       display: flex;
       border: 3px solid #f5f5f5;
       border-radius: 4px;
   }

   .show-solos-item {
       border: none;
       width: 50%;
       border-radius: 0;
       background: #2d2d2f;
       color: #fff;
       text-transform: uppercase;
       font-size: 14px;
       height: 38px;
       cursor: pointer;
       display: flex;
       justify-content: center;
       align-items: center;
   }

   .show-solos-item .btn-block:last-of-type {
       border-left: 3px solid #f5f5f5;
       border-radius: 0 !important;
       margin: 0;
   }

   /* lock mutes big */
   .btn-block.sidebar-item.lock-mute {
       width: 100%;
       max-width: 134px;
       height: 46px;
       display: flex;
       align-items: center;
       flex-wrap: wrap;
       margin: 20px auto;
   }

   .btn-block.sidebar-item.lock-mute p {
       margin: 0;
   }

   /* MAIN */
   /* board equalizer */
   .screen-channels-wrapper {
       margin-top: 30px;
       display: flex;
       justify-content: space-between;
       max-width: 97%;
   }

   .screen-channels-item {
       max-width: 96px;
       text-align: center;
       display: flex;
       flex-direction: column;
       justify-content: center;
       gap: 0;
       border-radius: 4px;
       overflow: hidden;
       box-shadow: 0 0 0 3px #00000014;
       cursor: pointer;
   }

   .equalizer-screens {
       display: flex;
       justify-content: center;
       align-items: flex-end;
   }

   .bottom-screen-info {
       background: #000;
       color: #fff;
       height: 32px;
       font-size: 12px;
       display: flex;
       justify-content: center;
       align-items: center;
   }

   .equalizer-board {
       position: relative;
       height: 138px;
       background: linear-gradient(rgba(206, 24, 30, 1) 0%,
               rgba(236, 140, 14, 1) 18%,
               rgba(248, 215, 6, 1) 38%,
               rgba(136, 195, 59, 1) 100%);
       overflow: hidden;
       z-index: -2;
   }

   .tab-channel-active {
       border: 3px solid #fff;
       box-shadow: none;
       max-width: 102px;
   }

   .equalizer-board img {
       width: 100%;
   }

   /* mixer */
   .main-item.draggers {
       margin-top: 25px;
       margin-bottom: 20px;
       display: flex;
   }

   .mixer-wrap {
       width: 12.5%;
       min-width: 120px;
   }

   .mixer-board {
       max-width: 88px;
   }

   .mixer-container {
       max-width: 120px;
       display: flex;
       gap: 5px;
       align-items: flex-end;
   }

   .mixer-container .inner-container {
       max-width: 88px;
       position: relative;
   }

   .mixer-container .inner-container .tooltip {
       position: absolute;
       z-index: 3;
       top: -12px !important;
       left: 16%;
       background: url(assets/icons/Union.svg);
       background-repeat: no-repeat;
       color: #fff;
       font-size: 12px;
       background-size: cover;
       width: 85px;
       height: 33px;
       display: flex;
       justify-content: center;
       align-items: center;
       padding-bottom: 5px;
       font-weight: bold;
   }

   .number-mixer {
       color: #fff;
       font-size: 20px;
       line-height: 1;
   }

   .volume-board {
       max-width: 24px;
       width: 100%;
       height: 319px;
       border-radius: 10px;
       margin-bottom: 20px;
       background: linear-gradient(rgba(206, 24, 30, 1) 0%,
               rgba(236, 140, 14, 1) 18%,
               rgba(248, 215, 6, 1) 38%,
               rgba(136, 195, 59, 1) 100%);
       position: relative;
       overflow: hidden;
       z-index: -2;
   }

   .volume-board img {
       max-width: 24px;
       width: 100%;
   }

   .mixer-dragger {
       position: absolute;
       z-index: 2;
       bottom: -5px;
       left: 26px;
       max-width: 59px;
   }

   /* mixer channels */
   .channel-screen {
       width: 120px;
       height: 71px;
       background: #1f1f21;
       color: #fff;
       display: flex;
       justify-content: center;
       align-items: flex-end;
       padding-bottom: 10px;
       margin-bottom: 20px;
       border-radius: 4px;
       font-weight: 600;
       text-decoration: none;
   }

   /* mixer mute buttons */
   .main-board-buttons.mute-solo {
       max-width: 120px;
       display: flex;
       margin-top: 30px;
       justify-content: center;
       gap: 20px;
   }

   .main-board-buttons.mute-solo .mute-solo-item {
       width: 56px;
       height: 40px;
   }

   /* needle board and needle */

   .needle-screen {
       max-width: 120px;
       margin-bottom: 30px;
       position: relative;
       overflow: hidden;
       /* max-height: 55px;
        cursor: pointer;
        transition: max-height 0.4s ease-out; */
       max-height: 55px;
       /* Initial height */
       cursor: pointer;
       transition: 0.3s ease-in-out;
       border-radius: 4px;
   }

   .needle-screen:hover {
       max-height: 81px;
       overflow: visible;
       transition: height 0.3s ease;
   }

   .needle-path {
       width: 100%;
   }

   .needle-dragger {
       position: absolute;
       z-index: 1;
       top: 0;
       left: 46%;
   }

   /* ANIMATIONS */
   .btn-activated {
       background: #f5f5f5;
   }

   .overlay {
       position: absolute;
       border-radius: 0;
       top: 0;
       left: 0;
       width: 100%;
       height: 100%;
       background: #2a2a2a;
       /* Adjust opacity as needed */
       z-index: -1;
   }

   /* btn animation */
   .active-btn-shadow {
       box-shadow: 0 0 9.2px 1px #f5f5f5;
   }

   .active-btn-background {
       background: #f5f5f5;
       color: #222;
       font-weight: 600;
   }

   .btn-block.active-btn-background p {
       color: #222;
       font-weight: 600;
   }

   .btn-block.active-btn-background img {
       filter: invert(1);
   }

   /* send on faders */
   .send-faders-active-options.show-flex,
   .activated-faders.show-flex {
       display: flex;
   }

   .needle-screen.hide-if-faders,
   .sidebar-item.mixing.hide-if-faders {
       display: none;
   }

   /* TABS Switch channels */

   .main-item.draggers {
       display: none;
   }

   .main-item.draggers.tab-active {
       display: flex;
   }

   .screen-channels-item.tab-active {
       box-shadow: 0 0 0 3px #fff;
   }

   .btn-block.edit-dca-item {
       width: 100%;
       max-width: 120px;
       height: 44px;
       margin-bottom: 15px;
   }

   .edit-dca-wrap {
       height: 55px;
       margin-bottom: 30px;
   }

   /* tab content colors change */
   #channel-aux .channel-screen,
   #channel-fx .channel-screen {
       background: #c8ffa5;
       color: #171100;
   }

   #channel-bus18 .channel-screen,
   #channel-bus916 .channel-screen {
       background: #b9ffff;
       color: #171100;
   }

   #channel-mtx .channel-screen,
   #channel-dca .channel-screen {
       background: #ffc2ff;
       color: #171100;
   }

   #channel-mtx .mixer-wrap:nth-of-type(8) .channel-screen,
   #channel-mtx .mixer-wrap:nth-of-type(7) .channel-screen {
       background: #f2ede9;
       color: #171100;
   }

   #channel-mtx .mixer-wrap .needle-screen {
       visibility: hidden;
   }

   #channel-mtx .mixer-wrap:last-of-type .needle-screen {
       visibility: visible;
   }

   #channel-dca .mixer-wrap .needle-screen {
       display: none;
   }

   /*  TABS END  */
   /* MUTE disable */
   .muted.disabled,
   .no-faders-tab.disabled {
       pointer-events: none;
       opacity: 0.6;
   }

   /*  MUTE Disable end */

   /* Routing PAGE */
   .below-main {
       background-color: #626262;
       padding: 10px 6px;
   }

   .tab-content-hidden {
       display: none;
   }

   .tab-content-hidden.active-detail-tab {
       display: block;
   }

   .detail-tabs-navigation {
       width: 100%;
       display: flex;
       margin-top: 25px;
       gap: 0.5%;
   }

   .detail-tabs-navigation .details-tab-nav {
       width: 14.2%;
       background: #383838;
       color: #fff;
       text-align: center;
       height: 49px;
       border-radius: 4px 4px 0 0;
       display: flex;
       justify-content: center;
       align-items: center;
       cursor: pointer;
   }

   .details-tab-nav.active-detail-tab {
       background: #5a5a5a;
   }

   .tab-content-hidden {
       background: #5a5a5a;
       width: 100%;
       min-height: 671px;
       padding: 5px 30px 30px 20px;
   }

   /* routing in tab content */
   .home-tab-title {
       width: 80%;
       text-align: center;
       padding: 50px 0 30px;
   }

   .home-tab-title,
   .heading-list,
   .scrolled {
       color: #fff;
       font-weight: 600;
       font-size: 16px;
   }

   .processing-flex {
       max-width: 98%;
       margin: 10px auto;
       display: flex;
   }

   .single-list {
       width: 16.5%;
       min-width: 213px;
   }

   .single-item {
       height: 32px;
       display: flex;
       align-items: center;
       padding-left: 15px;
       cursor: pointer;
   }

   .single-item:nth-last-of-type(odd) {
       background: #414141;
   }

   .single-item:nth-last-of-type(even) {
       background: #383838;
   }

   .scroll-items {
       height: 512px;
       overflow-y: scroll;
       background-color: #2d2d2f;
   }

   .scroll-items::-webkit-scrollbar {
       display: none;
   }

   .heading-list {
       text-align: center;
       padding: 20px 1px;
   }

   .single-list.last-block {
       padding: 0 20px;
   }

   .connected-devices {
       display: flex;
       flex-direction: column;
       justify-content: center;
       align-items: center;
   }

   .connected-devices>div {
       width: 195px;
       height: 195px;
       background-color: #2d2d2f;
       display: flex;
       align-items: center;
       justify-content: center;
       flex-direction: column;
       margin-bottom: 35px;
       border-radius: 12px;
       border: 4px solid #f7f7f7;
       gap: 15px;
   }

   .connected-devices>div>div {
       color: #fff;
       font-weight: 600;
       font-size: 16px;
   }

   .recplay-block {
       display: flex;
       align-items: center;
       justify-content: center;
       gap: 25px;
   }

   .recplay-block>div {
       width: 52px;
       height: 51px;
   }

   /* PRESET Style */
   #rpreset-tab-content {
       padding-top: 40px;
   }

   .single-list.preset-first {
       width: 18%;
   }

   .single-list.preset-second {
       width: 60%;
       padding-top: 62px;
   }

   .single-list.preset-third {
       width: 18%;
       padding-top: 62px;
   }

   .single-list.preset-first .heading-list {
       text-align: left;
   }

   .single-list.preset-third .btn-block {
       width: 118px;
       height: 51px;
   }

   .single-list.preset-third .single_preset_item {
       display: flex;
       justify-content: center;
       align-items: center;
       gap: 30px;
       flex-direction: column;
   }

   .single-list.preset-first .single_preset_item {
       display: flex;
       align-items: center;
       margin-bottom: 15px;
       gap: 20px;
       color: #fff;
       font-weight: 16px;
       font-weight: bold;
   }

   .single-list.preset-first .btn-block {
       width: 51px;
       height: 51px;
   }

   /* Analog output */
   .settings {
       background-color: #383838;
   }

   /* animate */
   .scrolled.scrolled-active {
       background: #f7f7f7;
       color: #383838;
   }

   /* analog tab  */
   .scroll-items.settings {
       display: flex;
       flex-direction: column;
       align-items: center;
       justify-content: space-between;
       padding: 40px 0;
   }

   .btn-block.settings-off {
       width: 120px;
       height: 71px;
   }

   .btn-block.settings-phase {
       width: 155px;
       height: 51px;
   }

   .box-wheel-delay {
       display: flex;
       justify-content: flex-start;
       flex-direction: column;
       align-items: center;
       height: 350px;
       gap: 50px;
   }

   .inside-border-single {
       height: 100%;
       max-height: 514px;
       border: 4px solid #f7f7f7;
       border-radius: 12px;
       background: #2d2d2f;
   }

   .btn-block.delayed {
       width: 155px;
       height: 51px;
       margin: 0 auto;
   }

   .delay-bottom-block {
       height: 156px;
       display: flex;
       align-items: flex-end;
       justify-content: center;
       padding-bottom: 40px;
   }

   .delay-three {
       width: 155px;
       height: 128px;
       border-radius: 6px;
       border: 4px solid #f7f7f7;
       margin-top: 20px;
       display: flex;
       flex-direction: column;
       align-items: center;
       justify-content: center;
       color: #fff;
       gap: 10px;
   }

   .delay-three>div {
       display: flex;
       justify-content: space-between;
       width: 100%;
       max-width: 80%;
   }

   /* analog tab wheel */
   .wheel-item {
       width: 23%;
       margin-bottom: 30px;
       display: flex;
       flex-direction: column;
       justify-content: center;
       align-items: center;
   }

   .wheel-board {
       position: relative;
       text-align: center;
       width: 134px;
       height: 122px;
   }

   .wheel-numbers {
       max-width: 140px;
   }

   .black-circle {
       position: absolute;
       top: 58%;
       left: 50%;
       transform: translate(-50%, -50%);
       background: #1e1e1e;
       width: 89px;
       height: 89px;
       border-radius: 100px;
       display: flex;
       align-items: center;
       justify-content: center;
   }

   .wheel-knob {
       width: 57px;
       height: 57px;
       transform: rotate(-140deg);
   }

   .insert-container {
       padding-right: 20px;
   }

   #animated {
       position: absolute;
   }

   .single-list .wheel-item {
       width: 145px;
       margin-bottom: 30px;
       display: flex;
       flex-direction: column;
       justify-content: center;
       align-items: center;
   }

   .single-list .wheel-board {
       position: relative;
       text-align: center;
       width: 145px;
       height: 122px;
   }

   .single-list .wheel-numbers {
       max-width: 145px;
       width: 100%;
   }

   .single-list .black-circle {
       position: absolute;
       top: 58%;
       left: 49%;
       transform: translate(-50%, -50%);
       background: #1e1e1e;
       width: 85px;
       height: 85px;
       border-radius: 100px;
       display: flex;
       align-items: center;
       justify-content: center;
   }

   /* card */
   .single-list.extended-card-col {
       width: 32%;
       margin-left: 30px;
   }

   .single-list.extended-card-col .inside-border-single {
       display: flex;
       align-items: flex-start;
       justify-content: center;
       padding-top: 20px;
   }

   .single-list.extended-card-col .inside-border-single img {
       width: 100%;
       max-width: 369px;
   }

   /* EFFECTS */
   .effect-inner-flex {
       display: flex;
       flex-direction: column;
   }

   .effect-insert-block-row .volume-board {
       width: 160px !important;
       max-width: 100% !important;
       height: 24px;
       border-radius: 10px;
       margin-bottom: 0;
       background: linear-gradient(to left,
               rgba(206, 24, 30, 1) 5%,
               rgba(236, 140, 14, 1) 25%,
               rgba(248, 215, 6, 1) 38%,
               rgba(136, 195, 59, 1) 100%);

       position: relative;
       overflow: hidden;
       z-index: 0;
   }

   .effect-insert-block-row .volume-board img {
       max-width: 160px !important;
       width: 100% !important;
       height: 24px !important;
   }

   .insert-btn-drop {
       border-radius: 4px;
       border: 3px solid #2d2d2f;
       color: #f7f7f7;
       width: 121px;
       height: 31px;
       display: flex;
       justify-content: center;
       align-items: center;
       font-size: 14px;
       font-weight: bold;
       cursor: pointer;
   }

   /* row 1 */
   .effect-inner-row {
       display: flex;
       justify-content: space-between;
   }

   .effect-column.insert-mute {
       display: flex;
       gap: 30px;
   }

   .effect-insert-block-row {
       display: flex;
       align-items: center;
       gap: 30px;
   }

   .effect-inner-row.first-row-insert-mute {
       padding-left: 30px;
       padding-top: 25px;
   }

   .effect-insert-block,
   .effect-mute-block,
   .effect-column.effects-presets,
   .effect-column.copy-paste {
       display: flex;
       flex-direction: column;
       justify-content: space-around;
       gap: 15px;
   }

   .effect-column.inner-tab-control {
       display: flex;
       gap: 15px;
       align-items: center;
   }

   /* third row tab 1 */
   .effect-inner-row.fx-1-third {
       padding-left: 30px;
       height: 110px;
   }

   .effect-column.inner-tab-control .btn-block {
       height: 52px;
       width: 51px;
   }

   /* row 2 main board effect */
   .effect-inner-row.main-effect-board {
       height: 370px;
       width: 100%;
       width: 100%;
       margin: 25px auto;
       /* background: url("assets/icons-effects/bg-main-content.jpg");
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center; */
       padding: 10px;
   }

   .fx1-inner-1-content {
       display: flex;
       flex-direction: column;
       width: 100%;
   }

   .heading-board-effect {
       display: flex;
       background: #000;
       height: 82px;
       align-items: center;
       gap: 2px;
       justify-content: space-between;
       padding: 0 5px;
   }

   .fx1-inner-1-content .heading-board-effect {
       color: #72bf44;
       font-size: 30px;
       font-family: "Digital-1", sans-serif !important;
   }

   .fx1-inner-1-content .heading-board-effect .filled-column {
       font-family: "Digital-1", sans-serif !important;
   }

   .bottom-heading-effect {
       display: flex;
       color: #fff;
       font-size: 15px;
       font-weight: bold;
       align-items: center;
       gap: 2px;
       justify-content: space-between;
       padding: 15px 5px 10px;
   }

   .dragger-effect-board {
       height: 218px;
       display: flex;
       align-items: center;
       gap: 2px;
       justify-content: space-between;
   }

   .relative-effect-board {
       position: relative;
       max-width: 86px;
       margin: auto;
   }

   .effect-board-route {
       background: #818181;
       width: 10px;
       height: 188px;
       border-radius: 33px;
       border: 4px solid #000;
       margin: auto;
   }

   .effect-board-route .effects-dragger {
       position: absolute;
       z-index: 1;
       bottom: 0;
       left: 0;
   }

   .dragger-effect-board .filler-space-column {
       display: flex;
       flex-direction: column;
       gap: 15px;
       justify-content: flex-end;
       align-items: flex-end;
       height: 185px;
   }

   .filler-space-column .bars-fillers {
       height: 6px;
       width: 100%;
       background-color: #000;
   }

   .filled-column {
       width: 8%;
       text-align: center;
   }

   .filler-space-column {
       width: 10%;
   }

   .effect-inner-row.main-effect-board {
       position: relative;
   }

   .screw-bottom-left {
       position: absolute;
       bottom: 10px;
       left: 10px;
       z-index: 1;
   }

   .screw-bottom-right {
       position: absolute;
       bottom: 10px;
       right: 10px;
       z-index: 1;
   }

   .screw-top-left {
       position: absolute;
       top: 10px;
       left: 10px;
       z-index: 1;
   }

   .screw-top-right {
       position: absolute;
       top: 10px;
       right: 10px;
       z-index: 1;
   }

   .tab-inner-content-hidden {
       display: none;
   }

   .tab-inner-content-hidden.active-detail-tab {
       display: flex;
   }

   .btn-block[disabled] {
       pointer-events: none;
   }

   /* fx2 and f4 */
   .effect-inner-flex.fx4 .fx1-inner-1-content .heading-board-effect {
       color: #b9ffff;
   }

   .effect-inner-flex.fx2 .fx1-inner-1-content .heading-board-effect {
       color: #ce181e;
   }

   /* fx3 tab */
   #fx3-tab-content .effect-inner-row.main-effect-board,
   #fx5-tab-content .effect-inner-row.main-effect-board,
   #fx6-tab-content .effect-inner-row.main-effect-board,
   #fx7-tab-content .effect-inner-row.main-effect-board,
   #fx8-tab-content .effect-inner-row.main-effect-board {
       background: #3f3f3f;
   }

   .fx3-inner-1-content {
       display: flex;
       width: 100%;
       padding: 0 40px;
       justify-content: space-around;
   }

   .col-1_fx3.inputoutput {
       display: flex;
       justify-content: center;
       align-items: center;
       gap: 20px;
   }

   .col-1_fx3.inputoutput .volume-board {
       border-radius: 0px;
       z-index: 0;
       width: 70px;
       max-width: 70px;
       height: 269px;
   }

   .col-1_fx3.inputoutput .mixer-volume {
       width: 70px;
       max-width: 70px;
       height: 269px;
   }

   .col-1_fx3.inputoutput .detail-txt {
       color: #fff;
       text-align: center;
       font-weight: 500;
       margin-bottom: 4px;
   }

   .col-1_fx3.numbers_1,
   .col-1_fx3.numbers_2,
   .col-1_fx3.numbers_3 {
       width: 20%;
       display: flex;
       justify-content: center;
       align-items: center;
       flex-direction: column;
       gap: 15px;
   }

   .display_wrap {
       background: #6f6f6f;
       border-radius: 4px;
       width: 230px;
       height: 72px;
       padding: 6px;
       border: 1px solid #6f6f6f;
   }

   .display_number {
       border: 1px solid #000;
   }

   .display_number.targeted {
       border: 1px solid #ce181e !important;
   }

   .display_number input {
       cursor: pointer;
   }

   .label_below {
       font-size: 10px;
       color: #fff;
       font-weight: 500;
       text-align: center;
   }

   .display_number {
       background-color: #000;
       color: #ce181e;
       display: flex;
       height: 49px;
       border-radius: 4px;
       justify-content: center;
       align-items: flex-end;
       cursor: pointer;
   }

   .display_number>div {
       font-size: 48px;
       font-family: "Digital-1", sans-serif !important;
   }

   .sm_note {
       font-size: 10px;
       color: #ce181e;
       font-weight: 500;

       margin-bottom: 6px;
   }

   .col-1_fx3.control_unit {
       width: 18%;
       display: flex;
       justify-content: space-around;
       flex-direction: column;
       align-items: center;
   }

   .control_wrap.wheel-item {
       position: relative;
       width: 207px;
       justify-content: space-around;
       align-items: center;
       height: 100%;
       height: 335px;
       margin-bottom: 0 !important;
       background-color: #2a2a2a;
       border-radius: 4px;
       border: 6px solid #000;
   }

   .control_wrap.wheel-item .detail-txt {
       color: #f7f7f7;
       font-weight: 600;
       font-size: 36px;
   }

   .control_wrap.wheel-item .wheel-board::before {
       content: url(assets/icons-effects/minus-ico.svg);

       font-weight: bold;
       color: #fff;
       position: absolute;
       top: -20px;
       left: 5px;
   }

   .control_wrap.wheel-item .wheel-board::after {
       content: url(assets/icons-effects/plus-ico.svg);

       font-weight: bold;
       color: #fff;
       position: absolute;
       top: -20px;
       right: 5px;
   }

   .control_wrap.wheel-item .wheel-knob {
       width: 75px;
       height: 75px;
   }

   .control_wrap.wheel-item .black-circle {
       width: 120px;
       height: 120px;
   }

   /* fx5 tab */
   #fx5-tab-content .effect-inner-row.main-effect-board {
       /* height: 233px; */
       margin-top: 50px;
       margin-bottom: 117px;
   }

   .fx5-inner-1-content {
       padding: 5px 50px;
       width: 100%;
   }

   .fx5-title {
       font-weight: bold;
       font-size: 32px;
       color: #fff;
       margin-bottom: 10px;
   }

   .fx5-flex-wrapper {
       display: flex;
       gap: 20px;
   }

   .solo_mode,
   .stereo_1,
   .stereo_2 {
       width: 10%;
   }

   .double-stereo {
       min-width: 220px !important;
       width: 20% !important;
   }

   .solo_mode .single_preset_item {
       display: flex;
       flex-direction: column;
       justify-content: center;
       align-items: center;
       gap: 39px;
       color: #fff;
       font-weight: bold;
       padding-top: 8px;
   }

   #solo-mode {
       width: 51px;
       height: 50px;
   }

   .single_stereo.wheel-item {
       width: 110px;
       min-width: 100px;
       margin-bottom: 0 !important;
   }

   .single_stereo.wheel-item .wheel-board {
       text-align: center;
       width: 100px;
       height: 65px;
       margin-bottom: 10px;
   }

   .single_stereo.wheel-item .black-circle {
       width: 63px;
       height: 63px;
   }

   .single_stereo.wheel-item .wheel-knob {
       width: 39px;
       height: 39px;
   }

   .double-stereo {
       display: flex;
       flex-wrap: wrap;
       width: 220px;
       justify-content: center;
   }

   .double-stereo .line-connection {
       width: 100%;
       text-align: center;
   }

   .stereo_1 {
       margin-left: 5px;
   }

   #stereo_3 .wheel-board {
       margin-right: 10px;
   }

   #stereo_4 .wheel-board {
       margin-left: -5px;
   }

   /* fx 7 */
   .vse-limit-col,
   .vse-electronic-col {
       width: 33.5%;
   }

   #fx7-inner-1-content .vse-limit-col .boxed:nth-of-type(2) {
       width: 35%;
   }

   .vse-electronic-col {
       display: flex;
       flex-wrap: wrap;
       padding-left: 0;
   }

   .vse-limit-col {
       display: flex;
       flex-wrap: wrap;
   }

   .title-box {
       font-size: 32px;
       color: #fff;
       font-weight: bold;
   }

   .sm-title-box {
       color: #fff;
       font-weight: bold;
       text-align: center;
       padding-top: 58px;
   }

   .vse-electronic-col .boxed,
   .vse-limit-col .boxed {
       width: 50%;
   }

   .toggle-box {
       display: flex;
       justify-content: flex-start;
       flex-direction: column;
       align-items: center;
       padding-top: 30px;
   }

   .toggle-box .text-detail {
       color: #fff;
       font-weight: 500;
       font-size: 20px;
       margin-bottom: 10px;
   }

   .toggle-box .circle {
       background-color: #bababa;
       width: 30px;
       height: 30px;
       top: 3px;
       left: 3px;
   }

   .toggle-box .circle {
       transform: translateY(82%);
   }

   .toggle-box .dark-mode .circle {
       transform: translateX(0);
   }

   .toggle-box .toggle-modes {
       background-color: #171717;
       border: 1px solid #515151;
       width: 38px;
       height: 63px;
   }

   .wheel-box .wheel-item {
       width: 100%;
       margin-bottom: 0;
   }

   .wheel-box .black-circle {
       width: 85px;
       height: 85px;
   }

   .wheel-box .detail-txt {
       color: #fff;
       font-weight: 500;
       font-size: 20px;
       margin-top: 20px;
   }

   .wheel-box .wheel-numbers {
       max-width: 134px;
       margin-top: 11px;
   }

   /* indicator */
   .vse-indicator-col {
       width: 30%;
       display: flex;
       align-items: center;
       justify-content: center;
   }

   .indicator_wrap {
       width: 331px;
       height: 165px;
       display: flex;
       justify-content: center;
       align-items: center;
       background: #171717;
       border-top: 3px solid #292929;
   }

   .indicator-img {
       position: relative;
       background: #f7f7f7;
       width: 273px;
       height: 107px;
       text-align: center;
       display: flex;
       justify-content: center;
       align-items: center;
   }

   .needle-indicator {
       width: 2px !important;
       position: absolute;
       left: 50%;
       transform: translate(-50%, -50%);
       top: 58%;
   }

   .needle-indicator {
       transform: rotate(-90deg);
       transform-origin: bottom;
       top: 45%;
       animation: needle-move 0.5s infinite ease-in-out;
       animation-fill-mode: forwards;
   }

   @keyframes needle-move {
       0% {
           transform: rotate(-90deg);
       }

       25% {
           transform: rotate(-88deg);
       }

       50% {
           transform: rotate(-91deg);
       }

       75% {
           transform: rotate(-89deg);
       }

       100% {
           transform: rotate(-90deg);
       }
   }

   .indicator-img img {
       width: 100%;
       max-width: 190px;
   }

   /* fx 8 */
   #fx8-inner-1-content {
       justify-content: flex-start;
   }

   #fx8-inner-1-content .sm-title-box {
       text-align: left;
       font-size: 32px;
       font-weight: 500;
       padding-top: 30px;
       width: 100% !important;
   }

   #fx8-inner-1-content .vse-electronic-col {
       width: 35% !important;
       flex-wrap: nowrap;
       flex-direction: column;
       justify-content: space-around;
   }

   .combine_on_inficator {
       display: flex;
       align-items: center;
       gap: 20px;
       max-width: 480px;
   }

   #fx8-inner-1-content .vse-indicator-col {
       width: 100%;
   }

   #fx8-inner-1-content .vse-electronic-col {
       width: 35%;
   }

   #fx8-inner-1-content .vse-limit-col {
       width: 65%;
       padding-top: 20px;
   }

   .double_wheel_fx8 {
       width: 40%;
       display: flex;
   }

   #fx8-inner-1-content .vse-limit-col .double_wheel_fx8 .boxed {
       width: 50% !important;
   }

   #fx8-inner-1-content .vse-limit-col .boxed {
       width: 30%;
   }

   #fx8-inner-1-content .wheel-box .black-circle {
       width: 155px;
       height: 155px;
   }

   #fx8-inner-1-content .wheel-board {
       width: 200px;
       height: 193px;
   }

   #fx8-inner-1-content .wheel-box .wheel-numbers {
       max-width: 200px;
       margin-top: 11px;
   }

   #fx8-inner-1-content .wheel-knob {
       width: 100px;
       height: 100px;
   }

   #fx8-inner-1-content .double_wheel_fx8 .wheel-box .black-circle {
       width: 77px;
       height: 77px;
   }

   #fx8-inner-1-content .double_wheel_fx8 .wheel-knob {
       width: 57px;
       height: 57px;
   }

   #fx8-inner-1-content .double_wheel_fx8 .wheel-board {
       width: 140px;
       height: 140px;
   }

   #fx8-inner-1-content .double_wheel_fx8 .wheel-box .wheel-numbers {
       max-width: 140px;
       margin-top: 19px;
   }

   #fx8-inner-1-content .double_wheel_fx8 .wheel-box.time_box .wheel-numbers {
       max-width: 123px;
       margin-top: 19px;
   }

   .below_row_fx8 .black-circle {
       width: 60px !important;
       height: 60px !important;
   }

   .below_row_fx8 .wheel-knob {
       width: 37px !important;
       height: 37px !important;
   }

   .below_row_fx8 {
       width: 70%;
       display: flex;
       justify-content: flex-end;
       gap: 80px;
   }

   #fx8-inner-1-content .below_row_fx8 .wheel-board {
       width: 100px;
       height: 80px;
   }

   .below_row_fx8 .wheel-box .detail-txt {
       margin-top: 0 !important;
   }

   #fx8-inner-1-content .wheel-box .detail-txt {
       margin-top: 5px;
   }

   /* fx 6 */

   #fx6-tab-content .screw-top-left,
   #fx6-tab-content .screw-top-right {
       top: 30%;
   }

   #fx6-tab-content .screw-bottom-left,
   #fx6-tab-content .screw-bottom-right {
       bottom: 30%;
   }

   .a-fx6 {
       position: absolute;
       z-index: 1;
       top: 30px;
       left: 50px;
       color: #f7f7f7;
       font-weight: 500;
       font-size: 32px;
   }

   .j-fx6 {
       position: absolute;
       z-index: 1;
       bottom: 30px;
       left: 20px;
       color: #f7f7f7;
       font-weight: 400;
       font-size: 16px;
   }

   #fx6-inner-1-content {
       justify-content: space-around;
   }

   #fx6-inner-1-content .vse-limit-col {
       width: 70%;
       padding-left: 60px;
   }

   #fx6-inner-1-content .vse-limit-col .boxed {
       width: 20%;
   }

   #fx6-inner-1-content .wheel-box .detail-txt {
       font-size: 14px;
       font-weight: 500;
       margin-top: 5px;
   }

   #fx6-inner-1-content .boxed.double-box {
       width: 40%;
       display: flex;
       justify-content: flex-start;
       align-items: center;
       gap: 55px;
       padding-left: 30px;
   }

   #fx6-inner-1-content .toggle-box .toggle-modes {
       width: 74px;
       height: 38px;
   }

   #fx6-inner-1-content .toggle-box .circle {
       transform: translateX(118%);
   }

   #fx6-inner-1-content .toggle-box .dark-mode .circle {
       transform: translateX(0%);
   }

   .red-btn-out {
       width: 45px;
       height: 45px;
       border-radius: 100px;
       background: #171717;
       border: 1px solid #585858;
       display: flex;
       justify-content: center;
       align-items: center;
   }

   .red-btn-in {
       width: 31px;
       height: 31px;
       background: #ce181e;
       border-radius: 100px;
       box-shadow: inset 0 24px 0px -21px #f2ede933;
       cursor: pointer;
   }

   #fx6-inner-1-content .text-detail {
       margin: 0;
   }

   #fx6-inner-1-content .toggle-box {
       flex-direction: row;
       width: 50% !important;
       padding: 0;
   }

   .fx6-att-col {
       width: 20%;
       display: flex;
       flex-direction: column;
       gap: 20px;
       justify-content: space-between;
       padding: 10px 0 15px;
   }

   #fx6-inner-1-content .fx6-att-col .wheel-knob:last-of-type,
   .switcher-poly {
       width: 65px;
       height: auto;
   }

   #fx6-tab-content .effect-inner-row.main-effect-board,
   #fx7-tab-content .effect-inner-row.main-effect-board,
   #fx8-tab-content .effect-inner-row.main-effect-board {
       height: 394px;
   }

   /* tab 3animate */
   .display_number input {
       font-size: 48px;
       font-family: "Digital-1", sans-serif !important;
       background: transparent;
       color: red;
       outline: none !important;
       border: 0;
       text-align: right;
       display: inline-block;
       width: 95px;
       padding-right: 4px;
       height: 42px;
   }

   .display_number input.level {
       width: 105px;
   }

   .control_wrap .wheel-board {
       display: none;
   }

   .control_wrap .wheel-board.active-knob {
       display: block;
       pointer-events: all;
   }