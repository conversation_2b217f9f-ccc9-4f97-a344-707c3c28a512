/* links of tabs */
.screen-channels-item {
    text-decoration: none;
}
.single-detail .aux-single,
.single-detail .fx-single {
    background-color: #c8ffa5;
    color: #171100;
}

.single-detail .dca-single,
.single-detail .mtx-single {
background: #ffc2ff;
    color: #171100;
}
 .single-detail .bus-single
  {
background: #b9ffff;
    color: #171100;
}
          .single-detail .ch-single  {
background-color: #2d2d2f;
    color: #f2ede9;
        }
                 /* {
                    background: #1f1f21;
                        color: #fff;
                 } */

      

/* swap option */
 .swap-option {
     display: none;
     /* Hide images by default */
    position: absolute;
        width: 110px;
        z-index: 3 !important;
top: 59px;
        left: 32px;
 }
.swap-block {
        position: relative;
}
 .swap-option.active {
     display: block;
     /* Show active image */
 }

 /* single Detail */
 .mixer-container {
position: relative;

 }
 .automix-wrapper {
    position: absolute;
        top: 10px;
        right: 3px;
        z-index: 5;
        width: 24px;
        height: 118px;
        display: flex;
        justify-content: center;
 }
  .auto-mix-board-xy {
    position: relative;
        top: 20px;
    width: 24px;
        height: 118px;
            right: 0;
  }
 .auto-mix-board-xy img {
    width: 24px;
position: absolute;
z-index: 6;
bottom: 0px;
right: 0;
 }
  .auto-mix-board-xy img:last-of-type {
      width: 16px;
position: absolute;
    z-index: 7;
    bottom: 0;
    right: 0;
  }


  .single-item.scrolled.scrolled-active .preset-space {
    background: #000;
        width: 50%;
        height: 32px;
        margin-left: auto;
        color: #fff;
        display: flex;
        align-items: center;
        padding-left: 20px;
  }

  /* slope DYN */
 .wheel-item.filtered-item {
        display: flex;
            flex-direction: row;
            align-items: center;
            justify-content: flex-start;
            gap: 50px;
  }
  .slope-wrap {
        display: flex;
            flex-direction: column;
        align-items: flex-end;
            justify-content: center;
            margin-top: auto;
            height: 101px;
    width: 21px;
  }
   .item-slope {
        width: 21px;
            height: 21px;
            border: 1px solid #232323;
            border-bottom: none;
            display: flex;
            align-items: center;
            justify-content: center;
       
   }
   .item-slope:last-of-type {
       border-bottom: 1px solid #232323;
   }
  .item-slope img {
width: 100%;
    max-width: 15px;
  }
   .item-slope.active-slope {
       background-color: #FF9B04;
   }
        .item-slope.active-slope img {
          filter: invert(1);
        } 


        /* single bus pages */
.bus-pages .sends-flex .single-column_sends:nth-of-type(4),
.bus-pages .sends-flex .single-column_sends:nth-of-type(5),
.bus-pages .sends-flex .single-column_sends:nth-of-type(6),
.bus-pages .sends-flex .single-column_sends:nth-of-type(7),
.bus-pages .sends-flex .single-column_sends:nth-of-type(8) {
display: none
        }


        /* line animation for gate and dyn */
                .screen-show-wrapper {
                    position: relative;
                    width: 320px;
                    height: 320px;
                }
        .screen-show-wrapper img {
            position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 3;
        }
                .screen-show-wrapper svg {
            position: relative;
                z-index: 3;
                cursor: pointer;
        }


/* main animation of svg and EQ */
.below-numbers>div::before {
        background: #ffffffb8 !important;
}


.movement-board {
    position: relative;
}

.wrapper_lines {
    width: 5px;
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 10;
}
.wrap_line_low {
    left: 230px;
        /* left: 26%; */
}
.wrap_line_lomid {
left: 390px;
/* left: 46%; */
}
.wrap_line_himid {
    left: 565px;
    /* left: 66.6%; */
}
.wrap_line_high {
    left: 759px;
        /* left: 89.5%; */
}

/* before lines label */
.wrapper_lines::before {
display: inline-block;
    position: absolute;
    z-index: 10;
    left: -10px;
    top: 4px;
    color: #fff;
    text-align: center;
    font-size: 12px;
    font-weight: bold;
}
.wrap_line_cutlo::before {
content: url('/assets/icons-details/eq-locut-icon.svg');

}
.wrap_line_low::before {
    content: '1';
    border: 1px solid #fff;;
    width: 19px;
    height: 19px;
}
.wrap_line_lomid::before {
    content: '2';
    border: 1px solid #fff;
    ;
    width: 19px;
    height: 19px;
}
.wrap_line_himid::before {
    content: '3';
    border: 1px solid #fff;
    ;
    width: 19px;
    height: 19px;
}

.wrap_line_high::before {
    content: '4';
    border: 1px solid #fff;
    ;
    width: 19px;
    height: 19px;
}
.active-line::before {
    transform: scale(1.2);
}

/* eq width and height */
/* .wrapper_lines.wrap_line_low::after {
   content: ' ';
    position: absolute;
    z-index: 10;
    left: 0;
    top: 50%;
    background: #fff;
}  */
  .wrap_line_low_wave {
      position: absolute;
      left: 100px;
      /* Always 100px in front of the line */
      bottom: 150px;
      /* Middle position */
      width: 150px;
      /* Control wave width */
      height: 0;
      /* Initially no height */
      background: rgba(0, 255, 0, 0.5);
      /* Light green wave */
      border-radius: 50%;
      /* Wave-like rounded edges */
      transition: height 0.2s ease;
      /* Smooth height change */
  }

  .wave-taper {
      width: 100%;
      height: 100%;
      background: linear-gradient(to right, rgba(0, 255, 0, 0.5), rgba(0, 255, 0, 0));
      /* Taper to transparent */
  }