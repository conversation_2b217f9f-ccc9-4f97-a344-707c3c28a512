import eventlet
# eventlet.monkey_patch()  # Patches standard libraries for async support

from flask import (
    Flask,
    render_template,
    jsonify,
    request,
    send_from_directory,
    abort,
    redirect,
    url_for,
    session,
    flash,
)
import boto3
import random
import xair_api
from botocore.exceptions import NoCredentialsError, PartialCredentialsError
from flask_socketio import SocketIO, emit, join_room, leave_room, rooms
import uuid
import time
from datetime import datetime, timedelta

# eventlet.monkey_patch()  # Patches standard libraries for async support

# # Create a key pair
# key = crypto.PKey()
# key.generate_key(crypto.TYPE_RSA, 2048)

# # Create a self-signed cert
# cert = crypto.X509()
# cert.get_subject().C = "US"
# cert.get_subject().ST = "MyState"
# cert.get_subject().L = "MyCity"
# cert.get_subject().O = "MyCompany"
# cert.get_subject().OU = "MyOrganizationalUnit"
# cert.get_subject().CN = "localhost"
# cert.set_serial_number(1)
# cert.gmtime_adj_notBefore(0)
# cert.gmtime_adj_notAfter(10*365*24*60*60) # 10 years validity
# cert.set_issuer(cert.get_subject())
# cert.set_pubkey(key)
# cert.sign(key, 'sha256')

# # Save the key and cert
# cert_file = "local.crt"
# key_file = "local.key"

# with open(cert_file, "wt") as f:
#     f.write(crypto.dump_certificate(crypto.FILETYPE_PEM, cert).decode("utf-8"))

# with open(key_file, "wt") as f:
#     f.write(crypto.dump_privatekey(crypto.FILETYPE_PEM, key).decode("utf-8"))

# print(f"Created {cert_file} and {key_file}")


def get_parameter(ssm_client, name):
    try:
        response = ssm_client.get_parameter(Name=name, WithDecryption=True)
        return response["Parameter"]["Value"]
    except (NoCredentialsError, PartialCredentialsError):
        print(
            "AWS credentials not found. Ensure the application is configured correctly."
        )
        # return('some_dummy_cant_configure_boto3')
        raise
    except Exception as e:
        print(f"Error retrieving parameter '{name}': {e}")
        # return ('some_dummy_cant_configure_boto3')
        raise


# Initialize Boto3 client for SSM
ssm_client = boto3.client("ssm", region_name="us-east-1")

# create the flask app
app = Flask(__name__, template_folder="../web", static_folder="../web/assets")

# Fetch secret key and password from SSM Parameter Store
app.secret_key = get_parameter(ssm_client, "/vse/app-secret-key")
PASSWORD = get_parameter(ssm_client, "/vse/app-password")

socketio = SocketIO(app, cors_allowed_origins="*")

# Enhanced session management with proper room isolation
class SessionManager:
    def __init__(self):
        self.sessions = {}  # session_key -> session_info
        self.user_sessions = {}  # user_id -> session_key
        
    def create_session(self, host_user_id=None):
        session_key = str(random.randint(100000, 999999))
        session_info = {
            'session_key': session_key,
            'host_user_id': host_user_id or str(uuid.uuid4()),
            'host_connected': True,  # Host is connected when they create the session
            'relay_connected': False,  # Desktop app with X32
            'clients': {},  # client_user_id -> client_info
            'relay_info': None,  # relay connection details
            'created_at': datetime.now(),
            'last_activity': datetime.now(),
            'status': 'waiting_for_relay'  # waiting_for_relay, active, ended
        }
        self.sessions[session_key] = session_info
        if host_user_id:
            self.user_sessions[host_user_id] = session_key
        return session_key, session_info
    
    def join_session(self, session_key, user_id, role='client'):
        if session_key not in self.sessions:
            return False, "Session not found"
            
        session = self.sessions[session_key]
        
        if role == 'host':
            # Only allow the original host to rejoin
            if user_id != session['host_user_id']:
                return False, "Only the session creator can join as host"
            session['host_connected'] = True
            
        elif role == 'relay':
            # Only one relay per session, but allow the same relay to reconnect
            if session['relay_connected'] and session['relay_info'] and session['relay_info']['user_id'] != user_id:
                return False, "Different relay already connected to this session"
            
            session['relay_connected'] = True
            session['relay_info'] = {
                'user_id': user_id,
                'joined_at': datetime.now(),
                'connected': True
            }
            
            # Session becomes active when relay connects
            if session['host_connected']:
                session['status'] = 'active'
                
        else:  # client
            if user_id in session['clients']:
                return False, "Client already in session"
            session['clients'][user_id] = {
                'user_id': user_id,
                'joined_at': datetime.now(),
                'connected': True
            }
        
        self.user_sessions[user_id] = session_key
        session['last_activity'] = datetime.now()
        return True, session
    
    def leave_session(self, user_id):
        if user_id not in self.user_sessions:
            return False, "User not in any session"
            
        session_key = self.user_sessions[user_id]
        session = self.sessions.get(session_key)
        
        if not session:
            del self.user_sessions[user_id]
            return False, "Session not found"
        
        # Remove user from session
        if user_id == session['host_user_id']:
            session['host_connected'] = False
            session['status'] = 'ended'
        elif user_id in session['clients']:
            del session['clients'][user_id]
        
        del self.user_sessions[user_id]
        
        # Clean up empty sessions
        if not session['host_connected'] and not session['clients']:
            del self.sessions[session_key]
            
        return True, session
    
    def get_session(self, session_key):
        return self.sessions.get(session_key)
    
    def get_user_session(self, user_id):
        session_key = self.user_sessions.get(user_id)
        if session_key:
            return self.sessions.get(session_key)
        return None
    
    def cleanup_expired_sessions(self, max_age_hours=24):
        """Remove sessions older than max_age_hours"""
        cutoff = datetime.now() - timedelta(hours=max_age_hours)
        expired_sessions = []
        
        for session_key, session in self.sessions.items():
            if session['last_activity'] < cutoff:
                expired_sessions.append(session_key)
        
        for session_key in expired_sessions:
            session = self.sessions[session_key]
            # Remove all users from tracking
            if session['host_user_id'] in self.user_sessions:
                del self.user_sessions[session['host_user_id']]
            for client_id in session['clients']:
                if client_id in self.user_sessions:
                    del self.user_sessions[client_id]
            del self.sessions[session_key]
        
        return len(expired_sessions)

# Global session manager instance
session_manager = SessionManager()


def generate_session_key():
    return str(random.randint(100000, 999999))  # 6-digit numeric key


@app.route("/create-session", methods=["POST"])
def create_session():
    data = request.get_json() or {}
    role = data.get("role", "host")  # host or client
    host_user_id = data.get("user_id")  # optional host user ID
    
    session_key, session_info = session_manager.create_session(host_user_id)
    
    return jsonify({
        "session_key": session_key,
        "host_user_id": session_info['host_user_id'],
        "status": session_info['status']
    })


@app.route("/validate-session", methods=["POST"])
def validate_session():
    data = request.get_json()
    session_key = data.get("session_key")

    if not session_key or not session_key.isdigit() or len(session_key) != 6:
        return jsonify({"valid": False, "message": "Invalid session key format"}), 400

    session = session_manager.get_session(session_key)
    if session:
        return jsonify({
            "valid": True, 
            "status": session['status'],
            "host_connected": session['host_connected'],
            "client_count": len(session['clients'])
        })

    return jsonify({"valid": False, "message": "Session not found"}), 404


@app.route("/join-session", methods=["POST"])
def join_session():
    data = request.get_json()
    session_key = data.get("session_key")
    user_id = data.get("user_id", str(uuid.uuid4()))
    role = data.get("role", "client")  # host or client

    success, result = session_manager.join_session(session_key, user_id, role)
    
    if not success:
        return jsonify({"error": result}), 400

    return jsonify({
        "message": "Joined session successfully",
        "session_key": session_key,
        "user_id": user_id,
        "role": role,
        "session_status": result['status']
    })


@app.route("/leave-session", methods=["POST"])
def leave_session():
    data = request.get_json()
    user_id = data.get("user_id")
    
    if not user_id:
        return jsonify({"error": "User ID required"}), 400
    
    success, result = session_manager.leave_session(user_id)
    
    if not success:
        return jsonify({"error": result}), 400
    
    return jsonify({"message": "Left session successfully"})


@app.route("/session-status/<session_key>", methods=["GET"])
def get_session_status(session_key):
    session = session_manager.get_session(session_key)
    
    if not session:
        return jsonify({"error": "Session not found"}), 404
    
    return jsonify({
        "session_key": session_key,
        "status": session['status'],
        "host_connected": session['host_connected'],
        "client_count": len(session['clients']),
        "created_at": session['created_at'].isoformat(),
        "last_activity": session['last_activity'].isoformat()
    })


@app.route("/assets/<path:filename>")
def custom_static(filename):
    return send_from_directory("../web/assets", filename)


@app.route("/detail/<path:filename>")
def custom_detail(filename):
    return send_from_directory("../web/detail", filename)


@app.route("/detail-light/<path:filename>")
def custom_detail_light(filename):
    return send_from_directory("../web/detail-light", filename)


@app.route("/")
def home():
    # Check if the user is authenticated
    if "authenticated" not in session:
        return redirect(url_for("login"))  # Redirect to login if not authenticated
    return render_template("index.html")  # board-login


# Render other HTML pages dynamically
@app.route("/<page_name>")
def render_page(page_name):
    # Check if the user is authenticated
    if "authenticated" not in session:
        return redirect(url_for("login"))  # Redirect to login if not authenticated

    try:
        return render_template(page_name)
    except:
        abort(404)


# Health check endpoint (no authentication required)
@app.route("/ping")
def ping():
    return jsonify({"status": "healthy", "service": "vse-app"}), 200


# ACME challenge endpoint
@app.route("/.well-known/acme-challenge/<path:token>")
def acme_challenge(token):
    return f"ACME challenge token: {token}", 404


# Login page
@app.route("/login", methods=["GET", "POST"])
def login():
    if request.method == "POST":
        password = request.form.get("password")
        if password == PASSWORD:
            session["authenticated"] = True  # Mark the user as authenticated
            return redirect(url_for("home"))  # Redirect to the home page
        else:
            flash("Incorrect password. Please try again.", "danger")
    return render_template("dummy-login.html")


# Logout route
@app.route("/logout")
def logout():
    session.pop("authenticated", None)  # Remove authentication from session
    return redirect(url_for("login"))  # Redirect to login page


# Handle 404 errors
@app.errorhandler(404)
def not_found(error):
    return render_template("404.html"), 404


# Health check route
@app.route("/health")
def health_check():
    """
    Health check endpoint to verify if the application is running.
    Returns a JSON response with status and an optional message.
    """
    return jsonify({"status": "healthy", "message": "Application is running"}), 200


@app.route("/set-ip", methods=["POST"])
def set_ip():
    kind_id = "X32"
    data = request.get_json()
    ip_address = data.get("ip")

    if not ip_address:
        return jsonify({"error": "No IP provided"}), 400

    global mixer
    mixer = xair_api.connect(kind_id, ip=ip_address, port=10023)

    try:
        mixer.__enter__()  # Establish connection

        # Get mute statuses for channels 1-32
        mute_status = {}
        fader_values = {}

        for i in range(1, 33):
            channel_id = str(i).zfill(2)

            # Fetch mute status
            mute_path = f"/ch/{channel_id}/mix/on"
            mute_value = mixer.query(mute_path)
            mute_status[i] = (
                int(mute_value[0]) if mute_value else 1
            )  # Default to unmuted if no response

            # Fetch fader value (0.0 - 1.0 scale)
            fader_path = f"/ch/{channel_id}/mix/fader"
            fader_value = mixer.query(fader_path)
            fader_values[i] = (
                round(float(fader_value[0]), 3) if fader_value else 0.0
            )  # Default to 0.0 if no response

        return jsonify(
            {
                "message": f"Connected successfully to {ip_address}",
                "mute_status": mute_status,
                "fader_values": fader_values,
            }
        )

    except Exception as e:
        return jsonify({"message": f"Failed to connect to {ip_address}: {e}"})


@app.route("/mute", methods=["POST"])
def mute_channel():
    data = request.get_json()
    mute_number = data.get("mute_number")
    action = data.get("action")

    if not mute_number or not action:
        return jsonify({"error": "Missing mute number or action"}), 400

    try:
        mute_number = int(mute_number)
        if mute_number < 1 or mute_number > 32:
            return jsonify({"error": "Invalid channel number"}), 400
    except ValueError:
        return jsonify({"error": "Invalid mute number format"}), 400

    mute_state = 0 if action == "mute" else 1

    try:
        mixer.send(f"/ch/{str(mute_number).zfill(2)}/mix/on", mute_state)
        print(f"Channel {mute_number} is now {action}")
        return jsonify(
            {
                "mute_number": mute_number,
                "action": action,
                "message": "Command sent successfully",
            }
        )
    except Exception as e:
        print(f"Error sending mute command: {e}")
        return jsonify({"error": "Failed to send mute command"}), 500


@app.route("/set-fader", methods=["POST"])
def set_fader():
    data = request.get_json()
    channel = data.get("channel")
    value = data.get("value")

    if not channel or value is None:
        return jsonify({"error": "Missing channel or value"}), 400

    try:
        channel = int(channel)
        value = float(value)

        if channel < 1 or channel > 32:
            return jsonify({"error": "Invalid channel number"}), 400
        if value < 0.0 or value > 1.0:
            return jsonify({"error": "Fader value out of range (0.0 - 1.0)"}), 400

        mixer.send(f"/ch/{str(channel).zfill(2)}/mix/fader", value)
        print(f"Set fader for channel {channel} to {value} (0.0 - 1.0 scale)")

        return jsonify(
            {
                "channel": channel,
                "value": value,
                "message": "Fader updated successfully",
            }
        )

    except Exception as e:
        return jsonify({"error": f"Failed to set fader: {e}"}), 500


# SocketIO event handlers with proper room isolation

@socketio.on("connect")
def handle_connect():
    print(f"Client connected: {request.sid}")


@socketio.on("disconnect")
def handle_disconnect():
    print(f"Client disconnected: {request.sid}")
    # Clean up user from any session they might be in
    # Note: We'll need to track request.sid -> user_id mapping


@socketio.on("join_session_room")
def handle_join_session_room(data):
    session_key = data.get("session_key")
    user_id = data.get("user_id")
    role = data.get("role", "client")
    client_info = data.get("client_info", {})  # Browser, IP, etc.
    
    if not session_key or not user_id:
        emit("error", {"message": "Session key and user ID required"})
        return
    
    session = session_manager.get_session(session_key)
    if not session:
        emit("error", {"message": "Session not found"})
        return
    
    # If this is a client trying to join, auto-approve for now (TODO: Fix popup approval later)
    if role == "client":
        print(f"AUTO-APPROVING client {user_id} for session {session_key}")
        
        # Auto-approve: Add client to session immediately
        success = session_manager.join_session(session_key, user_id, role='client')
        
        if success:
            # Join the SocketIO room immediately
            join_room(session_key)
            
            # Notify client of successful join
            emit("connection_approved", {
                "session_key": session_key,
                "message": "Auto-approved for testing"
            })
            
            # Notify others in the session
            emit("user_joined", {
                "user_id": user_id,
                "role": role,
                "session_key": session_key
            }, room=session_key, include_self=False)
            
            print(f"Client {user_id} auto-joined session {session_key}")
        else:
            emit("error", {"message": "Failed to join session"})
            
        return
    
    # Handle different roles
    if role == "host":
        # Host rejoining their own session
        join_room(session_key)
        
        # Update session activity
        session['last_activity'] = datetime.now()
        
        # Send session info to the joining host
        emit("session_joined", {
            "session_key": session_key,
            "role": role,
            "session_status": session['status'],
            "host_connected": session['host_connected'],
            "relay_connected": session.get('relay_connected', False),
            "client_count": len(session['clients'])
        })
        
        emit("user_joined", {
            "user_id": user_id,
            "role": role,
            "session_key": session_key
        }, room=session_key, include_self=False)
        
        return
        
    elif role == "relay":
        # Desktop app with X32 joining
        print(f"Relay {user_id} attempting to join session {session_key}")
        success, result = session_manager.join_session(session_key, user_id, role='relay')
        
        if success:
            join_room(session_key)
            
            print(f"Relay {user_id} successfully joined session {session_key}, sending confirmation")
            emit("session_joined", {
                "session_key": session_key,
                "role": role,
                "session_status": result['status'],
                "host_connected": result['host_connected'],
                "relay_connected": result['relay_connected'],
                "client_count": len(result['clients'])
            })
            
            emit("user_joined", {
                "user_id": user_id,
                "role": role,
                "session_key": session_key
            }, room=session_key, include_self=False)
            
            print(f"Relay {user_id} join complete for session {session_key}")
        else:
            print(f"Relay {user_id} failed to join session {session_key}: {result}")
            emit("error", {"message": f"Failed to join as relay: {result}"})
            
        return


@socketio.on("leave_session_room")
def handle_leave_session_room(data):
    session_key = data.get("session_key")
    user_id = data.get("user_id")
    
    if session_key and user_id:
        leave_room(session_key)
        
        # Notify others in the room
        emit("user_left", {
            "user_id": user_id,
            "session_key": session_key
        }, room=session_key)


@socketio.on("offer")
def handle_offer(data):
    session_key = data.get("session_key")
    user_id = data.get("user_id")
    offer = data.get("offer")
    
    if not session_key:
        emit("error", {"message": "Session key required"})
        return
    
    session = session_manager.get_session(session_key)
    if not session:
        emit("error", {"message": "Session not found"})
        return
    
    # Update session activity
    session['last_activity'] = datetime.now()
    
    # Send offer to others in the same room (excluding sender)
    emit("offer", {
        "session_key": session_key,
        "user_id": user_id,
        "offer": offer
    }, room=session_key, include_self=False)


@socketio.on("answer")
def handle_answer(data):
    session_key = data.get("session_key")
    user_id = data.get("user_id")
    answer = data.get("answer")
    
    if not session_key:
        emit("error", {"message": "Session key required"})
        return
    
    session = session_manager.get_session(session_key)
    if not session:
        emit("error", {"message": "Session not found"})
        return
    
    # Update session activity
    session['last_activity'] = datetime.now()
    
    # Send answer to others in the same room (excluding sender)
    emit("answer", {
        "session_key": session_key,
        "user_id": user_id,
        "answer": answer
    }, room=session_key, include_self=False)


@socketio.on("ice-candidate")
def handle_ice_candidate(data):
    session_key = data.get("session_key")
    user_id = data.get("user_id")
    candidate = data.get("candidate")
    
    if not session_key:
        emit("error", {"message": "Session key required"})
        return
    
    # Send ICE candidate to others in the same room (excluding sender)
    emit("ice-candidate", {
        "session_key": session_key,
        "user_id": user_id,
        "candidate": candidate
    }, room=session_key, include_self=False)


@socketio.on("approve_connection")
def handle_approve_connection(data):
    """Handle host approval of client connection"""
    session_key = data.get("session_key")
    request_id = data.get("request_id")
    approved = data.get("approved", False)
    
    if not session_key or not request_id:
        emit("error", {"message": "Session key and request ID required"})
        return
    
    session = session_manager.get_session(session_key)
    if not session:
        emit("error", {"message": "Session not found"})
        return
    
    # Check if request exists
    pending_requests = session.get('pending_requests', {})
    if request_id not in pending_requests:
        emit("error", {"message": "Request not found or already processed"})
        return
    
    request_info = pending_requests[request_id]
    user_id = request_info['user_id']
    socket_id = request_info['socket_id']
    
    if approved:
        # Approve the connection
        # Add client to session
        success, result = session_manager.join_session(session_key, user_id, 'client')
        
        if success:
            # Send approval to the waiting client
            socketio.emit("connection_approved", {
                "session_key": session_key,
                "message": "Connection approved by host"
            }, room=socket_id)
            
            # Now actually join the room
            socketio.emit("join_room_approved", {
                "session_key": session_key,
                "user_id": user_id
            }, room=socket_id)
            
            # Log the approval
            emit("approval_result", {
                "message": f"Approved connection for {user_id}",
                "user_id": user_id
            })
        else:
            emit("error", {"message": f"Failed to add client: {result}"})
    else:
        # Deny the connection
        socketio.emit("connection_denied", {
            "message": "Connection denied by host",
            "reason": data.get("reason", "No reason provided")
        }, room=socket_id)
        
        # Log the denial
        emit("approval_result", {
            "message": f"Denied connection for {user_id}",
            "user_id": user_id
        })
    
    # Remove the pending request
    del pending_requests[request_id]
    session['last_activity'] = datetime.now()


@socketio.on("client_join_approved")
def handle_client_join_approved(data):
    """Handle client joining after approval"""
    session_key = data.get("session_key")
    user_id = data.get("user_id")
    
    if not session_key or not user_id:
        emit("error", {"message": "Session key and user ID required"})
        return
    
    session = session_manager.get_session(session_key)
    if not session:
        emit("error", {"message": "Session not found"})
        return
    
    # Join the SocketIO room
    join_room(session_key)
    
    # Update session activity
    session['last_activity'] = datetime.now()
    
    # Notify others in the room
    emit("user_joined", {
        "user_id": user_id,
        "role": "client",
        "session_key": session_key
    }, room=session_key, include_self=False)
    
    # Send session info to the joining client
    emit("session_joined", {
        "session_key": session_key,
        "role": "client",
        "session_status": session['status'],
        "host_connected": session['host_connected'],
        "client_count": len(session['clients'])
    })


@socketio.on("osc_command")
def handle_osc_command(data):
    """Handle OSC commands from clients - route to host relay"""
    session_key = data.get("session_key")
    user_id = data.get("user_id")
    command = data.get("command")
    client_timestamp = data.get("client_timestamp")  # For latency measurement
    
    if not session_key or not command:
        emit("error", {"message": "Session key and command required"})
        return
    
    session = session_manager.get_session(session_key)
    if not session:
        emit("error", {"message": "Session not found"})
        return
    
    # Check if user is authorized (in the session)
    if user_id not in session.get('clients', {}) and user_id != session.get('host_user_id'):
        emit("error", {"message": "Unauthorized: User not in session"})
        return
    
    # Update session activity
    session['last_activity'] = datetime.now()
    
    # Forward OSC command to host (will be handled by host relay app)
    emit("osc_command", {
        "session_key": session_key,
        "user_id": user_id,
        "command": command,
        "client_timestamp": client_timestamp,  # Pass through for latency measurement
        "timestamp": datetime.now().isoformat()
    }, room=session_key)


@socketio.on("osc_executed")
def handle_osc_executed(data):
    """Handle OSC execution confirmations from relay - forward back to client"""
    session_key = data.get("session_key")
    
    if not session_key:
        return
    
    # Forward the confirmation to the session room (client will receive it)
    emit("osc_executed", data, room=session_key)
    print(f"OSC execution confirmation forwarded to session {session_key}")


@socketio.on("board_state_update")
def handle_board_state_update(data):
    """Handle board state updates from relay - forward to clients"""
    session_key = data.get("session_key")
    
    if not session_key:
        return
    
    # Forward the board state update to the session room (clients will receive it)
    emit("board_state_update", data, room=session_key)
    print(f"Board state update forwarded to session {session_key}")


# Periodic cleanup task
@socketio.on("cleanup_sessions")
def handle_cleanup_sessions():
    """Manual trigger for session cleanup - for testing"""
    expired_count = session_manager.cleanup_expired_sessions()
    emit("cleanup_result", {"expired_sessions": expired_count})


if __name__ == "__main__":
    # Run without SSL - Traefik handles HTTPS termination
    eventlet.wsgi.server(eventlet.listen(("0.0.0.0", 5000)), app)
