#!/bin/bash

# Kill Everything Script - Fresh AWS Account Cleanup
# Destroys ALL resources except default VPC in a fresh AWS account

set -e

echo "💀 KILL EVERYTHING - FRESH AWS ACCOUNT CLEANUP"
echo "=============================================="
echo "⚠️  WARNING: This will destroy ALL resources in your AWS account!"
echo "⚠️  Only the default VPC will be preserved."
echo ""

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[DESTROY]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

# Check if AWS CLI is configured
if ! aws sts get-caller-identity > /dev/null 2>&1; then
    echo "❌ AWS CLI is not configured. Please run 'aws configure' first."
    exit 1
fi

ACCOUNT_ID=$(aws sts get-caller-identity --query 'Account' --output text)
print_status "Cleaning AWS account: $ACCOUNT_ID"

# 1. DESTROY ALL ECS RESOURCES
echo ""
echo "🗑️  ECS RESOURCES"
echo "================="

# Delete all ECS services and clusters (AGGRESSIVE)
print_status "Destroying all ECS clusters and services..."
ECS_CLUSTERS=$(aws ecs list-clusters --region us-east-1 --query 'clusterArns[]' --output text 2>/dev/null)

echo "Found ECS clusters: $ECS_CLUSTERS"

for cluster_arn in $ECS_CLUSTERS; do
    if [ "$cluster_arn" != "None" ] && [ "$cluster_arn" != "" ]; then
        CLUSTER_NAME=$(echo $cluster_arn | cut -d'/' -f2)
        echo "🔧 Processing ECS cluster: $CLUSTER_NAME"
        
        # Scale down all services to 0 and force delete
        SERVICES=$(aws ecs list-services --cluster $CLUSTER_NAME --region us-east-1 --query 'serviceArns[]' --output text 2>/dev/null)
        echo "Found services in cluster $CLUSTER_NAME: $SERVICES"
        
        for service_arn in $SERVICES; do
            if [ "$service_arn" != "None" ] && [ "$service_arn" != "" ]; then
                SERVICE_NAME=$(echo $service_arn | cut -d'/' -f3)
                echo "🔧 Scaling down service: $SERVICE_NAME"
                aws ecs update-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --desired-count 0 --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Wait longer for services to scale down
        echo "⏳ Waiting for services to scale down..."
        sleep 30
        
        # Force delete services
        for service_arn in $SERVICES; do
            if [ "$service_arn" != "None" ] && [ "$service_arn" != "" ]; then
                SERVICE_NAME=$(echo $service_arn | cut -d'/' -f3)
                echo "🗑️  Force deleting service: $SERVICE_NAME"
                aws ecs delete-service --cluster $CLUSTER_NAME --service $SERVICE_NAME --force --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Wait for service deletion
        echo "⏳ Waiting for service deletion..."
        sleep 20
        
        # Delete cluster
        echo "🗑️  Deleting cluster: $CLUSTER_NAME"
        aws ecs delete-cluster --cluster $CLUSTER_NAME --region us-east-1 2>/dev/null || true
    fi
done

# Additional wait to ensure all ECS resources are cleaned up
echo "⏳ Final wait for ECS cleanup..."
sleep 30

# 2. DESTROY ALL LOAD BALANCERS
echo ""
echo "🗑️  LOAD BALANCERS"
echo "=================="

print_status "Destroying all load balancers..."
ALB_ARNS=$(aws elbv2 describe-load-balancers --region us-east-1 --query 'LoadBalancers[].LoadBalancerArn' --output text 2>/dev/null)
for alb_arn in $ALB_ARNS; do
    if [ "$alb_arn" != "None" ] && [ "$alb_arn" != "" ]; then
        aws elbv2 delete-load-balancer --load-balancer-arn $alb_arn --region us-east-1 2>/dev/null || true
    fi
done

# Delete all target groups
TG_ARNS=$(aws elbv2 describe-target-groups --region us-east-1 --query 'TargetGroups[].TargetGroupArn' --output text 2>/dev/null)
for tg_arn in $TG_ARNS; do
    if [ "$tg_arn" != "None" ] && [ "$tg_arn" != "" ]; then
        aws elbv2 delete-target-group --target-group-arn $tg_arn --region us-east-1 2>/dev/null || true
    fi
done

# 3. DESTROY ALL NON-DEFAULT VPCs (AGGRESSIVE MODE)
echo ""
echo "🗑️  NON-DEFAULT VPCs (AGGRESSIVE MODE)"
echo "======================================"

print_status "Listing all VPCs before cleanup..."
aws ec2 describe-vpcs --region us-east-1 --query 'Vpcs[].{VpcId:VpcId,CidrBlock:CidrBlock,IsDefault:IsDefault,State:State}' --output table 2>/dev/null || echo "Could not list VPCs"

print_status "Destroying all non-default VPCs..."
VPC_IDS=$(aws ec2 describe-vpcs --region us-east-1 --query 'Vpcs[?IsDefault==`false`].VpcId' --output text 2>/dev/null)

echo "Found non-default VPCs: $VPC_IDS"

for VPC_ID in $VPC_IDS; do
    if [ "$VPC_ID" != "None" ] && [ "$VPC_ID" != "" ]; then
        print_status "Destroying VPC: $VPC_ID"
        
        # STEP 1: Show all dependencies first
        echo "🔍 Analyzing VPC $VPC_ID dependencies..."
        
        # List all resources in this VPC
        echo "📋 Instances in VPC:"
        aws ec2 describe-instances --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'Reservations[].Instances[].{InstanceId:InstanceId,State:State.Name,Type:InstanceType}' --output table 2>/dev/null || echo "No instances found"
        
        echo "📋 Network interfaces in VPC:"
        aws ec2 describe-network-interfaces --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'NetworkInterfaces[].{NetworkInterfaceId:NetworkInterfaceId,Status:Status,Description:Description,AttachmentId:Attachment.AttachmentId}' --output table 2>/dev/null || echo "No network interfaces found"
        
        echo "📋 NAT Gateways in VPC:"
        aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'NatGateways[?State!=`deleted`].{NatGatewayId:NatGatewayId,State:State,SubnetId:SubnetId}' --output table 2>/dev/null || echo "No NAT gateways found"
        
        echo "📋 VPC Endpoints in VPC:"
        aws ec2 describe-vpc-endpoints --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'VpcEndpoints[].{VpcEndpointId:VpcEndpointId,State:State,ServiceName:ServiceName}' --output table 2>/dev/null || echo "No VPC endpoints found"
        
        # STEP 2: Terminate any running instances in this VPC
        echo "🖥️  Terminating instances in VPC $VPC_ID..."
        INSTANCE_IDS=$(aws ec2 describe-instances --filters "Name=vpc-id,Values=$VPC_ID" "Name=instance-state-name,Values=running,pending,stopping,stopped" --region us-east-1 --query 'Reservations[].Instances[].InstanceId' --output text 2>/dev/null)
        for instance_id in $INSTANCE_IDS; do
            if [ "$instance_id" != "None" ] && [ "$instance_id" != "" ]; then
                echo "🖥️  Terminating instance: $instance_id"
                aws ec2 terminate-instances --instance-ids $instance_id --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Wait for instances to terminate if any were found
        if [ ! -z "$INSTANCE_IDS" ] && [ "$INSTANCE_IDS" != "None" ]; then
            echo "⏳ Waiting for instances to terminate..."
            aws ec2 wait instance-terminated --instance-ids $INSTANCE_IDS --region us-east-1 2>/dev/null || echo "Instance termination wait completed or timed out"
        fi
        
        # STEP 3: Delete any load balancers in this VPC's subnets
        echo "⚖️  Deleting load balancers in VPC $VPC_ID..."
        VPC_SUBNETS=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'Subnets[].SubnetId' --output text 2>/dev/null)
        for subnet_id in $VPC_SUBNETS; do
            if [ "$subnet_id" != "None" ] && [ "$subnet_id" != "" ]; then
                # Find ALBs in this subnet
                ALB_ARNS=$(aws elbv2 describe-load-balancers --region us-east-1 --query "LoadBalancers[?contains(AvailabilityZones[].SubnetId, '$subnet_id')].LoadBalancerArn" --output text 2>/dev/null)
                for alb_arn in $ALB_ARNS; do
                    if [ "$alb_arn" != "None" ] && [ "$alb_arn" != "" ]; then
                        echo "⚖️  Deleting ALB: $alb_arn"
                        aws elbv2 delete-load-balancer --load-balancer-arn $alb_arn --region us-east-1 2>/dev/null || true
                    fi
                done
            fi
        done
        
        # Wait for load balancers to delete
        if [ ! -z "$ALB_ARNS" ] && [ "$ALB_ARNS" != "None" ]; then
            echo "⏳ Waiting for load balancers to be deleted..."
            sleep 60
        fi
        
        # STEP 4: Delete VPC Endpoints
        echo "🔗 Deleting VPC endpoints in VPC $VPC_ID..."
        VPC_ENDPOINTS=$(aws ec2 describe-vpc-endpoints --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'VpcEndpoints[].VpcEndpointId' --output text 2>/dev/null)
        for endpoint_id in $VPC_ENDPOINTS; do
            if [ "$endpoint_id" != "None" ] && [ "$endpoint_id" != "" ]; then
                echo "🔗 Deleting VPC endpoint: $endpoint_id"
                aws ec2 delete-vpc-endpoint --vpc-endpoint-id $endpoint_id --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Delete NAT Gateways
        NAT_GATEWAYS=$(aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'NatGateways[?State!=`deleted`].NatGatewayId' --output text 2>/dev/null)
        for nat_id in $NAT_GATEWAYS; do
            if [ "$nat_id" != "None" ] && [ "$nat_id" != "" ]; then
                aws ec2 delete-nat-gateway --nat-gateway-id $nat_id --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Detach and Delete Internet Gateways
        IGW_IDS=$(aws ec2 describe-internet-gateways --filters "Name=attachment.vpc-id,Values=$VPC_ID" --region us-east-1 --query 'InternetGateways[].InternetGatewayId' --output text 2>/dev/null)
        for igw_id in $IGW_IDS; do
            if [ "$igw_id" != "None" ] && [ "$igw_id" != "" ]; then
                aws ec2 detach-internet-gateway --internet-gateway-id $igw_id --vpc-id $VPC_ID --region us-east-1 2>/dev/null || true
                aws ec2 delete-internet-gateway --internet-gateway-id $igw_id --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Delete Security Groups (except default)
        SG_IDS=$(aws ec2 describe-security-groups --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'SecurityGroups[?GroupName!=`default`].GroupId' --output text 2>/dev/null)
        
        # Remove all security group rules first
        for sg_id in $SG_IDS; do
            if [ "$sg_id" != "None" ] && [ "$sg_id" != "" ]; then
                aws ec2 describe-security-groups --group-ids $sg_id --region us-east-1 --query 'SecurityGroups[0].IpPermissions' --output json 2>/dev/null | \
                aws ec2 revoke-security-group-ingress --group-id $sg_id --ip-permissions file:///dev/stdin --region us-east-1 2>/dev/null || true
                aws ec2 describe-security-groups --group-ids $sg_id --region us-east-1 --query 'SecurityGroups[0].IpPermissionsEgress' --output json 2>/dev/null | \
                aws ec2 revoke-security-group-egress --group-id $sg_id --ip-permissions file:///dev/stdin --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Delete security groups
        for sg_id in $SG_IDS; do
            if [ "$sg_id" != "None" ] && [ "$sg_id" != "" ]; then
                aws ec2 delete-security-group --group-id $sg_id --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Delete Subnets
        SUBNET_IDS=$(aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'Subnets[].SubnetId' --output text 2>/dev/null)
        for subnet_id in $SUBNET_IDS; do
            if [ "$subnet_id" != "None" ] && [ "$subnet_id" != "" ]; then
                aws ec2 delete-subnet --subnet-id $subnet_id --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Delete Route Tables (except main)
        ROUTE_TABLE_IDS=$(aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'RouteTables[?Associations[0].Main!=`true`].RouteTableId' --output text 2>/dev/null)
        for rt_id in $ROUTE_TABLE_IDS; do
            if [ "$rt_id" != "None" ] && [ "$rt_id" != "" ]; then
                aws ec2 delete-route-table --route-table-id $rt_id --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Delete Network Interfaces (aggressive cleanup)
        echo "🔌 Deleting network interfaces in VPC $VPC_ID..."
        NETWORK_INTERFACES=$(aws ec2 describe-network-interfaces --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'NetworkInterfaces[].NetworkInterfaceId' --output text 2>/dev/null)
        for eni_id in $NETWORK_INTERFACES; do
            if [ "$eni_id" != "None" ] && [ "$eni_id" != "" ]; then
                echo "🔌 Deleting network interface: $eni_id"
                
                # Detach if attached
                aws ec2 describe-network-interfaces --network-interface-ids $eni_id --region us-east-1 --query 'NetworkInterfaces[0].Attachment.AttachmentId' --output text 2>/dev/null | while read attachment_id; do
                    if [ "$attachment_id" != "None" ] && [ "$attachment_id" != "" ]; then
                        echo "🔗 Detaching network interface: $eni_id (attachment: $attachment_id)"
                        aws ec2 detach-network-interface --attachment-id $attachment_id --region us-east-1 2>/dev/null || true
                        sleep 5
                    fi
                done
                
                # Delete the network interface
                aws ec2 delete-network-interface --network-interface-id $eni_id --region us-east-1 2>/dev/null || true
            fi
        done
        
        # Wait a bit for network interfaces to be fully deleted
        if [ ! -z "$NETWORK_INTERFACES" ] && [ "$NETWORK_INTERFACES" != "None" ]; then
            echo "⏳ Waiting for network interfaces to be fully deleted..."
            sleep 30
        fi
        
        # Wait for NAT Gateways to be deleted before proceeding
        if [ ! -z "$NAT_GATEWAYS" ] && [ "$NAT_GATEWAYS" != "None" ]; then
            echo "⏳ Waiting for NAT Gateways to be fully deleted..."
            sleep 60  # NAT Gateways take time to delete
            
            # Check if they're actually gone
            for nat_id in $NAT_GATEWAYS; do
                if [ "$nat_id" != "None" ] && [ "$nat_id" != "" ]; then
                    echo "🔍 Checking NAT Gateway deletion status: $nat_id"
                    aws ec2 describe-nat-gateways --nat-gateway-ids $nat_id --region us-east-1 --query 'NatGateways[0].State' --output text 2>/dev/null || echo "NAT Gateway deleted"
                fi
            done
        fi
        
        # Delete the VPC with retries
        VPC_DELETE_ATTEMPTS=5
        for attempt in $(seq 1 $VPC_DELETE_ATTEMPTS); do
            echo "🗑️  Attempt $attempt/$VPC_DELETE_ATTEMPTS: Deleting VPC $VPC_ID"
            
            if aws ec2 delete-vpc --vpc-id $VPC_ID --region us-east-1 2>/dev/null; then
                echo "✅ VPC $VPC_ID deleted successfully"
                break
            else
                if [ $attempt -eq $VPC_DELETE_ATTEMPTS ]; then
                    echo "❌ Failed to delete VPC $VPC_ID after $VPC_DELETE_ATTEMPTS attempts"
                    
                    # Show what's still attached to debug
                    echo "🔍 Debugging VPC dependencies:"
                    aws ec2 describe-vpc-attribute --vpc-id $VPC_ID --attribute enableDnsHostnames --region us-east-1 2>/dev/null || echo "VPC not found or already deleted"
                    
                    # Check for remaining dependencies
                    echo "🔍 Checking for remaining network interfaces:"
                    aws ec2 describe-network-interfaces --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'NetworkInterfaces[].{NetworkInterfaceId:NetworkInterfaceId,Status:Status,Description:Description}' --output table 2>/dev/null || echo "No network interfaces found"
                    
                    # Check for remaining instances
                    echo "🔍 Checking for remaining instances:"
                    aws ec2 describe-instances --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'Reservations[].Instances[].{InstanceId:InstanceId,State:State.Name}' --output table 2>/dev/null || echo "No instances found"
                    
                    # Check for remaining subnets
                    echo "🔍 Checking for remaining subnets:"
                    aws ec2 describe-subnets --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'Subnets[].{SubnetId:SubnetId,State:State,AvailabilityZone:AvailabilityZone}' --output table 2>/dev/null || echo "No subnets found"
                    
                    # Check for remaining security groups
                    echo "🔍 Checking for remaining security groups:"
                    aws ec2 describe-security-groups --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'SecurityGroups[].{GroupId:GroupId,GroupName:GroupName}' --output table 2>/dev/null || echo "No security groups found"
                    
                    # Check for remaining route tables
                    echo "🔍 Checking for remaining route tables:"
                    aws ec2 describe-route-tables --filters "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'RouteTables[].{RouteTableId:RouteTableId,Main:Associations[0].Main}' --output table 2>/dev/null || echo "No route tables found"
                    
                    # Check for remaining NAT gateways
                    echo "🔍 Checking for remaining NAT gateways:"
                    aws ec2 describe-nat-gateways --filter "Name=vpc-id,Values=$VPC_ID" --region us-east-1 --query 'NatGateways[?State!=`deleted`].{NatGatewayId:NatGatewayId,State:State}' --output table 2>/dev/null || echo "No NAT gateways found"
                    
                    # Check for remaining internet gateways
                    echo "🔍 Checking for remaining internet gateways:"
                    aws ec2 describe-internet-gateways --filters "Name=attachment.vpc-id,Values=$VPC_ID" --region us-east-1 --query 'InternetGateways[].{InternetGatewayId:InternetGatewayId,State:Attachments[0].State}' --output table 2>/dev/null || echo "No internet gateways found"
                    
                    # Final attempt - try to get the exact error
                    echo "🔍 Attempting VPC deletion with full error output:"
                    aws ec2 delete-vpc --vpc-id $VPC_ID --region us-east-1 2>&1 || true
                else
                    echo "⏳ VPC deletion failed, waiting 30 seconds before retry..."
                    sleep 30
                fi
            fi
        done
    fi
done

# 4. DESTROY ALL ACM CERTIFICATES
echo ""
echo "🗑️  ACM CERTIFICATES"
echo "===================="

print_status "Destroying all ACM certificates..."
CERT_ARNS=$(aws acm list-certificates --region us-east-1 --query 'CertificateSummaryList[].CertificateArn' --output text 2>/dev/null)
for cert_arn in $CERT_ARNS; do
    if [ "$cert_arn" != "None" ] && [ "$cert_arn" != "" ]; then
        aws acm delete-certificate --certificate-arn $cert_arn --region us-east-1 2>/dev/null || true
    fi
done

# 5. DESTROY ALL ROUTE 53 HOSTED ZONES
echo ""
echo "🗑️  ROUTE 53 HOSTED ZONES"
echo "========================="

print_status "Destroying all Route 53 hosted zones..."
HOSTED_ZONES=$(aws route53 list-hosted-zones --query 'HostedZones[].Id' --output text 2>/dev/null)

for zone_id in $HOSTED_ZONES; do
    if [ "$zone_id" != "None" ] && [ "$zone_id" != "" ]; then
        CLEAN_ZONE_ID=$(echo $zone_id | cut -d'/' -f3)
        
        # Delete all records except NS and SOA
        aws route53 list-resource-record-sets --hosted-zone-id $CLEAN_ZONE_ID --query 'ResourceRecordSets[?Type!=`NS` && Type!=`SOA`]' --output json 2>/dev/null | \
        jq -r '.[] | @base64' 2>/dev/null | while read record; do
            echo $record | base64 --decode 2>/dev/null | jq -r '. as $item | {Action: "DELETE", ResourceRecordSet: $item}' 2>/dev/null | \
            aws route53 change-resource-record-sets --hosted-zone-id $CLEAN_ZONE_ID --change-batch file:///dev/stdin 2>/dev/null || true
        done
        
        # Delete the hosted zone
        aws route53 delete-hosted-zone --id $CLEAN_ZONE_ID 2>/dev/null || true
    fi
done

# 6. DESTROY ALL ECR REPOSITORIES
echo ""
echo "🗑️  ECR REPOSITORIES"
echo "==================="

print_status "Destroying all ECR repositories..."
ECR_REPOS=$(aws ecr describe-repositories --region us-east-1 --query 'repositories[].repositoryName' --output text 2>/dev/null)
for repo_name in $ECR_REPOS; do
    if [ "$repo_name" != "None" ] && [ "$repo_name" != "" ]; then
        aws ecr delete-repository --repository-name $repo_name --force --region us-east-1 2>/dev/null || true
    fi
done

# 7. DESTROY ALL IAM RESOURCES (except AWS managed)
echo ""
echo "🗑️  IAM RESOURCES"
echo "================="

print_status "Destroying all custom IAM resources..."

# Delete all custom IAM roles
IAM_ROLES=$(aws iam list-roles --query 'Roles[?!starts_with(RoleName, `AWS`) && !starts_with(RoleName, `service-role`) && !starts_with(RoleName, `OrganizationAccountAccessRole`)].RoleName' --output text 2>/dev/null)
for role_name in $IAM_ROLES; do
    if [ "$role_name" != "None" ] && [ "$role_name" != "" ]; then
        # Detach all managed policies
        ATTACHED_POLICIES=$(aws iam list-attached-role-policies --role-name $role_name --query 'AttachedPolicies[].PolicyArn' --output text 2>/dev/null)
        for policy_arn in $ATTACHED_POLICIES; do
            if [ "$policy_arn" != "None" ] && [ "$policy_arn" != "" ]; then
                aws iam detach-role-policy --role-name $role_name --policy-arn $policy_arn 2>/dev/null || true
            fi
        done
        
        # Delete inline policies
        INLINE_POLICIES=$(aws iam list-role-policies --role-name $role_name --query 'PolicyNames[]' --output text 2>/dev/null)
        for policy_name in $INLINE_POLICIES; do
            if [ "$policy_name" != "None" ] && [ "$policy_name" != "" ]; then
                aws iam delete-role-policy --role-name $role_name --policy-name $policy_name 2>/dev/null || true
            fi
        done
        
        # Delete the role
        aws iam delete-role --role-name $role_name 2>/dev/null || true
    fi
done

# Delete all custom IAM policies
IAM_POLICIES=$(aws iam list-policies --scope Local --query 'Policies[].PolicyName' --output text 2>/dev/null)
for policy_name in $IAM_POLICIES; do
    if [ "$policy_name" != "None" ] && [ "$policy_name" != "" ]; then
        POLICY_ARN=$(aws iam list-policies --scope Local --query "Policies[?PolicyName=='$policy_name'].Arn" --output text 2>/dev/null)
        if [ "$POLICY_ARN" != "None" ] && [ "$POLICY_ARN" != "" ]; then
            aws iam delete-policy --policy-arn $POLICY_ARN 2>/dev/null || true
        fi
    fi
done

# 8. DESTROY ALL CLOUDWATCH RESOURCES
echo ""
echo "🗑️  CLOUDWATCH RESOURCES"
echo "======================="

print_status "Destroying all CloudWatch log groups..."
LOG_GROUPS=$(aws logs describe-log-groups --region us-east-1 --query 'logGroups[].logGroupName' --output text 2>/dev/null)
for log_group in $LOG_GROUPS; do
    if [ "$log_group" != "None" ] && [ "$log_group" != "" ]; then
        aws logs delete-log-group --log-group-name $log_group --region us-east-1 2>/dev/null || true
    fi
done

# 9. DESTROY ALL S3 BUCKETS (except AWS managed)
echo ""
echo "🗑️  S3 BUCKETS"
echo "=============="

print_status "Destroying all custom S3 buckets..."
S3_BUCKETS=$(aws s3api list-buckets --query 'Buckets[?!starts_with(Name, `aws-`) && !starts_with(Name, `cf-`)].Name' --output text 2>/dev/null)
for bucket_name in $S3_BUCKETS; do
    if [ "$bucket_name" != "None" ] && [ "$bucket_name" != "" ]; then
        # Empty the bucket first
        aws s3 rm s3://$bucket_name --recursive 2>/dev/null || true
        # Delete the bucket
        aws s3api delete-bucket --bucket $bucket_name --region us-east-1 2>/dev/null || true
    fi
done

# 10. CLEAN UP LOCAL TERRAFORM FILES
echo ""
echo "🗑️  LOCAL CLEANUP"
echo "================="

print_status "Cleaning up local Terraform files..."
find . -name ".terraform" -type d -exec rm -rf {} + 2>/dev/null || true
find . -name "terraform.tfstate*" -type f -delete 2>/dev/null || true
find . -name ".terraform.lock.hcl" -type f -delete 2>/dev/null || true

# FINAL VERIFICATION
echo ""
echo "🔍 FINAL VERIFICATION"
echo "===================="

print_status "Checking remaining VPCs..."
REMAINING_VPCS=$(aws ec2 describe-vpcs --region us-east-1 --query 'Vpcs[].{VpcId:VpcId,CidrBlock:CidrBlock,IsDefault:IsDefault}' --output table 2>/dev/null)
if [ $? -eq 0 ]; then
    echo "$REMAINING_VPCS"
else
    echo "Could not check remaining VPCs"
fi

print_status "Checking remaining ECS clusters..."
aws ecs list-clusters --region us-east-1 --query 'clusterArns' --output text 2>/dev/null || echo "No ECS clusters found"

print_status "Checking remaining load balancers..."
aws elbv2 describe-load-balancers --region us-east-1 --query 'LoadBalancers[].LoadBalancerName' --output text 2>/dev/null || echo "No load balancers found"

print_status "Checking remaining ECR repositories..."
aws ecr describe-repositories --region us-east-1 --query 'repositories[].repositoryName' --output text 2>/dev/null || echo "No ECR repositories found"

echo ""
echo "💀 KILL EVERYTHING COMPLETED!"
echo "============================="
echo ""
print_status "Summary of destroyed resources:"
echo "- All ECS clusters and services"
echo "- All Load Balancers and Target Groups"
echo "- All non-default VPCs and networking"
echo "- All ACM certificates"
echo "- All Route 53 hosted zones"
echo "- All ECR repositories"
echo "- All custom IAM roles and policies"
echo "- All CloudWatch log groups"
echo "- All custom S3 buckets"
echo "- All local Terraform files"
echo ""
echo "✅ Default VPC preserved"
echo "🚀 Ready for fresh deployment!"
