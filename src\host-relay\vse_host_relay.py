#!/usr/bin/env python3
"""
VSE Host Relay Application

A lightweight desktop application that runs on the host's computer to bridge
OSC messages between the VSE web server and the local X32 mixer.

Features:
- WebSocket connection to VSE server
- Local X32 auto-discovery and connection
- OSC message relay (bidirectional)
- Simple GUI for status monitoring
- Cross-platform executable generation
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import json
import time
import socket
import logging
from datetime import datetime
from typing import Optional, Dict, Any
import queue
import sys
import os
import ipaddress
import subprocess
import platform
import random

# Third-party imports (will be included in requirements)

try:
    import socketio
    import xair_api
    # python-socketio is needed for Flask-SocketIO communication
    # xair_api handles all OSC communication
except ImportError as e:
    print(f"Missing required dependencies: {e}")
    print("Please install: pip install python-socketio xair-api")
    sys.exit(1)


class VSEHostRelay:
    def __init__(self):
        self.version = "1.0.0"
        self.app_name = "VSE Host Relay"
        
        # Connection states
        self.server_connected = False
        self.x32_connected = False
        self.session_active = False
        
        # Configuration
        self.config = {
            'server_url': 'https://vseaudiobeta.com',  # Default to production server
            'x32_ip': "*************",  # Auto-discovered or manual
            'x32_port': 10023,
            'session_key': None,
            'host_user_id': None,
            'auto_connect': True,
            'log_level': 'INFO'
        }
        
        # Network objects
        self.socketio_client = None
        self.x32_mixer = None
        self.osc_server = None
        
        # Threading
        self.message_queue = queue.Queue()
        self.running = True
        
        # X32 channel state tracking
        self.channel_states = {}  # Track mute/unmute states for channels
        
        # Logging setup
        self.setup_logging()
        
        # GUI setup
        self.setup_gui()
        
        # Start background threads
        self.start_background_threads()

    def setup_logging(self):
        """Setup logging configuration"""
        logging.basicConfig(
            level=getattr(logging, self.config['log_level']),
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('vse_host_relay.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger(__name__)

    def setup_gui(self):
        """Create the main GUI interface"""
        self.root = tk.Tk()
        self.root.title(f"{self.app_name} v{self.version}")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # Configure style
        style = ttk.Style()
        style.theme_use('clam')
        
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        
        # Title
        title_label = ttk.Label(main_frame, text=self.app_name, font=('Arial', 16, 'bold'))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 20))
        
        # Connection Status Section
        status_frame = ttk.LabelFrame(main_frame, text="Connection Status", padding="10")
        status_frame.grid(row=1, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        status_frame.columnconfigure(1, weight=1)
        
        # Server status
        ttk.Label(status_frame, text="VSE Server:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.server_status_var = tk.StringVar(value="Disconnected")
        self.server_status_label = ttk.Label(status_frame, textvariable=self.server_status_var, foreground="red")
        self.server_status_label.grid(row=0, column=1, sticky=tk.W)
        
        # X32 status
        ttk.Label(status_frame, text="X32 Mixer:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.x32_status_var = tk.StringVar(value="Disconnected")
        self.x32_status_label = ttk.Label(status_frame, textvariable=self.x32_status_var, foreground="red")
        self.x32_status_label.grid(row=1, column=1, sticky=tk.W)
        
        # Session status
        ttk.Label(status_frame, text="Session:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.session_status_var = tk.StringVar(value="No Active Session")
        self.session_status_label = ttk.Label(status_frame, textvariable=self.session_status_var, foreground="gray")
        self.session_status_label.grid(row=2, column=1, sticky=tk.W)
        
        # Configuration Section
        config_frame = ttk.LabelFrame(main_frame, text="Configuration", padding="10")
        config_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(0, 10))
        config_frame.columnconfigure(1, weight=1)
        
        # Server URL
        ttk.Label(config_frame, text="Server URL:").grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.server_url_var = tk.StringVar(value=self.config['server_url'])
        server_url_entry = ttk.Entry(config_frame, textvariable=self.server_url_var, width=50)
        server_url_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))
        
        # X32 IP
        ttk.Label(config_frame, text="X32 IP:").grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.x32_ip_var = tk.StringVar(value=self.config.get('x32_ip', ''))
        x32_ip_entry = ttk.Entry(config_frame, textvariable=self.x32_ip_var, width=20)
        x32_ip_entry.grid(row=1, column=1, sticky=tk.W, padx=(0, 10))
        
        # Auto-discover button
        discover_btn = ttk.Button(config_frame, text="Auto Discover", command=self.discover_x32)
        discover_btn.grid(row=1, column=2)
        
        # Session Key
        ttk.Label(config_frame, text="Session Key:").grid(row=2, column=0, sticky=tk.W, padx=(0, 10))
        self.session_key_var = tk.StringVar()
        session_key_entry = ttk.Entry(config_frame, textvariable=self.session_key_var, width=10)
        session_key_entry.grid(row=2, column=1, sticky=tk.W, padx=(0, 10))
        
        # Control Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.grid(row=3, column=0, columnspan=3, pady=10)
        
        self.connect_btn = ttk.Button(button_frame, text="Connect", command=self.connect_all)
        self.connect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        self.disconnect_btn = ttk.Button(button_frame, text="Disconnect", command=self.disconnect_all, state=tk.DISABLED)
        self.disconnect_btn.pack(side=tk.LEFT, padx=(0, 10))
        
        # Test buttons
        self.test_mute_btn = ttk.Button(button_frame, text="Toggle Mute Ch1", command=self.test_mute_channel_1)
        self.test_mute_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        self.test_fader_btn = ttk.Button(button_frame, text="Test Fader Ch6", command=self.test_fader_channel_6)
        self.test_fader_btn.pack(side=tk.LEFT, padx=(0, 5))
        
        # Activity Log
        log_frame = ttk.LabelFrame(main_frame, text="Activity Log", padding="10")
        log_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        main_frame.rowconfigure(4, weight=1)
        
        self.log_text = scrolledtext.ScrolledText(log_frame, height=15, state=tk.DISABLED)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Clear log button
        clear_log_btn = ttk.Button(log_frame, text="Clear Log", command=self.clear_log)
        clear_log_btn.grid(row=1, column=0, sticky=tk.E, pady=(5, 0))
        
        # Status bar
        self.status_bar = ttk.Label(main_frame, text="Ready", relief=tk.SUNKEN, anchor=tk.W)
        self.status_bar.grid(row=5, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        
        # Bind close event
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
        
        # Initial log message
        self.log_message("VSE Host Relay initialized", "INFO")

    def start_background_threads(self):
        """Start background processing threads"""
        # Message processing thread
        self.message_thread = threading.Thread(target=self.process_messages, daemon=True)
        self.message_thread.start()
        
        # GUI update thread
        self.gui_update_thread = threading.Thread(target=self.update_gui, daemon=True)
        self.gui_update_thread.start()

    def log_message(self, message: str, level: str = "INFO"):
        """Add a message to the log queue"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {level}: {message}"
        self.message_queue.put(('log', log_entry))
        
        # Also log to file
        log_level = level.lower()
        if log_level == 'success':
            log_level = 'info'  # Map SUCCESS to INFO for file logging
        getattr(self.logger, log_level)(message)

    def update_gui(self):
        """Background thread to update GUI elements"""
        while self.running:
            try:
                # Process message queue
                while not self.message_queue.empty():
                    msg_type, data = self.message_queue.get_nowait()
                    
                    if msg_type == 'log':
                        self.root.after(0, self._add_log_entry, data)
                    elif msg_type == 'status_update':
                        self.root.after(0, self._update_status, data)
                    elif msg_type == 'connection_request':
                        self.root.after(0, self._show_approval_dialog, data)
                
                time.sleep(0.1)
            except Exception as e:
                print(f"GUI update error: {e}")

    def _add_log_entry(self, entry: str):
        """Add entry to log text widget (called from main thread)"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.insert(tk.END, entry + "\n")
        self.log_text.see(tk.END)
        self.log_text.config(state=tk.DISABLED)

    def _update_status(self, status_data: Dict[str, Any]):
        """Update status indicators (called from main thread)"""
        if 'server' in status_data:
            connected = status_data['server']
            self.server_status_var.set("Connected" if connected else "Disconnected")
            self.server_status_label.config(foreground="green" if connected else "red")
            
        if 'x32' in status_data:
            connected = status_data['x32']
            ip = status_data.get('x32_ip', '')
            status_text = f"Connected ({ip})" if connected and ip else ("Connected" if connected else "Disconnected")
            self.x32_status_var.set(status_text)
            self.x32_status_label.config(foreground="green" if connected else "red")
            
        if 'session' in status_data:
            active = status_data['session']
            session_key = status_data.get('session_key', '')
            status_text = f"Active ({session_key})" if active and session_key else ("Active" if active else "No Active Session")
            self.session_status_var.set(status_text)
            self.session_status_label.config(foreground="green" if active else "gray")

    def discover_x32(self):
        """Auto-discover X32 on all active network interfaces"""
        self.log_message("Scanning for X32 mixer on all network interfaces...", "INFO")
        
        def scan_network():
            try:
                found_devices = []
                self._found_in_current_network = False
                
                # Get all active network interfaces dynamically
                self.log_message("Getting active network interfaces...", "INFO")
                network_ranges = self._get_active_network_ranges()
                
                if not network_ranges:
                    self.log_message("No active network interfaces found", "WARNING")
                    return
                
                self.log_message(f"Found {len(network_ranges)} active network interfaces:", "INFO")
                for i, network_info in enumerate(network_ranges, 1):
                    self.log_message(f"  {i}. {network_info['network']} on {network_info['interface']}", "INFO")
                
                # Scan each discovered network range
                for network_info in network_ranges:
                    if not self.running:
                        break
                    
                    network_range = network_info['network']
                    interface_name = network_info['interface']
                    
                    self.log_message(f"Scanning {network_range} on {interface_name}...", "INFO")
                    
                    # Generate all possible IPs in this network
                    try:
                        network = ipaddress.IPv4Network(network_range, strict=False)
                        
                        # For large networks, limit the scan to avoid excessive time
                        max_hosts = min(254, network.num_addresses - 2)  # Exclude network and broadcast
                        
                        host_ips = list(network.hosts())[:max_hosts]
                        
                        # Fast scan: Test common IPs first, then broadcast search
                        self.log_message(f"Quick scan of {network_range}...", "INFO")
                        
                        # Common X32 IP addresses to test first
                        priority_ips = []
                        network_base = str(network.network_address).rsplit('.', 1)[0]
                        
                        # Add common device IPs
                        common_last_octets = [1, 2, 10, 20, 50, 100, 200, 240, 250, 254]
                        for octet in common_last_octets:
                            test_ip = f"{network_base}.{octet}"
                            try:
                                ip_obj = ipaddress.IPv4Address(test_ip)
                                if ip_obj in network:
                                    priority_ips.append(str(ip_obj))
                            except:
                                continue
                        
                        # Test priority IPs first (fast)
                        for ip in priority_ips:
                            if not self.running:
                                break
                            
                            # Always log when testing your specific IP
                            if str(ip) == "*************":
                                self.log_message(f"Testing your X32 at {ip}...", "INFO")
                                
                            if self._test_x32_connection(str(ip)):
                                found_devices.append(str(ip))
                                self.log_message(f"X32 found at {ip} in network {network_range}", "SUCCESS")
                                # Found one, move to next network
                                break
                        
                        # If not found in priority IPs and this network might have devices, try a few more
                        if not found_devices:
                            self.log_message(f"Priority scan complete for {network_range}, skipping full scan", "INFO")
                                
                    except ValueError as e:
                        self.log_message(f"Invalid network range {network_range}: {e}", "ERROR")
                        continue
                
                if found_devices:
                    # Use the first found device
                    discovered_ip = found_devices[0]
                    self.log_message(f"Setting IP field to: {discovered_ip}", "INFO")
                    
                    # Update GUI in main thread
                    def update_ip_field():
                        try:
                            self.x32_ip_var.set(discovered_ip)
                            self.log_message(f"IP field updated successfully to {discovered_ip}", "SUCCESS")
                        except Exception as e:
                            self.log_message(f"Failed to update IP field: {e}", "ERROR")
                    
                    self.root.after(0, update_ip_field)
                    self.log_message(f"Auto-discovery complete. Found X32 at {discovered_ip}", "SUCCESS")
                    
                    # Log all found devices
                    if len(found_devices) > 1:
                        other_devices = ', '.join(found_devices[1:])
                        self.log_message(f"Additional X32 devices found: {other_devices}", "INFO")
                else:
                    self.log_message("No X32 mixer found on any active network interfaces", "WARNING")
                    self.log_message("Ensure X32 is powered on and connected to the network", "INFO")
                    
            except Exception as e:
                self.log_message(f"Auto-discovery failed: {e}", "ERROR")
        
        # Run discovery in background thread
        discovery_thread = threading.Thread(target=scan_network, daemon=True)
        discovery_thread.start()

    def _get_active_network_ranges(self):
        """Get all active network interfaces and their IP ranges"""
        network_ranges = []
        system = platform.system().lower()
        
        try:
            if system == "windows":
                network_ranges = self._get_windows_networks()
            elif system in ["linux", "darwin"]:  # Linux or macOS
                network_ranges = self._get_unix_networks()
            else:
                self.log_message(f"Unsupported system: {system}", "WARNING")
                
        except Exception as e:
            self.log_message(f"Error getting network interfaces: {e}", "ERROR")
            
        return network_ranges

    def _get_windows_networks(self):
        """Get active network interfaces on Windows using ipconfig"""
        network_ranges = []
        
        try:
            # Run ipconfig to get network information
            result = subprocess.run(['ipconfig'], capture_output=True, text=True, timeout=10)
            
            if result.returncode != 0:
                self.log_message("Failed to run ipconfig", "ERROR")
                return network_ranges
            
            lines = result.stdout.split('\n')
            current_adapter = None
            current_ip = None
            current_subnet = None
            
            for line in lines:
                line = line.strip()
                
                # Detect adapter name
                if 'adapter' in line and ':' in line:
                    current_adapter = line
                    current_ip = None
                    current_subnet = None
                    continue
                
                # Skip disconnected adapters
                if 'Media disconnected' in line:
                    current_adapter = None
                    continue
                
                # Extract IPv4 address
                if 'IPv4 Address' in line and ':' in line:
                    try:
                        current_ip = line.split(':')[1].strip()
                        # Remove any additional info in parentheses
                        if '(' in current_ip:
                            current_ip = current_ip.split('(')[0].strip()
                    except:
                        continue
                
                # Extract subnet mask
                if 'Subnet Mask' in line and ':' in line:
                    try:
                        current_subnet = line.split(':')[1].strip()
                    except:
                        continue
                
                # If we have both IP and subnet, calculate network range
                if current_ip and current_subnet and current_adapter:
                    try:
                        # Create network from IP and subnet mask
                        network = ipaddress.IPv4Network(f"{current_ip}/{current_subnet}", strict=False)
                        
                        network_ranges.append({
                            'network': str(network),
                            'interface': current_adapter,
                            'ip': current_ip,
                            'subnet': current_subnet
                        })
                        
                        self.log_message(f"Found network: {network} on {current_adapter}", "INFO")
                        
                        # Reset for next adapter
                        current_adapter = None
                        current_ip = None
                        current_subnet = None
                        
                    except ValueError as e:
                        self.log_message(f"Invalid network {current_ip}/{current_subnet}: {e}", "WARNING")
                        continue
                        
        except subprocess.TimeoutExpired:
            self.log_message("ipconfig command timed out", "ERROR")
        except Exception as e:
            self.log_message(f"Error parsing ipconfig output: {e}", "ERROR")
            
        return network_ranges

    def _get_unix_networks(self):
        """Get active network interfaces on Linux/macOS using ip or ifconfig"""
        network_ranges = []
        
        try:
            # Try 'ip' command first (modern Linux)
            try:
                result = subprocess.run(['ip', 'addr', 'show'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return self._parse_ip_command(result.stdout)
            except FileNotFoundError:
                pass  # 'ip' command not available
            
            # Fall back to 'ifconfig' (older systems, macOS)
            try:
                result = subprocess.run(['ifconfig'], capture_output=True, text=True, timeout=10)
                if result.returncode == 0:
                    return self._parse_ifconfig_command(result.stdout)
            except FileNotFoundError:
                pass  # 'ifconfig' command not available
                
            self.log_message("Neither 'ip' nor 'ifconfig' commands available", "ERROR")
            
        except Exception as e:
            self.log_message(f"Error getting Unix network interfaces: {e}", "ERROR")
            
        return network_ranges

    def _parse_ip_command(self, output):
        """Parse 'ip addr show' output"""
        network_ranges = []
        
        lines = output.split('\n')
        current_interface = None
        
        for line in lines:
            line = line.strip()
            
            # Interface line
            if line and line[0].isdigit() and ':' in line:
                parts = line.split(':')
                if len(parts) >= 2:
                    current_interface = parts[1].strip()
                continue
            
            # IP address line
            if 'inet ' in line and current_interface:
                try:
                    parts = line.split()
                    for part in parts:
                        if '/' in part and not part.startswith('inet'):
                            # This should be the IP/CIDR
                            network = ipaddress.IPv4Network(part, strict=False)
                            
                            network_ranges.append({
                                'network': str(network),
                                'interface': current_interface,
                                'ip': str(network.network_address),
                                'subnet': str(network.netmask)
                            })
                            
                            self.log_message(f"Found network: {network} on {current_interface}", "INFO")
                            break
                            
                except (ValueError, IndexError):
                    continue
                    
        return network_ranges

    def _parse_ifconfig_command(self, output):
        """Parse 'ifconfig' output"""
        network_ranges = []
        
        # Split by interface blocks
        interface_blocks = output.split('\n\n')
        
        for block in interface_blocks:
            lines = block.split('\n')
            if not lines:
                continue
                
            # First line should contain interface name
            interface_line = lines[0]
            if ':' not in interface_line:
                continue
                
            interface_name = interface_line.split(':')[0].strip()
            
            # Look for inet line
            for line in lines:
                if 'inet ' in line:
                    try:
                        # Extract IP and netmask
                        parts = line.split()
                        ip_addr = None
                        netmask = None
                        
                        for i, part in enumerate(parts):
                            if part == 'inet' and i + 1 < len(parts):
                                ip_addr = parts[i + 1]
                            elif part == 'netmask' and i + 1 < len(parts):
                                netmask = parts[i + 1]
                        
                        if ip_addr and netmask:
                            # Convert hex netmask to dotted decimal if needed
                            if netmask.startswith('0x'):
                                netmask = str(ipaddress.IPv4Address(int(netmask, 16)))
                            
                            network = ipaddress.IPv4Network(f"{ip_addr}/{netmask}", strict=False)
                            
                            network_ranges.append({
                                'network': str(network),
                                'interface': interface_name,
                                'ip': ip_addr,
                                'subnet': netmask
                            })
                            
                            self.log_message(f"Found network: {network} on {interface_name}", "INFO")
                            break
                            
                    except (ValueError, IndexError):
                        continue
                        
        return network_ranges

    def _test_x32_connection(self, ip_address):
        """Test if an IP address has an X32 mixer - FAST version"""
        is_target_ip = ip_address == "*************"
        
        try:
            # Very quick port scan
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(0.1)  # Very fast timeout
            result = sock.connect_ex((ip_address, 10023))  # X32 OSC port
            sock.close()
            
            if is_target_ip:
                if result == 0:
                    self.log_message(f"Port 10023 is OPEN on {ip_address} - proceeding with X32 test", "INFO")
                else:
                    self.log_message(f"Port 10023 is CLOSED on {ip_address} (error code: {result})", "WARNING")
                    return False
            
            if result == 0:  # Port is open
                # Quick X32 verification - try the most common model first
                try:
                    if is_target_ip:
                        self.log_message(f"Testing X32 model at {ip_address}...", "INFO")
                    
                    # Most X32s respond to "X32" - try this first for speed
                    test_mixer = xair_api.connect("X32", ip=ip_address, port=10023)
                    test_mixer.__enter__()
                    response = test_mixer.query("/info")
                    test_mixer.__exit__(None, None, None)
                    
                    if response:
                        if is_target_ip:
                            self.log_message(f"X32 model SUCCESS at {ip_address} - Response: {response}", "SUCCESS")
                        else:
                            self.log_message(f"X32 confirmed at {ip_address}", "SUCCESS")
                        return True
                    
                    if is_target_ip:
                        self.log_message(f"X32 model gave no response, trying X32P at {ip_address}...", "INFO")
                    
                    # If X32 didn't work, try X32P (your model)
                    test_mixer = xair_api.connect("X32P", ip=ip_address, port=10023)
                    test_mixer.__enter__()
                    response = test_mixer.query("/info")
                    test_mixer.__exit__(None, None, None)
                    
                    if response:
                        if is_target_ip:
                            self.log_message(f"X32P model SUCCESS at {ip_address} - Response: {response}", "SUCCESS")
                        else:
                            self.log_message(f"X32P confirmed at {ip_address}", "SUCCESS")
                        return True
                    
                    if is_target_ip:
                        self.log_message(f"X32P model also gave no response at {ip_address}", "WARNING")
                        
                except Exception as e:
                    if is_target_ip:
                        self.log_message(f"X32 API test failed at {ip_address}: {e}", "WARNING")
                        
        except Exception as e:
            if is_target_ip:
                self.log_message(f"Socket connection error for {ip_address}: {e}", "WARNING")
            
        return False

    def connect_all(self):
        """Connect to both VSE server and X32 mixer"""
        self.log_message("Initiating connections...", "INFO")
        
        # Update configuration from GUI
        self.config['server_url'] = self.server_url_var.get()
        self.config['x32_ip'] = self.x32_ip_var.get()
        self.config['session_key'] = self.session_key_var.get()
        
        # Validate configuration
        if not self.config['server_url']:
            messagebox.showerror("Configuration Error", "Server URL is required")
            return
            
        if not self.config['x32_ip']:
            messagebox.showerror("Configuration Error", "X32 IP address is required")
            return
        
        # Start connections in background
        connect_thread = threading.Thread(target=self._connect_background, daemon=True)
        connect_thread.start()
        
        # Update UI
        self.connect_btn.config(state=tk.DISABLED)
        self.disconnect_btn.config(state=tk.NORMAL)

    def _connect_background(self):
        """Background connection process"""
        try:
            # Connect to X32 first
            self.log_message(f"Connecting to X32 at {self.config['x32_ip']}...", "INFO")
            self.x32_mixer = xair_api.connect("X32", ip=self.config['x32_ip'], port=self.config['x32_port'])
            self.x32_mixer.__enter__()
            
            # Test connection
            info = self.x32_mixer.query("/info")
            if info:
                self.x32_connected = True
                self.message_queue.put(('status_update', {'x32': True, 'x32_ip': self.config['x32_ip']}))
                self.log_message("X32 connection established", "SUCCESS")
            else:
                raise Exception("X32 not responding to queries")
            
            # Connect to VSE server
            self.log_message(f"Connecting to VSE server at {self.config['server_url']}...", "INFO")
            self._connect_websocket()
            
        except Exception as e:
            self.log_message(f"Connection failed: {e}", "ERROR")
            self.root.after(0, self._connection_failed)

    def _connect_websocket(self):
        """Connect to VSE server via SocketIO"""
        try:
            # Clean up server URL
            server_url = self.config['server_url']
            
            # Remove any trailing paths (like session-test.html)
            if '/session-test.html' in server_url:
                server_url = server_url.replace('/session-test.html', '')
            
            # Ensure no trailing slash for SocketIO client
            server_url = server_url.rstrip('/')
            
            self.log_message(f"Connecting to SocketIO server: {server_url}", "INFO")
            
            # Create SocketIO client
            self.socketio_client = socketio.Client(
                logger=False,  # Disable socketio logging to avoid spam
                engineio_logger=False
            )
            
            # Register event handlers
            self.socketio_client.on('connect', self._on_socketio_connect)
            self.socketio_client.on('disconnect', self._on_socketio_disconnect)
            self.socketio_client.on('session_joined', self._on_session_joined)
            self.socketio_client.on('connection_request', self._handle_connection_request)
            self.socketio_client.on('osc_command', self._handle_osc_command)
            self.socketio_client.on('error', self._on_socketio_error)
            
            # Connect to server
            self.socketio_client.connect(server_url)
            
        except Exception as e:
            self.log_message(f"SocketIO connection failed: {e}", "ERROR")

    def _on_socketio_connect(self):
        """SocketIO connection established"""
        self.server_connected = True
        self.message_queue.put(('status_update', {'server': True}))
        self.log_message("VSE server connection established", "SUCCESS")
        
        # Join session if session key provided
        if self.config['session_key']:
            self._join_session()

    def _on_socketio_disconnect(self):
        """SocketIO connection closed"""
        self.server_connected = False
        self.session_active = False
        self.message_queue.put(('status_update', {'server': False, 'session': False}))
        self.log_message("VSE server connection closed", "WARNING")

    def _on_session_joined(self, data):
        """Handle successful session join"""
        session_key = data.get('session_key')
        role = data.get('role')
        session_status = data.get('session_status')
        client_count = data.get('client_count', 0)
        host_connected = data.get('host_connected', False)
        relay_connected = data.get('relay_connected', False)
        
        self.session_active = True
        self.message_queue.put(('status_update', {'session': True, 'session_key': session_key}))
        
        self.log_message(f"Successfully joined session {session_key} as {role.upper()}", "SUCCESS")
        self.log_message(f"Session status: {session_status}", "INFO")
        self.log_message(f"Host connected: {host_connected}, Relay connected: {relay_connected}, Clients: {client_count}", "INFO")

    def _on_socketio_error(self, error):
        """Handle SocketIO errors"""
        self.log_message(f"SocketIO error: {error}", "ERROR")

    def _join_session(self):
        """Join the specified session on the server"""
        if not self.config['session_key']:
            return
            
        # Generate a user_id if not set
        user_id = self.config.get('host_user_id') or 'relay_' + str(int(time.time()))
        
        join_data = {
            'session_key': self.config['session_key'],
            'user_id': user_id,
            'role': 'relay'  # Desktop app is now a "relay", not a "host"
        }
        
        try:
            self.log_message(f"Sending join request: {join_data}", "INFO")
            self.socketio_client.emit('join_session_room', join_data)
            self.log_message(f"Join request sent for session {self.config['session_key']}", "INFO")
        except Exception as e:
            self.log_message(f"Failed to join session: {e}", "ERROR")

    def _handle_connection_request(self, data):
        """Handle connection request from client"""
        user_id = data.get('user_id', 'Unknown User')
        client_info = data.get('client_info', {})
        request_id = client_info.get('request_id')
        ip_address = client_info.get('ip_address', 'Unknown IP')
        user_agent = client_info.get('user_agent', 'Unknown Browser')
        
        self.log_message(f"Connection request from {user_id} ({ip_address})", "INFO")
        
        # Queue the approval dialog for the main thread
        self.message_queue.put(('connection_request', {
            'user_id': user_id,
            'ip_address': ip_address,
            'user_agent': user_agent,
            'request_id': request_id,
            'session_key': data.get('session_key')
        }))

    def _show_approval_dialog(self, request_data):
        """Show approval dialog in main thread"""
        user_id = request_data['user_id']
        ip_address = request_data['ip_address']
        user_agent = request_data['user_agent']
        request_id = request_data['request_id']
        session_key = request_data['session_key']
        
        # Create approval dialog
        dialog_title = "Connection Request"
        dialog_message = f"""A client wants to connect to your mixer:

User: {user_id}
IP Address: {ip_address}
Browser: {user_agent}

Do you want to allow this connection?"""
        
        # Show dialog and get response
        result = messagebox.askyesno(dialog_title, dialog_message)
        
        # Send approval/denial to server
        approval_message = {
            'type': 'approve_connection',
            'session_key': session_key,
            'request_id': request_id,
            'approved': result
        }
        
        try:
            if self.socketio_client and self.socketio_client.connected:
                self.socketio_client.emit('approve_connection', approval_message)
                
                if result:
                    self.log_message(f"Approved connection for {user_id}", "SUCCESS")
                else:
                    self.log_message(f"Denied connection for {user_id}", "INFO")
            else:
                self.log_message("Cannot send approval - not connected to server", "ERROR")
                
        except Exception as e:
            self.log_message(f"Failed to send approval response: {e}", "ERROR")

    def _handle_osc_command(self, data: Dict[str, Any]):
        """Handle OSC command from web client"""
        self.log_message(f"OSC COMMAND HANDLER TRIGGERED", "SUCCESS")
        
        if not self.x32_connected or not self.x32_mixer:
            self.log_message("OSC command received but X32 not connected", "WARNING")
            return
            
        if not self.socketio_client or not self.socketio_client.connected:
            self.log_message("OSC command received but server not connected", "WARNING")
            return
            
        try:
            # Extract the actual command from the SocketIO event data
            command = data.get('command', {})
            cmd_type = command.get('type')
            
            self.log_message(f"=== OSC COMMAND DEBUG ===", "INFO")
            self.log_message(f"Raw data received: {data}", "INFO")
            self.log_message(f"Extracted command: {command}", "INFO")
            self.log_message(f"Command type: '{cmd_type}' (type: {type(cmd_type)})", "INFO")
            self.log_message(f"cmd_type == 'sync_request': {cmd_type == 'sync_request'}", "INFO")
            
            if not cmd_type:
                self.log_message("ERROR: No command type found in OSC command!", "ERROR")
                return
            
            if cmd_type == 'mute':
                channel = command.get('channel', 1)
                action = command.get('action', 'toggle')  # Default to toggle
                
                self.log_message(f"MUTE COMMAND: Channel {channel}, Action: {action}", "INFO")
                
                # Get current state from X32 or from our tracking
                channel_key = f"ch_{channel}_mute"
                
                if action == 'toggle':
                    # Query current state from X32
                    try:
                        osc_path = f"/ch/{str(channel).zfill(2)}/mix/on"
                        current_state_raw = self.x32_mixer.query(osc_path)
                        
                        self.log_message(f"DEBUG OSC: Raw state from X32: {current_state_raw} (type: {type(current_state_raw)})", "INFO")
                        
                        # Extract value from tuple if needed
                        if isinstance(current_state_raw, tuple) and len(current_state_raw) > 0:
                            current_state = current_state_raw[0]
                        else:
                            current_state = current_state_raw
                        
                        self.log_message(f"DEBUG OSC: Extracted state value: {current_state}", "INFO")
                        
                        # X32 returns 1 for unmuted, 0 for muted
                        # Toggle: if currently unmuted (1), mute it (0), and vice versa
                        new_state = 0 if current_state else 1
                        is_muting = (new_state == 0)
                        
                        self.log_message(f"DEBUG OSC: Channel {channel} current: {current_state}, calculated new: {new_state}, is_muting: {is_muting}", "INFO")
                        
                    except Exception as e:
                        # Fallback to tracked state if query fails
                        self.log_message(f"Could not query X32 state, using tracked state: {e}", "WARNING")
                        current_tracked = self.channel_states.get(channel_key, True)  # Default to unmuted
                        new_state = 0 if current_tracked else 1
                        is_muting = (new_state == 0)
                
                else:
                    # Explicit mute/unmute action
                    new_state = 0 if action == 'mute' else 1
                    is_muting = (new_state == 0)
                
                # Send OSC command
                osc_path = f"/ch/{str(channel).zfill(2)}/mix/on"
                self.log_message(f"SENDING OSC: {osc_path} = {new_state}", "INFO")
                
                try:
                    self.x32_mixer.send(osc_path, new_state)
                    self.log_message(f"OSC SEND SUCCESSFUL", "SUCCESS")
                except Exception as osc_error:
                    self.log_message(f"OSC SEND FAILED: {osc_error}", "ERROR")
                    return
                
                # Update tracked state
                self.channel_states[channel_key] = (new_state == 1)  # Store as unmuted=True, muted=False
                
                # Log result
                action_text = "muted" if is_muting else "unmuted"
                self.log_message(f"Channel {channel} {action_text}", "SUCCESS")
                
                # Send confirmation back to client for latency measurement
                self._send_osc_confirmation(data, f"Channel {channel} {action_text}")
                
            elif cmd_type == 'fader':
                channel = command.get('channel', 1)
                value = command.get('value', 0.0)
                
                osc_path = f"/ch/{str(channel).zfill(2)}/mix/fader"
                self.x32_mixer.send(osc_path, float(value))
                self.log_message(f"Channel {channel} fader set to {value}", "SUCCESS")
                
                # Send confirmation back to client for latency measurement
                self._send_osc_confirmation(data, f"Channel {channel} fader set to {value}")
                
            elif cmd_type == 'solo':
                channel = command.get('channel', 1)
                action = command.get('action', 'toggle')  # Default to toggle
                
                self.log_message(f"Solo command received: Channel {channel}, Action: {action}", "INFO")
                
                # X32 Solo OSC path: /-stat/solosw/XX (Global solo switch)
                osc_path = f"/-stat/solosw/{str(channel).zfill(2)}"
                
                if action == 'toggle':
                    # Query current state and toggle
                    try:
                        current_state_raw = self.x32_mixer.query(osc_path)
                        self.log_message(f"DEBUG: Current solo state from X32: {current_state_raw} (type: {type(current_state_raw)})", "INFO")
                        
                        if isinstance(current_state_raw, tuple) and len(current_state_raw) > 0:
                            current_state = current_state_raw[0]
                        else:
                            current_state = current_state_raw
                        
                        # X32 returns 1 for active solo, 0 for inactive - toggle it
                        new_state = 0 if current_state else 1
                        is_soloing = (new_state == 1)
                        
                        self.log_message(f"DEBUG: current_state: {current_state}, new_state: {new_state}, is_soloing: {is_soloing}", "INFO")
                    except Exception as e:
                        self.log_message(f"Could not query X32 solo state: {e}", "WARNING")
                        new_state = 1  # Default to solo
                        is_soloing = True
                else:
                    # Explicit solo/unsolo action
                    new_state = 1 if action == 'solo' else 0
                    is_soloing = (new_state == 1)
                
                # Send OSC command
                self.log_message(f"SENDING SOLO OSC: {osc_path} = {new_state}", "INFO")
                
                try:
                    self.x32_mixer.send(osc_path, new_state)
                    self.log_message(f"SOLO OSC SEND SUCCESSFUL", "SUCCESS")
                    
                    # Verify the command was received by querying back
                    try:
                        verify_state = self.x32_mixer.query(osc_path)
                        self.log_message(f"SOLO VERIFICATION: {osc_path} now = {verify_state}", "INFO")
                    except Exception as verify_error:
                        self.log_message(f"SOLO VERIFICATION FAILED: {verify_error}", "WARNING")
                        
                except Exception as osc_error:
                    self.log_message(f"SOLO OSC SEND FAILED: {osc_error}", "ERROR")
                    return
                
                # Log result
                action_text = "soloed" if is_soloing else "unsoloed"
                self.log_message(f"Channel {channel} {action_text}", "SUCCESS")
                
                # Send confirmation back to client for latency measurement
                self._send_osc_confirmation(data, f"Channel {channel} {action_text}")
                
            elif cmd_type == 'sync_request':
                self.log_message("✅ SYNC_REQUEST HANDLER ACTIVATED", "SUCCESS")
                channels = command.get('channels', list(range(1, 33)))  # Default to all 32 main channels
                self.log_message(f"Board state sync requested for {len(channels)} channels: {channels[:8]}{'...' if len(channels) > 8 else ''}", "INFO")
                
                try:
                    # Query current state from X32 for requested channels
                    self.log_message("Querying X32 board state...", "INFO")
                    board_state = self._get_board_state(channels)
                    self.log_message(f"Board state retrieved: {len(board_state.get('faders', {}))} faders, {len(board_state.get('mutes', {}))} mutes", "INFO")
                    
                    # Send board state back to client
                    self.log_message("Sending board state update...", "INFO")
                    self._send_board_state_update(data, board_state)
                    self.log_message("✅ SYNC_REQUEST COMPLETED SUCCESSFULLY", "SUCCESS")
                except Exception as sync_error:
                    self.log_message(f"❌ SYNC_REQUEST FAILED: {sync_error}", "ERROR")
                    raise sync_error
                
            else:
                self.log_message(f"Unknown OSC command type: {cmd_type}", "WARNING")
                
        except Exception as e:
            self.log_message(f"OSC command execution failed: {e}", "ERROR")

    def _send_osc_confirmation(self, original_data: Dict[str, Any], result_message: str):
        """Send OSC execution confirmation back to client for latency measurement"""
        try:
            if self.socketio_client and self.socketio_client.connected:
                confirmation = {
                    'type': 'osc_executed',
                    'session_key': original_data.get('session_key'),
                    'user_id': original_data.get('user_id'),
                    'client_timestamp': original_data.get('client_timestamp'),  # Original timestamp from client
                    'relay_timestamp': time.time() * 1000,  # Current time in milliseconds
                    'result': result_message,
                    'command': original_data.get('command', {})
                }
                
                # Send confirmation to the session room
                self.socketio_client.emit('osc_executed', confirmation)
                self.log_message(f"Sent OSC confirmation: {result_message}", "DEBUG")
            else:
                self.log_message("Cannot send OSC confirmation - not connected to server", "WARNING")
        except Exception as e:
            self.log_message(f"Failed to send OSC confirmation: {e}", "ERROR")

    def _get_board_state(self, channels: list) -> dict:
        """Query current board state from X32 for specified channels"""
        board_state = {
            'faders': {},
            'mutes': {}
        }
        
        try:
            for channel in channels:
                channel_str = str(channel).zfill(2)
                
                # Get fader value
                fader_path = f"/ch/{channel_str}/mix/fader"
                fader_raw = self.x32_mixer.query(fader_path)
                if isinstance(fader_raw, tuple) and len(fader_raw) > 0:
                    board_state['faders'][channel] = float(fader_raw[0])
                else:
                    board_state['faders'][channel] = float(fader_raw) if fader_raw else 0.0
                
                # Get mute state
                mute_path = f"/ch/{channel_str}/mix/on"
                mute_raw = self.x32_mixer.query(mute_path)
                if isinstance(mute_raw, tuple) and len(mute_raw) > 0:
                    mute_state = mute_raw[0]
                else:
                    mute_state = mute_raw
                
                # X32 returns 1 for unmuted, 0 for muted - we want True for muted
                board_state['mutes'][channel] = (mute_state == 0)
                
                if channel <= 8:  # Only log first 8 channels to avoid spam
                    self.log_message(f"CH{channel}: fader={board_state['faders'][channel]:.3f}, muted={board_state['mutes'][channel]}", "DEBUG")
                
        except Exception as e:
            self.log_message(f"Error querying board state: {e}", "ERROR")
        
        return board_state

    def _send_board_state_update(self, original_data: Dict[str, Any], board_state: dict):
        """Send board state update to client"""
        try:
            if self.socketio_client and self.socketio_client.connected:
                update = {
                    'type': 'board_state_update',
                    'session_key': original_data.get('session_key'),
                    'board_state': board_state,
                    'timestamp': time.time()
                }
                
                # Send to the session room
                self.socketio_client.emit('board_state_update', update)
                self.log_message(f"Sent board state update: {len(board_state['faders'])} faders, {len(board_state['mutes'])} mutes (channels 1-{max(board_state['faders'].keys()) if board_state['faders'] else 0})", "SUCCESS")
            else:
                self.log_message("Cannot send board state update - not connected to server", "WARNING")
        except Exception as e:
            self.log_message(f"Failed to send board state update: {e}", "ERROR")

    def _connection_failed(self):
        """Handle connection failure (called from main thread)"""
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)

    def disconnect_all(self):
        """Disconnect from all services"""
        self.log_message("Disconnecting...", "INFO")
        
        # Disconnect SocketIO
        if self.socketio_client and self.socketio_client.connected:
            self.socketio_client.disconnect()
            self.socketio_client = None
        
        # Disconnect X32
        if self.x32_mixer:
            try:
                self.x32_mixer.__exit__(None, None, None)
            except:
                pass
            self.x32_mixer = None
        
        # Update status
        self.server_connected = False
        self.x32_connected = False
        self.session_active = False
        
        self.message_queue.put(('status_update', {'server': False, 'x32': False, 'session': False}))
        
        # Update UI
        self.connect_btn.config(state=tk.NORMAL)
        self.disconnect_btn.config(state=tk.DISABLED)
        
        self.log_message("Disconnected", "INFO")

    def test_mute_channel_1(self):
        """Test toggle muting channel 1 using direct xair-api"""
        if not self.x32_connected or not self.x32_mixer:
            self.log_message("Cannot test - X32 not connected", "WARNING")
            return
            
        try:
            # Query current state and toggle
            current_state_raw = self.x32_mixer.query("/ch/01/mix/on")
            self.log_message(f"DEBUG: Raw state from X32: {current_state_raw} (type: {type(current_state_raw)})", "INFO")
            
            # Extract value from tuple if needed
            if isinstance(current_state_raw, tuple) and len(current_state_raw) > 0:
                current_state = current_state_raw[0]
            else:
                current_state = current_state_raw
            
            self.log_message(f"DEBUG: Extracted state value: {current_state}", "INFO")
            
            new_state = 0 if current_state else 1  # 0 = muted, 1 = unmuted
            self.log_message(f"DEBUG: Calculated new state: {new_state}", "INFO")
            
            self.x32_mixer.send("/ch/01/mix/on", new_state)
            action = "unmuted" if new_state else "muted"
            self.log_message(f"HOST TEST: Channel 1 toggled to {action} (direct xair-api)", "SUCCESS")
            
            # Update our state tracking
            self.channel_states["ch_1_mute"] = (new_state == 1)
            
        except Exception as e:
            self.log_message(f"Host test toggle mute failed: {e}", "ERROR")

    def test_fader_channel_6(self):
        """Test fader on channel 6 using your working code"""
        if not self.x32_connected or not self.x32_mixer:
            self.log_message("Cannot test - X32 not connected", "WARNING")
            return
            
        try:
            self.log_message("HOST TEST: Starting fader animation on channel 6...", "INFO")
            
            # Your working code - animate fader for 10 iterations
            for i in range(10):
                val = random.uniform(0, 1)
                self.x32_mixer.send("/ch/06/mix/fader", val)
                time.sleep(0.1)  # Small delay between commands
                
            self.log_message("HOST TEST: Fader animation complete (direct xair-api)", "SUCCESS")
            
        except Exception as e:
            self.log_message(f"Host test fader failed: {e}", "ERROR")

    def clear_log(self):
        """Clear the activity log"""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def process_messages(self):
        """Background message processing"""
        while self.running:
            try:
                time.sleep(0.1)
                # Additional background processing can go here
            except Exception as e:
                print(f"Message processing error: {e}")

    def on_closing(self):
        """Handle application closing"""
        self.log_message("Shutting down...", "INFO")
        self.running = False
        
        # Disconnect everything
        self.disconnect_all()
        
        # Wait a moment for cleanup
        time.sleep(0.5)
        
        # Destroy GUI
        self.root.destroy()

    def run(self):
        """Start the application"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.on_closing()


def main():
    """Main entry point"""
    app = VSEHostRelay()
    app.run()


if __name__ == "__main__":
    main()
