<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="favicon.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>VSE Chat</title>
    <style>
      /* Global and basic styles */
      body {
        font-family: sans-serif;
        margin: 0;
        background: #f0f2f5;
        display: flex;
        flex-direction: column;
        min-height: 100vh;
      }
      header, main, footer {
        padding: 20px;
      }
      header {
        background: #20232a;
        color: #fff;
        text-align: center;
        padding: 10px;
      }
      main {
        flex: 1;
      }
      h2 {
        margin-top: 0;
      }
      .videos {
        display: flex;
        gap: 20px;
        flex-wrap: wrap;
      }
      video {
        width: 300px;
        height: 225px;
        background: #000;
        border-radius: 4px;
      }
      section {
        margin-bottom: 20px;
      }
      /* Modern control bar styles */
      .control-bar {
        background: #20232a;
        padding: 10px;
        display: flex;
        justify-content: center;
        gap: 10px;
        border-top: 1px solid #444;
        flex-wrap: wrap;
        position: relative;
      }
      .control-bar button {
        background: #282c34;
        border: none;
        color: #fff;
        padding: 10px 15px;
        font-size: 14px;
        border-radius: 4px;
        cursor: pointer;
        transition: background 0.2s;
      }
      .control-bar button:hover {
        background: #3a3f4b;
      }
      /* Dropdown menus for audio device selection */
      #micDropdownMenu, #speakerDropdownMenu {
        display: none;
        position: absolute;
        background: #fff;
        border: 1px solid #ddd;
        z-index: 1000;
        max-height: 150px;
        overflow-y: auto;
      }
      #micDropdownMenu div, #speakerDropdownMenu div {
        padding: 5px;
        cursor: pointer;
      }
      #micDropdownMenu div:hover, #speakerDropdownMenu div:hover {
        background: #f0f0f0;
      }
      /* More options dropdown styling */
      #moreOptionsDropdown {
        display: none;
        position: absolute;
        bottom: 50px;
        right: 10px;
        background: #fff;
        border: 1px solid #ddd;
        z-index: 1000;
        padding: 5px;
      }
      #moreOptionsDropdown button {
        background: transparent;
        border: none;
        color: black;
        padding: 5px 10px;
        cursor: pointer;
        text-align: left;
      }
      #moreOptionsDropdown button:hover {
        background: #f0f0f0;
      }
      /* Chat sidebar styles */
      .chat-sidebar {
        position: fixed;
        top: 0;
        right: 0;
        bottom: 0; /* Use top & bottom positioning for full height */
        width: 300px;
        background: #fff;
        border-left: 1px solid #ddd;
        box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        padding: 10px;
        display: none;
        flex-direction: column;
        z-index: 1100; /* Ensure it appears above the footer */
      }
      .chat-sidebar header {
        font-weight: bold;
        margin-bottom: 10px;
        text-align: center;
      }
      .messages {
        flex: 1;
        overflow-y: auto;
        margin-bottom: 10px;
        padding: 5px;
      }
      .chat-input-container {
        display: flex;
        align-items: center;
        gap: 5px;
        position: relative;
        flex-shrink: 0; /* Prevents the input area from shrinking */
      }
      .chat-input-container input[type="text"] {
        flex: 1;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 4px;
      }
      .chat-input-container button {
        border: none;
        background: transparent;
        cursor: pointer;
        font-size: 20px;
      }
      /* Emoji picker styles */
      .emoji-picker {
        display: none;
        position: absolute;
        bottom: 45px;
        right: 0;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 4px;
        padding: 5px;
        box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
        flex-wrap: wrap;
        width: 250px;
      }
      .emoji-picker span {
        cursor: pointer;
        padding: 5px;
        font-size: 20px;
      }
      .emoji-picker span:hover {
        background: #f0f0f0;
      }
      /* Chat bubble styles */
      .chat-message {
        margin-bottom: 10px;
        max-width: 80%;
        display: flex;
        flex-direction: column;
      }
      .chat-message.sent {
        align-self: flex-end;
        background: #dcf8c6;
        border-radius: 10px 10px 0 10px;
        padding: 8px 12px;
      }
      .chat-message.received {
        align-self: flex-start;
        background: #fff;
        border: 1px solid #ddd;
        border-radius: 10px 10px 10px 0;
        padding: 8px 12px;
      }
      .chat-timestamp {
        font-size: 10px;
        color: #999;
        text-align: right;
        margin-top: 2px;
      }
    </style>
  </head>
  <body>
    <header role="banner">
      <h1>VSE Chat</h1>
    </header>
    <main role="main">
      <section>
        <h2>1. Start your Webcam</h2>
        <div class="videos">
          <div>
            <h3>Local Stream</h3>
            <video id="webcamVideo" autoplay playsinline tabindex="0"></video>
          </div>
          <div>
            <h3>Remote Stream</h3>
            <video id="remoteVideo" autoplay playsinline tabindex="0"></video>
          </div>
        </div>
        <button id="webcamButton" aria-label="Start Webcam">Start webcam</button>
      </section>
      <section>
        <h2>2. Create a new Call (Device A)</h2>
        <button id="createSessionButton" aria-label="Create Session">Create Session</button>
        <p>Session Key: <span id="sessionKeyDisplay">N/A</span></p>
        <button id="callButton" aria-label="Create Call" disabled>Create Call (Offer)</button>
      </section>
      <section>
        <h2>3. Join a Call (Device B)</h2>
        <input id="joinSessionInput" placeholder="Enter session key" aria-label="Session Key" />
        <button id="joinSessionButton" aria-label="Join Session">Join Session</button>
        <button id="answerButton" aria-label="Answer Call" disabled>Answer Call</button>
      </section>
      <section>
        <h2>4. Hangup</h2>
        <button id="hangupButton" aria-label="Hangup Call" disabled>Hangup</button>
      </section>
    </main>
    <!-- Chat sidebar -->
    <aside id="chatSidebar" class="chat-sidebar" role="complementary" aria-label="Chat Sidebar">
      <header>Chat</header>
      <div class="messages" id="chatMessages" tabindex="0">
        <!-- Chat messages will appear here -->
      </div>
      <div class="chat-input-container">
        <input
          type="text"
          id="chatInput"
          placeholder="Type your message..."
          disabled
          aria-label="Chat Input"
        />
        <button id="emojiButton" aria-label="Emoji Picker">😀</button>
        <div id="emojiPicker" class="emoji-picker"></div>
      </div>
    </aside>
    <!-- Control bar -->
    <footer class="control-bar" role="navigation" aria-label="Control Bar">
      <button id="micButton" aria-label="Toggle Microphone">Microphone</button>
      <button id="micDropdownButton" aria-label="Select Microphone">▼</button>
      <button id="cameraButton" aria-label="Toggle Camera">Camera</button>
      <button id="presentButton" aria-label="Share Screen">Present now</button>
      <button id="chatToggleButton" aria-label="Toggle Chat Sidebar">Chat</button>
      <button id="moreOptionsButton" aria-label="More Options">More options</button>
      <button id="speakerButton" aria-label="Select Speaker">Speaker</button>
      <button id="speakerDropdownButton" aria-label="Select Speaker">▼</button>
      <!-- Dropdown menus for device selection -->
      <div id="micDropdownMenu"></div>
      <div id="speakerDropdownMenu"></div>
      <!-- More options dropdown -->
      <div id="moreOptionsDropdown">
        <button id="testSpeakerButton" aria-label="Test Speakers">Test Speakers</button>
      </div>
    </footer>
    <!-- External JavaScript file -->
    <script src="{{ url_for('static', filename='app_audio.js') }}" type="module"></script>
  </body>
</html>


