SHELL := bash
CREATE_ZIP ?= "TRUE"
OFFLINE_DOCS ?= "FALSE"
.SHELLFLAGS := -eu -o pipefail -c
.DELETE_ON_ERROR:
MAKEFLAGS += --warn-undefined-variables
MAKEFLAGS += --no-builtin-rules

.DEFAULT: help

help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

venv: ## Create a virtual environment.
	python3.12 -m venv venv
	venv/bin/pip install --upgrade pip
	venv/bin/pip install --upgrade setuptools
	venv/bin/pip install --upgrade wheel
	venv/bin/pip install pip-tools
.PHONY: venv

dependencies: ## Installs packages in requirements.txt into the virtual environment.
	pip install -r requirements.txt
.PHONY: dependencies

update-dependencies:  ## Updates all of the dependency files to the latest versions
	pip install pip-tools
	pip-compile requirements.in > requirements.txt
.PHONY: update-dependencies