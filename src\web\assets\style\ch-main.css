 html,
 * {
     box-sizing: border-box;
 }

 body,
 a,
 p,
 div {
     font-family: "Open Sans", sans-serif;
 }

 body {
     background: #626262;
     margin: 0;
     padding: 0;
 }

 .page-wrapper {
     padding: 25px 20px;
     max-height: 100vh;
     max-width: 100%;
 }

 .container {
     display: flex;
     gap: 25px;
     flex-wrap: nowrap;
 }

 .left-sidebar {
     width: 10%;
     text-align: center;
 }

 .main {
     width: 90%;
 }

 /* buttons */

 .btn-block {
     border-radius: 4px;
     border: 3px solid #f5f5f5;
     background: #2d2d2f;
     color: #fff;
     text-transform: uppercase;
     font-size: 14px;
     height: 38px;
     width: 42px;
     display: flex;
     align-items: center;
     justify-content: center;
     cursor: pointer;
 }

 .btn-block p,
 .left-sidebar p {
     color: #fff;
     font-weight: 600;
     text-transform: uppercase;
     font-size: 14px;
 }

 /* main part header */
 .main-item.navbar {
     display: flex;
     justify-content: space-around;
     align-items: center;
 }

 .main-item.navbar a {
     display: flex;
     flex-direction: column;
     gap: 10px;
     justify-content: center;
     align-items: center;
     text-decoration: none;
     text-transform: uppercase;
     color: #fff;
     font-size: 14px;
     font-weight: bold;
 }

 .navbar-item-img {
     background: #000;
     height: 40px;
     width: 40px;
     border-radius: 100px;
     display: flex;
     align-items: center;
     justify-content: center;
     box-shadow: 0 0 0 3px #00000014;
 }

 .navbar-item-img.active_item {
     background: #fff;
     box-shadow: none;
 }

 .navbar-item-img.active_item img {
     filter: invert(1);
 }

 .navbar-item-img.homed img {
     margin-bottom: 2px;
     margin-left: 2px;
 }

 .scenes img {
     margin-top: 7px;
     margin-right: 7px;
 }

 .meters img {
     margin-bottom: 7px;
     margin-left: 5px;
     width: 32px;
 }

 /* sidebar items */
 .sidebar-item.controls {
     width: 100%;
     max-width: 130px;
     display: flex;
     align-items: center;
     justify-content: space-between;
     margin: 15px auto;
     border: 3px solid #f5f5f5;
     border-radius: 4px;
     gap: 0 !important;
 }

 .sidebar-item.controls .btn-block {
     border: none;
     width: 32%;
     border-radius: 0;
 }

 .sidebar-item.controls .btn-block:nth-of-type(2) {
     border-left: 3px solid #f5f5f5;
     border-right: 3px solid #f5f5f5;
     border-radius: 0 !important;
     width: 36%;
     margin: 0;
 }

 /* siebar - 4 buttons */
 .sidebar-item.addons {
     width: 100%;
     max-width: 134px;
     display: flex;
     align-items: center;
     justify-content: space-between;
     flex-wrap: wrap;
     margin: 20px auto;
     gap: 20px;
 }

 .sidebar-item.addons .btn-block {
     width: 42%;
     max-width: 56px;
     max-height: 36px;
 }

 /* fader */
 .activated-faders {
     width: 100%;
     max-width: 130px;
     min-height: 151px;
     margin: auto;
     background: #f5f5f5;
     box-shadow: 0 0 9.2px 1px #f5f5f5;
     display: flex;
     align-items: center;
     justify-content: center;
     flex-direction: column;
     gap: 10px;
     display: none;
 }

 .activated-faders img {
     width: 100%;
     max-width: 60%;
 }

 .btn-block.sidebar-item.faders {
     width: 100%;
     max-width: 134px;
     height: 46px;
     display: flex;
     align-items: center;
     margin: 20px auto;
 }

 .btn-block.sidebar-item.faders p {
     font-size: 12px;
 }

 /* 1-2 when fader activated */
 .send-faders-active-options {
     width: 100%;
     max-width: 120px;
     display: flex;
     align-items: center;
     justify-content: space-between;
     display: none;
 }

 .send-faders-active {
     width: 46%;
     max-width: 55px;
     height: 46px;
     margin-bottom: 20px;
 }

 /* X Y auto mixing */
 .sidebar-item.mixing {
     width: 100%;
     max-width: 134px;
     display: flex;
     align-items: center;
     justify-content: space-between;
     flex-wrap: wrap;
     margin: 45px auto;
 }

 .sidebar-item.mixing p {
     width: 100%;
 }

 .btn-block.mixing-item {
     width: 46%;
     max-width: 55px;
     height: 46px;
 }

 /* mute groups */
 .sidebar-item.mute-group {
     width: 100%;
     max-width: 134px;
     display: flex;
     align-items: center;
     justify-content: space-between;
     flex-wrap: wrap;
     margin: 20px auto;
     gap: 15px;
 }

 .sidebar-item.mute-group .btn-block {
     width: 46%;
     max-width: 55px;
     height: 55px;
 }

 .sidebar-item.mute-group p {
     width: 100%;
     margin-bottom: 0;
 }

 .sidebar-item.mute-group .last-mute {
     width: 100%;
     max-width: 100%;
 }

 /* solos  */
 .sidebar-item.show-solos {
     width: 100%;
     max-width: 134px;
     display: flex;
     align-items: center;
     justify-content: center;
     flex-direction: column;
     margin: 20px auto;
 }

 .sidebar-item.show-solos p {
     width: 100%;
 }

 .show-solos-item-wrap {
     max-width: 134px;
     width: 100%;
     display: flex;
     border: 3px solid #f5f5f5;
     border-radius: 4px;
 }

 .show-solos-item {
     border: none;
     width: 50%;
     border-radius: 0;
     background: #2d2d2f;
     color: #fff;
     text-transform: uppercase;
     font-size: 14px;
     height: 38px;
     cursor: pointer;
     display: flex;
     justify-content: center;
     align-items: center;
 }

 .show-solos-item .btn-block:last-of-type {
     border-left: 3px solid #f5f5f5;
     border-radius: 0 !important;
     margin: 0;
 }

 /* lock mutes big */
 .btn-block.sidebar-item.lock-mute {
     width: 100%;
     max-width: 134px;
     height: 46px;
     display: flex;
     align-items: center;
     flex-wrap: wrap;
     margin: 20px auto;
 }

 .btn-block.sidebar-item.lock-mute p {
     margin: 0;
 }

 /* MAIN */
 /* board equalizer */
 .screen-channels-wrapper {
     margin-top: 30px;
     display: flex;
     justify-content: space-between;
     max-width: 97%;
 }

 .screen-channels-item {
     max-width: 96px;
     text-align: center;
     display: flex;
     flex-direction: column;
     justify-content: center;
     gap: 0;
     border-radius: 4px;
     overflow: hidden;
     box-shadow: 0 0 0 3px #00000014;
     cursor: pointer;
 }

 .equalizer-screens {
     display: flex;
     justify-content: center;
     align-items: flex-end;
 }

 .bottom-screen-info {
     background: #000;
     color: #fff;
     height: 32px;
     font-size: 12px;
     display: flex;
     justify-content: center;
     align-items: center;
 }

 .equalizer-board {
     position: relative;
     height: 138px;
     background: linear-gradient(rgba(206, 24, 30, 1) 0%,
             rgba(236, 140, 14, 1) 18%,
             rgba(248, 215, 6, 1) 38%,
             rgba(136, 195, 59, 1) 100%);
     overflow: hidden;
     z-index: -2;
 }

 .tab-channel-active {
     border: 3px solid #fff;
     box-shadow: none;
     max-width: 102px;
 }

 .equalizer-board img {
     width: 100%;
 }

 /* mixer */
 .main-item.draggers {
     margin-top: 25px;
     margin-bottom: 20px;
     display: flex;
 }

 .mixer-wrap {
     width: 12.5%;
     min-width: 120px;
 }

 .mixer-board {
     max-width: 88px;
 }

 .mixer-container {
     max-width: 120px;
     display: flex;
     gap: 5px;
     align-items: flex-end;
 }

 .mixer-container .inner-container {
     max-width: 88px;
     position: relative;
 }

 .mixer-container .inner-container .tooltip {
     position: absolute;
     z-index: 3;
     top: -12px !important;
     left: 16%;
     background: url(../assets/icons/Union.svg);
     background-repeat: no-repeat;
     color: #fff;
     font-size: 12px;
     background-size: cover;
     width: 85px;
     height: 33px;
     display: flex;
     justify-content: center;
     align-items: center;
     padding-bottom: 5px;
     font-weight: bold;
 }

 .number-mixer {
     color: #fff;
     font-size: 20px;
     line-height: 1;
 }

 .volume-board {
     max-width: 24px;
     width: 100%;
     height: 319px;
     border-radius: 10px;
     margin-bottom: 20px;
     background: linear-gradient(rgba(206, 24, 30, 1) 0%,
             rgba(236, 140, 14, 1) 18%,
             rgba(248, 215, 6, 1) 38%,
             rgba(136, 195, 59, 1) 100%);
     position: relative;
     overflow: hidden;
     z-index: -2;
 }

 .volume-board img {
     max-width: 24px;
     width: 100%;
 }

 .mixer-dragger {
     position: absolute;
     z-index: 2;
     bottom: -5px;
     left: 26px;
     max-width: 59px;
 }

 /* mixer channels */
 .channel-screen {
     width: 120px;
     height: 71px;
     background: #1f1f21;
     color: #fff;
     display: flex;
     justify-content: center;
     align-items: flex-end;
     padding-bottom: 10px;
     margin-bottom: 20px;
     border-radius: 4px;
     font-weight: 600;
     text-decoration: none;
 }

 /* mixer mute buttons */
 .main-board-buttons.mute-solo {
     max-width: 120px;
     display: flex;
     margin-top: 30px;
     justify-content: center;
     gap: 20px;
 }

 .main-board-buttons.mute-solo .mute-solo-item {
     width: 56px;
     height: 40px;
 }

 /* needle board and needle */

 .needle-screen {
     max-width: 120px;
     margin-bottom: 30px;
     position: relative;
     overflow: hidden;
     /* max-height: 55px;
        cursor: pointer;
        transition: max-height 0.4s ease-out; */
     max-height: 55px;
     /* Initial height */
     cursor: pointer;
     transition: 0.3s ease-in-out;
     border-radius: 4px;
 }

 .needle-screen:hover {
     max-height: 81px;
     overflow: visible;
     transition: height 0.3s ease;
 }

 .needle-path {
     width: 100%;
 }

 .needle-dragger {
     position: absolute;
     z-index: 1;
     top: 0;
     left: 46%;
 }

 /* ANIMATIONS */
 .btn-activated {
     background: #f5f5f5;
 }

 .overlay {
     position: absolute;
     border-radius: 0;
     top: 0;
     left: 0;
     width: 100%;
     height: 100%;
     background: #2a2a2a;
     /* Adjust opacity as needed */
     z-index: -1;
 }

 /* btn animation */
 .active-btn-shadow {
     box-shadow: 0 0 9.2px 1px #f5f5f5;
 }

 .active-btn-background {
     background: #f5f5f5;
     color: #222;
     font-weight: 600;
 }

 .btn-block.active-btn-background p {
     color: #222;
     font-weight: 600;
 }

 .btn-block.active-btn-background img {
     filter: invert(1);
 }

 /* send on faders */
 .send-faders-active-options.show-flex,
 .activated-faders.show-flex {
     display: flex;
 }

 .needle-screen.hide-if-faders,
 .sidebar-item.mixing.hide-if-faders {
     display: none;
 }

 /* TABS Switch channels */

 .main-item.draggers {
     display: none;
 }

 .main-item.draggers.tab-active {
     display: flex;
 }

 .screen-channels-item.tab-active {
     box-shadow: 0 0 0 3px #fff;
 }

 .btn-block.edit-dca-item {
     width: 100%;
     max-width: 120px;
     height: 44px;
     margin-bottom: 15px;
 }

 .edit-dca-wrap {
     height: 55px;
     margin-bottom: 30px;
 }

 /* tab content colors change */
 #channel-aux .channel-screen,
 #channel-fx .channel-screen {
     background: #c8ffa5;
     color: #171100;
 }

 #channel-bus18 .channel-screen,
 #channel-bus916 .channel-screen {
     background: #b9ffff;
     color: #171100;
 }

 #channel-mtx .channel-screen,
 #channel-dca .channel-screen {
     background: #ffc2ff;
     color: #171100;
 }

 #channel-mtx .mixer-wrap:nth-of-type(8) .channel-screen,
 #channel-mtx .mixer-wrap:nth-of-type(7) .channel-screen {
     background: #f2ede9;
     color: #171100;
 }

 #channel-mtx .mixer-wrap .needle-screen {
     visibility: hidden;
 }

 #channel-mtx .mixer-wrap:last-of-type .needle-screen {
     visibility: visible;
 }

 #channel-dca .mixer-wrap .needle-screen {
     display: none;
 }

 /*  TABS END  */
 /* MUTE disable */
 .muted.disabled,
 .no-faders-tab.disabled {
     pointer-events: none;
     opacity: 0.6;
 }

 /*  MUTE Disable end */

 /* DETAIL PAGES */

 /* sidebar */
 .mixer-wrap {
     margin: auto;
 }

 .sidebar-item.addons {
     max-width: 130px;
     gap: 15px;
 }

 .detail-pages-wrapper {
     width: 100%;
     max-width: 125px;
     display: flex;
     margin-top: 20px;
     margin-bottom: 20px;
     justify-content: space-between;
 }

 .detail-pages-wrapper {
     width: 100%;
     max-width: 110px;
     display: flex;
     margin-top: 20px;
     margin-bottom: 20px;
     justify-content: space-between;
 }

 .page-switch {
     width: 100%;
     max-width: 50px;
     height: 40px;
 }

 .page-switch img {
     width: 100%;
     max-width: 40%;
 }

 .number-mixer {
     text-align: left;
 }

 .channel-screen {
     background-color: #f2ede9;
     color: #2d2d2f;
 }

 /* main tabs CSS */
 .tab-content-hidden {
     display: none;
 }

 .tab-content-hidden.active-detail-tab {
     display: block;
 }

 .detail-tabs-navigation {
     width: 100%;
     display: flex;
     margin-top: 25px;
     gap: 0.5%;
 }

 .detail-tabs-navigation .details-tab-nav {
     width: 14.2%;
     background: #383838;
     color: #fff;
     text-align: center;
     height: 49px;
     border-radius: 4px 4px 0 0;
     display: flex;
     justify-content: center;
     align-items: center;
     cursor: pointer;
 }

 .details-tab-nav.active-detail-tab {
     background: #5a5a5a;
 }

 .tab-content-hidden {
     background: #5a5a5a;
     width: 100%;
     min-height: 671px;
     padding: 5px 30px 30px 20px;
 }

 .inner-wrapper-detail {
     display: flex;
     justify-content: space-between;
     width: 100%;
 }

 .left-config-tab {
     width: 80%;
 }

 .right-config-tab {
     width: 18%;
 }

 /* left config rows */
 /* first row */
 .first-row-config {
     display: flex;
     justify-content: space-between;
     width: 100%;
     align-items: flex-end;
     padding: 10px 20px;
     margin: auto;
 }

 .dropdown-detail {
     border-radius: 4px;
     border: 3px solid #2d2d2f;
     color: #f7f7f7;
     width: 127px;
     height: 51px;
     display: flex;
     justify-content: center;
     align-items: center;
     font-size: 14px;
     font-weight: bold;
     cursor: pointer;
 }

 .swap-detail {
     border-radius: 4px;
     border: 3px solid #2d2d2f;
     color: #f7f7f7;
     width: 121px;
     height: 31px;
     display: flex;
     justify-content: center;
     align-items: center;
     font-size: 14px;
     font-weight: bold;
     margin: 20px auto 55px;
     cursor: pointer;
 }

 .three-swap {
     display: flex;
     width: 100%;
     max-width: 176px;
     justify-content: space-between;
     gap: 10px;
 }

 .three-swap .detail-btn {
     width: 52px;
 }

 /* second row */
 .second-row-config {
     display: flex;
     justify-content: space-between;
     width: 100%;
     align-items: center;
     padding: 10px 15px;
     padding-left: 0;
 }

 .second-row-config .insert-container .detail-btn {
     border-radius: 4px;
     border: 3px solid #2d2d2f;
     color: #f7f7f7;
     width: 121px;
     height: 31px;
     display: flex;
     justify-content: center;
     align-items: center;
     font-size: 14px;
     font-weight: bold;
     margin-bottom: 25px;
     cursor: pointer;
     background: transparent;
 }

 .second-row-config .insert-container .detail-btn:nth-of-type(3) {
     background: #2d2d2f;
 }

 .wheel-container {
     width: 85%;
     display: flex;
     flex-wrap: wrap;
     align-items: center;
     gap: 15px;
     margin-top: 30px;
     margin-left: auto;
     margin-right: auto;
 }

 .wheel-item {
     width: 23%;
     margin-bottom: 30px;
     display: flex;
     flex-direction: column;
     justify-content: center;
     align-items: center;
 }

 .wheel-item.slope-item .wheel-numbers {
     margin-left: -10px;
 }

 .wheel-container .wheel-item:first-of-type {
     margin-bottom: 0;
     margin-top: 35px;
 }

 .wheel-board {
     position: relative;
     text-align: center;
     width: 140px;
     height: 122px;
 }

 .wheel-numbers {
     max-width: 140px;
 }

 .black-circle {
     position: absolute;
     top: 58%;
     left: 50%;
     transform: translate(-50%, -50%);
     background: #1e1e1e;
     width: 86px;
     height: 86px;
     border-radius: 100px;
     display: flex;
     align-items: center;
     justify-content: center;
 }

 .wheel-knob {
     width: 57px;
     height: 57px;
     transform: rotate(-140deg);
 }

 .insert-container {
     padding-right: 20px;
 }

 #animated,
 .animated-svg {
     position: absolute;
 }

 /* rotate bar */
 .rotation-div {
     z-index: 0;
     clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);
     transform-origin: center center;
     position: absolute;
     top: 7px;
     left: 7px;
     transform-origin: center center;
     width: 75px;
     height: 75px;
     background-color: transparent;
     border: 5px solid red;
     border-top: 5px solid red;
     border-bottom: 5px solid transparent;
     border-radius: 75px;
 }

 /* svg torate */
 .rotation-svg {
     position: absolute;
     top: 7px;
     left: 7px;
     transform-origin: center center;
     width: 75px;
     height: 75px;
 }

 .rotation-path {
     stroke-linecap: round;
     stroke-width: 5;
     /* Adjust stroke width as needed */
 }

 /* third row */
 .third-row-config {
     display: flex;
     justify-content: flex-start;
 }

 .dca-detail-board {
     width: 35%;
 }

 .mute-detail-board {
     width: 25%;
 }

 .dca-18-flex,
 .mute-16-flex {
     width: 100%;
     display: flex;
     flex-wrap: wrap;
     gap: 15px;
     justify-content: center;
 }

 .dca-18-flex {
     max-width: 310px;
     margin: auto;
 }

 .mute-16-flex {
     max-width: 250px;
     margin: auto;
 }

 .dca-18-flex .detail-btn {
     width: 25%;
     max-width: 52px;
 }

 .mute-16-flex .detail-btn {
     width: 33%;
     max-width: 52px;
 }

 .automix-detail-board {
     width: 20%;
 }

 .auto-flex {
     width: 100%;
     display: flex;
     flex-wrap: wrap;
     gap: 15px;
     justify-content: center;
     max-width: 185px;
     margin: auto;
 }

 .auto-flex .detail-btn {
     width: 50%;
     max-width: 52px;
 }

 .auto-flex .detail-btn:first-of-type {
     width: 100%;
     max-width: 121px;
 }

 /* right conifg */
 .split-flex {
     display: flex;
     justify-content: space-between;
     gap: 10px;
 }

 .right-config-tab .title-main {
     color: #fff;
     text-align: center;
     margin-top: 40px;
     margin-bottom: 25px;
     font-size: 24px;
     font-weight: 600;
 }

 .detail-txt {
     color: #fff;
     font-size: 14px;
     text-align: center;
     margin-top: 25px;
     margin-bottom: 15px;
     font-weight: 600;
     width: 100%;
 }

 .stereo-mono {
     display: flex;
     flex-direction: column;
     justify-content: space-between;
 }

 .split-flex .main-board-buttons {
     margin-top: auto;
     padding-bottom: 35px;
 }

 .mono-c .mixer-container {
     flex-direction: column;
 }

 .mono-c .mixer-container .inner-container {
     padding-top: 15px;
     padding-bottom: 5px;
 }

 .mono-c .detail-txt {
     margin-bottom: 0;
 }

 /* Buttons active/non active detail */
 .detail-btn {
     border-radius: 4px;
     border: 3px solid #f5f5f5;
     background: #2d2d2f;
     color: #fff;
     text-transform: uppercase;
     font-size: 14px;
     height: 51px;
     width: 120px;
     display: flex;
     align-items: center;
     justify-content: center;
     cursor: pointer;
 }

 .detail-btn.detail-btn-active {
     background: #ff9b04;
     color: #2d2d2f;
     font-weight: bold;
 }

 .detail-btn.detail-btn-active p {
     color: #222;
     font-weight: 600;
 }

 /* weight before after and main */
 .weight-detail-board .black-circle {
     top: 50%;
 }

 .weight-detail-board .wheel-board::before {
     content: "-12";
     font-size: 12px;
     color: #fff;
     position: absolute;
     bottom: 5px;
     left: 5px;
 }

 .weight-detail-board .wheel-board::after {
     content: "+12";
     font-size: 12px;
     color: #fff;
     position: absolute;
     bottom: 5px;
     right: 5px;
 }

 /* tabs */

 .processing-flex {
     max-width: 98%;
     margin: 10px auto;
     display: flex;
     align-items: center;
     gap: 15px;
 }

 .single-list.preset-first {
     width: 22%;
 }

 .single-list.preset-second {
     width: 60%;
     padding-top: 62px;
 }

 .single-list.preset-third {
     width: 18%;
     padding-top: 62px;
 }

 .single-list .heading-list {
     text-align: center;
     padding: 20px 1px;
 }

 .single-list.preset-first .heading-list {
     font-weight: 600;
     font-size: 20px;
     text-align: left;
     padding: 20px 0 35px;
 }

 .single-list.preset-first .single_preset_item div {
     font-weight: 600;
     font-size: 20px;
     text-align: left;
 }

 .heading-list,
 .scrolled {
     color: #fff;
     font-weight: 600;
     font-size: 16px;
 }

 .single-list.preset-first .single_preset_item {
     display: flex;
     align-items: center;
     margin-bottom: 15px;
     gap: 20px;
     color: #fff;
     font-weight: 16px;
     font-weight: bold;
 }

 .single-list.preset-first .btn-block {
     width: 51px;
     height: 51px;
 }

 .single-list.preset-second {
     width: 56%;
     padding-top: 62px;
 }

 .scroll-items {
     height: 512px;
     overflow-y: scroll;
     background-color: #2d2d2f;
 }

 .single-list.preset-third .single_preset_item {
     display: flex;
     justify-content: center;
     align-items: center;
     gap: 30px;
     flex-direction: column;
 }

 .single-item:nth-last-of-type(odd) {
     background: #414141;
 }

 .single-item:nth-last-of-type(even) {
     background: #383838;
 }

 .scrolled.scrolled-active {
     background: #f7f7f7;
     color: #383838;
 }

 .single-item {
     height: 32px;
     display: flex;
     align-items: center;
     padding-left: 15px;
     cursor: pointer;
 }

 .single-list.preset-third .btn-block {
     width: 118px;
     height: 51px;
 }

 .scroll-items::-webkit-scrollbar {
     display: none;
 }

 .single-list.preset-first {
     padding-top: 60px;
     padding-left: 30px;
 }

 .single-list.preset-first .active-btn-background {
     background-color: #ff9b04;
     border: 3px solid #ff9b04;
 }

 /* gate and dyn */
 .gate-right-col .wheel-container .wheel-item:first-of-type {
     margin-top: 0;
     margin-bottom: 30px;
 }

 /* naming tab  */
 .names-flex {
     display: flex;
     width: 100%;
     max-width: 850px;
     margin-left: 8%;
     margin-top: 60px;
     gap: 50px;
 }

 .names-color-col,
 .names-instrument-col {
     width: 186px;
 }

 .names-channel-col {
     width: 373px;
 }

 #naming-tab-content .scroll-items {
     background-color: #000 !important;
     height: 449px;
 }

 #naming-tab-content .scroll-items>div {
     height: 64px;
     width: 100%;
     display: flex;
     justify-content: center;
     align-items: center;
     border: 1px solid #383838;
     cursor: pointer;
 }

 .names-channel-col {
     color: #fff;
 }

 .channel-names-set {
     height: 51px;
     width: 373px;
     display: flex;
     justify-content: center;
     align-items: center;
     background: #000;
     color: #fff;
     margin-bottom: 50px;
 }

 .hidden_row_names {
     height: 51px;
     width: 373px;
     visibility: hidden;
     margin-bottom: 50px;
 }

 .btn-block.button_row_names {
     width: 124px;
     height: 51px;
     margin: 30px auto 20px;
 }

 .button_row_names_hidden {
     width: 124px;
     height: 51px;
     visibility: hidden;
     margin: 30px auto 20px;
 }

 .names-instrument-col .scrolled img {
     filter: invert(1);
 }

 .names-instrument-col .scrolled.scrolled-active img {
     filter: invert(0);
 }

 /* circle color select */
 .color_circle {
     height: 44px;
     width: 44px;
     border-radius: 100px;
 }

 /* sends tab board */
 .one-drag-wrapper .mixer-board {
     max-width: 50px;
 }

 .one-drag-wrapper .mixer-dragger {
     left: -5px;
 }

 .sends-flex {
     display: flex;
     padding: 10px 0 60px;
 }

 .single-column_sends {
     width: 12.4%;
     display: flex;
     flex-direction: column;
     gap: 25px;
     align-items: center;
 }

 .flex-two-btns {
     display: flex;
     gap: 20px;
     justify-content: space-around;
     width: 80%;
 }

 .flex-two-btns>div {
     flex-direction: column;
     font-size: 16px;
     width: 50px;
     height: 40px;
     font-weight: 500;
     padding: 2px;
     line-height: 1.1;
 }

 .flex-two-btns>div span {
     font-size: 10px;
     font-weight: 500;
     text-transform: capitalize !important;
 }

 .double-drag-wrapper {
     display: flex;
     gap: 20px;
 }

 .double-m-wrap {
     display: flex;
     gap: 20px;
 }

 .btn-block.m-button {
     width: 50px;
     height: 40px;
 }

 .button-drop_wrapper .dropdown-detail {
     width: 113px;
     height: 29px;
 }

 /* gate */
 #gate-tab-content .inner-wrapper-detail,
 #dyn-tab-content .inner-wrapper-detail {
     justify-content: flex-start;
 }

 .gate-left-col {
     width: 40%;
     max-width: 500px;
     padding-top: 80px;
     padding-left: 20px;
 }

 .gate-right-col {
     width: 60%;
     padding-top: 0;
 }

 .screen-set.column_one_left {
     display: flex;
     padding-left: 30px;

     gap: 30px;
 }

 .screen-set.column_one_left .volume-board {
     z-index: 0;
 }

 .type-set.column_one_left {
     text-align: center;
     color: #fff;
     font-weight: 600;
     margin-top: 20px;
     max-width: 320px;
 }

 .type-set.column_one_left #gate-detail {
     margin: 10px auto 30px;
 }

 .active-gate-set.column_one_left {
     margin-bottom: auto;
     height: 160px;
     display: flex;
     flex-direction: column;
     justify-content: flex-end;
     max-width: 52px;
     text-align: center;
     color: #fff;
     font-weight: 600;
     gap: 5px;
 }

 #gate-tab-content .detail-btn.orange-detail-btn,
 #dyn-tab-content .detail-btn.orange-detail-btn {
     width: 52px;
     height: 51px;
 }

 .wheel-item.key-source-item,
 .wheel-item.filtered-item {
     height: 181px;
     justify-content: flex-start;
 }

 .wheel-item.key-source-item .dropdown-detail {
     margin-top: 30px;
 }

 #gate-1-filter {
     margin-bottom: 15px;
 }

 /* DYN tab */
 .dyn-bottom-flex {
     display: flex;
     height: 200px;
     width: 100%;
     max-width: 500px;
     gap: 80px;
 }

 .modes-set.column_one_left {
     display: flex;
     gap: 40px;
 }

 .modes-set.column_one_left .sm-col-one {
     width: 134px;
 }

 .swapper-type,
 .swapper-mode {
     width: 132px;
     height: 43px;
     display: flex;
     justify-content: center;
     border-radius: 4px;
     border: 3px solid #383838;
 }

 .swapper-type>div,
 .swapper-mode>div {
     width: 50%;
     display: flex;
     justify-content: center;
     align-items: center;
     font-size: 14px;
     cursor: pointer;
 }

 .swapper-type>div:first-of-type {
     border-right: 3px solid #383838;
     width: 64px;
 }

 .swapper-type>div:last-of-type {
     width: 60px;
 }

 .swapper-modes {
     display: flex;
     flex-direction: column;
     gap: 15px;
 }

 /* screen animation */
 .screen-show-wrapper {
     position: relative;
 }

 .screen-line.option-1 {
     position: absolute;
     z-index: 2;
     top: -1px;
     right: -2px;
 }

 /* EQ tab */
 .inner-wrapper_eq {
     display: flex;
     padding-left: 0;
     padding-top: 20px;
     gap: 40px;
 }

 .inner-wrapper_eq .detail-btn {
     width: 52px;
     height: 51px;
 }

 .wheel-wrapper-tabs.wheel-container {
     margin-top: 0 !important;
     min-height: 503px;
     flex-wrap: nowrap;
     flex-direction: column;
     gap: 25px;
     margin-bottom: 42px;
 }

 .wheel-wrapper-tabs.wheel-container .wheel-item {
     width: 100%;
     margin-top: 0 !important;
     margin-bottom: 0 !important;
 }

 .wheel-wrapper-tabs.wheel-container .wheel-item .detail-txt {
     margin-top: 0;
     margin-bottom: 10px;
 }

 .bottom-btns-eq {
     display: flex;
     justify-content: center;
     gap: 50px;
 }

 .left-co-eq {
     padding-top: 20px;
     width: 15%;
 }

 .right-co-eq {
     width: 80%;
 }

 .eq-board-wrapper {
     position: relative;
     width: 903px;
     height: 475px;
     background: #000;
     margin-top: 50px;
     margin-bottom: 40px;
     display: flex;
     flex-wrap: wrap;
 }

 .db-numbers {
     color: #fff;
     height: 440px;
     width: 35px;
     font-size: 12px;
     font-weight: bold;
     display: flex;
     flex-direction: column;
     justify-content: flex-start;
     align-items: center;
 }

 .db-numbers>div {
     margin-top: 40px;
     margin-right: 4px;
     width: 22.1px;
     text-align: center;
 }

 .db-numbers>div::after {
     content: "";
     width: 94%;
     height: 1px;
     z-index: 1;
     display: block;
     background: rgb(255 255 255 / 50%);
     position: absolute;
     margin-top: -9px;
     margin-left: 29px;
 }

 .movement-board {
     display: inline-block;
     width: 847px;
     height: 440px;
 }

 .select-btns-eq {
     display: flex;
     gap: 25px;
     margin-left: 40px;
 }

 .below-numbers {
     display: flex;
     width: 869px;
     height: 35px;
     margin-left: 25px;
     color: #fff;
     font-weight: bold;
     font-size: 12px;
 }

 .below-numbers>div {
     width: 22.1px;
     text-align: center;
     display: flex;
     align-items: center;
     justify-content: center;
 }

 .below-numbers>div::before {
     content: "";
     height: 94%;
     width: 1px;
     z-index: 1;
     display: block;
     background: #fff;
     position: absolute;
     bottom: 30px;
     margin-left: 0;
 }

 .track-20::before {
     display: none !important;
 }

 /* numbers track road */
 .track-20K {
     margin-left: auto;
 }

 .track-30 {
     margin-left: 25px;
 }

 .track-40 {
     margin-left: 15px;
 }

 .track-50 {
     margin-left: 7px;
 }

 .track-60 {
     margin-left: 7px;
 }

 .track-70 {
     margin-left: -5px;
 }

 .track-80 {
     margin-left: -7px;
 }

 .track-90 {
     margin-left: -7px;
 }

 .track-100 {
     margin-left: -7px;
 }

 .track-200 {
     margin-left: 60px;
 }

 .track-300 {
     margin-left: 25px;
 }

 .track-400 {
     margin-left: 10px;
 }

 .track-600 {
     margin-left: 25px;
 }

 .track-700 {
     margin-left: -5px;
 }

 .track-800 {
     margin-left: -5px;
 }

 .track-900 {
     margin-left: -5px;
 }

 .track-1K {
     margin-left: -5px;
 }

 .track-2K {
     margin-left: 60px;
 }

 .track-3K {
     margin-left: 25px;
 }

 .track-4K {
     margin-left: 10px;
 }

 .track-5K {
     margin-left: 9px;
 }

 .track-6K {
     margin-left: 0;
 }

 .track-7K {
     margin-left: -5px;
 }

 .track-8K {
     margin-left: -7px;
 }

 .track-9K {
     margin-left: -7px;
 }

 .track-10K {
     margin-left: -7px;
 }

 /* end number track */
 .control-board-btns .detail-txt,
 .bottom-btns-eq .detail-txt {
     margin-top: 10px;
 }

 .reset-wrap-btn {
     margin-right: 25px;
 }

 .control-board-btns {
     display: flex;
     gap: 30px;
 }

 .select-drag-btns-eq {
     display: flex;
     gap: 39px;
     position: relative;
 }

 .select-label {
     color: #fff;
     font-weight: bold;
     font-size: 14px;
     position: absolute;
     top: -27px;
     left: 50%;
     transform: translateX(-50%);
 }

 .select-label::before {
     content: url(../assets/icons-details/select-left-line.svg);
     position: absolute;
     z-index: 1;
     margin-left: -250px;
     margin-top: 8px;
 }

 .select-label::after {
     content: url(../assets/icons-details/select-right-line.svg);
     position: absolute;
     z-index: 1;
     margin-left: 10px;
     margin-top: 8px;
 }

 .inactive-btn {
     width: 52px;
     height: 51px;
     border-radius: 4px;
     border: 3px solid #2d2d2f;
     background: transparent;
 }

 .tab-content-hidden {
     margin-bottom: 20px;
 }
 #detail-page-25 .automix-detail-board,
  #detail-page-26 .automix-detail-board,
   #detail-page-27 .automix-detail-board,
        #detail-page-28 .automix-detail-board,
                 #detail-page-29 .automix-detail-board,
 #detail-page-30 .automix-detail-board,
  #detail-page-31 .automix-detail-board,
   #detail-page-32 .automix-detail-board,
 #detail-page-25 .weight-detail-board,
  #detail-page-26 .weight-detail-board,
   #detail-page-27 .weight-detail-board, 
   #detail-page-28 .weight-detail-board, 
   #detail-page-29 .weight-detail-board, 
   #detail-page-30 .weight-detail-board, 
   #detail-page-31 .weight-detail-board,
#detail-page-32 .weight-detail-board {
     visibility: hidden;
 }