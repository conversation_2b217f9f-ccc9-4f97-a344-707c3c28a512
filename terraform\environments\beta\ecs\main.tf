terraform {
  backend "s3" {
    bucket         = "terraform-state-vse"
    key            = "beta/ecs/terraform.tfstate"
    region         = "us-east-1"
    encrypt        = true
    dynamodb_table = "terraform-lock-table"
  }

  required_providers {
    random = {
      source  = "hashicorp/random"
      version = "~> 3.1"
    }
  }
}

# ECS Cluster
resource "aws_ecs_cluster" "vse_cluster" {
  name = "vse-cluster"
}

# IAM Role for ECS Task Execution
resource "aws_iam_role" "task_execution_role" {
  name = "ecs_task_execution_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# Attach the AmazonECSTaskExecutionRolePolicy to the role
resource "aws_iam_role_policy_attachment" "ecs_task_execution_policy" {
  role       = aws_iam_role.task_execution_role.name
  policy_arn = "arn:aws:iam::aws:policy/service-role/AmazonECSTaskExecutionRolePolicy"
}

# Load Balancer Security Group (define this first)
resource "aws_security_group" "alb_security_group" {
  name        = "alb-security-group"
  description = "Allow HTTP and HTTPS traffic to ALB"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port   = 80
    to_port     = 80
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}


# Security Group for ECS Service
resource "aws_security_group" "ecs_security_group" {
  name        = "ecs-security-group-${random_id.sg_suffix.hex}"
  description = "Allow HTTP traffic from NLB"
  vpc_id      = module.vpc.vpc_id

  lifecycle {
    create_before_destroy = true
  }
}

# Random ID for security group naming
resource "random_id" "sg_suffix" {
  byte_length = 4
}

# Add the ingress rules back to the security group
resource "aws_security_group_rule" "ecs_http" {
  type              = "ingress"
  from_port         = 80
  to_port           = 80
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.ecs_security_group.id
}

resource "aws_security_group_rule" "ecs_https" {
  type              = "ingress"
  from_port         = 443
  to_port           = 443
  protocol          = "tcp"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.ecs_security_group.id
}

resource "aws_security_group_rule" "ecs_app" {
  type              = "ingress"
  from_port         = 5000
  to_port           = 5000
  protocol          = "tcp"
  cidr_blocks       = ["10.0.0.0/16"]
  security_group_id = aws_security_group.ecs_security_group.id
}

resource "aws_security_group_rule" "ecs_egress" {
  type              = "egress"
  from_port         = 0
  to_port           = 0
  protocol          = "-1"
  cidr_blocks       = ["0.0.0.0/0"]
  security_group_id = aws_security_group.ecs_security_group.id
}

# ECS Task Definition
resource "aws_ecs_task_definition" "vse_task" {
  family                   = "vse-task"
  network_mode             = "awsvpc"
  requires_compatibilities = ["FARGATE"]
  execution_role_arn       = aws_iam_role.task_execution_role.arn
  task_role_arn            = aws_iam_role.ecs_task_role.arn
  cpu                      = "512"
  memory                   = "1024"
  
  container_definitions = jsonencode([
    {
      name      = "traefik"
      image     = "traefik:v3.0"
      essential = true
      portMappings = [
        {
          containerPort = 80
          hostPort      = 80
        },
        {
          containerPort = 443
          hostPort      = 443
        }
      ]
      command = [
        "--api.dashboard=true",
        "--api.insecure=true",
        "--ping=true",
        "--providers.ecs=true",
        "--providers.ecs.region=us-east-1",
        "--providers.ecs.clusters=vse-cluster",
        "--entrypoints.web.address=:80",
        "--entrypoints.websecure.address=:443",
        "--certificatesresolvers.letsencrypt.acme.tlschallenge=true",
        "--certificatesresolvers.letsencrypt.acme.email=<EMAIL>",
        "--certificatesresolvers.letsencrypt.acme.storage=/data/acme.json",
        "--log.level=DEBUG"
      ]
      mountPoints = [
        {
          sourceVolume  = "traefik-data"
          containerPath = "/data"
        }
      ]
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = "/ecs/vse-traefik"
          awslogs-region        = "us-east-1"
          awslogs-stream-prefix = "ecs"
        }
      }
    },
    {
      name      = "vse-app"
      image     = "${var.ecr_repository_url}:latest"
      essential = true
      portMappings = [
        {
          containerPort = 5000
          hostPort      = 5000
        }
      ]
      dockerLabels = {
        "traefik.enable" = "true"
        
        # HTTPS router for main traffic
        "traefik.http.routers.vse-secure.rule" = "Host(`vseaudiobeta.com`)"
        "traefik.http.routers.vse-secure.entrypoints" = "websecure"
        "traefik.http.routers.vse-secure.tls.certresolver" = "letsencrypt"
        
        # HTTP router - simple redirect to HTTPS (ACME challenges handled automatically)
        "traefik.http.routers.vse-insecure.rule" = "Host(`vseaudiobeta.com`)"
        "traefik.http.routers.vse-insecure.entrypoints" = "web"
        "traefik.http.routers.vse-insecure.middlewares" = "redirect-to-https"
        
        # Redirect middleware
        "traefik.http.middlewares.redirect-to-https.redirectscheme.scheme" = "https"
        "traefik.http.middlewares.redirect-to-https.redirectscheme.permanent" = "true"
        
        # Service definition
        "traefik.http.services.vse.loadbalancer.server.port" = "5000"
      }
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = "/ecs/vse-app"
          awslogs-region        = "us-east-1"
          awslogs-stream-prefix = "ecs"
        }
      }
    }
  ])
  
  volume {
    name = "traefik-data"
    efs_volume_configuration {
      file_system_id = aws_efs_file_system.traefik_data.id
      root_directory = "/"
    }
  }
}

# ECS Service
resource "aws_ecs_service" "vse_service" {
  name            = "vse-service"
  cluster         = aws_ecs_cluster.vse_cluster.id
  task_definition = aws_ecs_task_definition.vse_task.arn
  desired_count   = 1
  launch_type     = "FARGATE"

  network_configuration {
    subnets          = module.vpc.private_subnets
    security_groups  = [aws_security_group.ecs_security_group.id]
    assign_public_ip = false
  }


  load_balancer {
    target_group_arn = aws_lb_target_group.vse_http_tg.arn
    container_name   = "traefik"
    container_port   = 80
  }

  load_balancer {
    target_group_arn = aws_lb_target_group.vse_https_tg.arn
    container_name   = "traefik"
    container_port   = 443
  }

  depends_on = [aws_lb_listener.vse_http_listener, aws_lb_listener.vse_https_listener, aws_efs_mount_target.traefik_data]
}

# Network Load Balancer
resource "aws_lb" "vse_alb" {
  name               = "vse-nlb"
  internal           = false
  load_balancer_type = "network"
  subnets            = module.vpc.public_subnets
}

# HTTPS certificates managed by Let's Encrypt

# Create Route 53 Zone for the domain
resource "aws_route53_zone" "vse_zone" {
  name = "vseaudiobeta.com"
  
  tags = {
    Environment = "beta"
    Project     = "VSE"
  }
}

# Route 53 A Record
resource "aws_route53_record" "vse_alias" {
  zone_id = aws_route53_zone.vse_zone.zone_id
  name    = "vseaudiobeta.com"
  type    = "A"

  alias {
    name                   = aws_lb.vse_alb.dns_name
    zone_id                = aws_lb.vse_alb.zone_id
    evaluate_target_health = true
  }
}

# HTTP Target Group
resource "aws_lb_target_group" "vse_http_tg" {
  name        = "vse-http-target-group"
  port        = 80
  protocol    = "TCP"
  vpc_id      = module.vpc.vpc_id
  target_type = "ip"
  health_check {
    protocol            = "HTTP"
    path                = "/ping"
    port                = "5000"
    interval            = 30
    timeout             = 10
    healthy_threshold   = 2
    unhealthy_threshold = 5
  }
}

# HTTPS Target Group
resource "aws_lb_target_group" "vse_https_tg" {
  name        = "vse-https-target-group"
  port        = 443
  protocol    = "TCP"
  vpc_id      = module.vpc.vpc_id
  target_type = "ip"
  health_check {
    protocol            = "HTTP"
    path                = "/ping"
    port                = "5000"
    interval            = 30
    timeout             = 10
    healthy_threshold   = 2
    unhealthy_threshold = 5
  }
}

# HTTP Listener
resource "aws_lb_listener" "vse_http_listener" {
  load_balancer_arn = aws_lb.vse_alb.arn
  port              = "80"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.vse_http_tg.arn
  }
}

# HTTPS Listener
resource "aws_lb_listener" "vse_https_listener" {
  load_balancer_arn = aws_lb.vse_alb.arn
  port              = "443"
  protocol          = "TCP"

  default_action {
    type             = "forward"
    target_group_arn = aws_lb_target_group.vse_https_tg.arn
  }
}


# EFS File System for certificate storage
resource "aws_efs_file_system" "traefik_data" {
  creation_token = "traefik-data"
  encrypted      = true

  tags = {
    Name        = "traefik-data"
    Environment = "beta"
    Project     = "VSE"
  }
}

# EFS Mount Targets (one per AZ)
resource "aws_efs_mount_target" "traefik_data" {
  count           = length(module.vpc.private_subnets)
  file_system_id  = aws_efs_file_system.traefik_data.id
  subnet_id       = module.vpc.private_subnets[count.index]
  security_groups = [aws_security_group.efs_security_group.id]
}

# Security Group for EFS
resource "aws_security_group" "efs_security_group" {
  name        = "efs-security-group"
  description = "Security group for EFS mount targets"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port       = 2049
    to_port         = 2049
    protocol        = "tcp"
    security_groups = [aws_security_group.ecs_security_group.id]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "efs-security-group"
    Environment = "beta"
    Project     = "VSE"
  }
}

# CloudWatch Log Group
resource "aws_cloudwatch_log_group" "traefik_logs" {
  name              = "/ecs/vse-traefik"
  retention_in_days = 30

  tags = {
    Environment = "beta"
    Project     = "VSE"
  }
}

# VPC Module
module "vpc" {
  source               = "terraform-aws-modules/vpc/aws"
  name                 = "vse-app-vpc"
  cidr                 = "10.0.0.0/16"
  azs                  = ["us-east-1a", "us-east-1b"]
  public_subnets       = ["********/24", "********/24"]
  private_subnets      = ["********/24", "********/24"]
  enable_nat_gateway   = true
  single_nat_gateway   = true
  enable_dns_hostnames = true
  enable_dns_support   = true
}



# Fetch AWS account details
data "aws_caller_identity" "current" {}

# IAM Policy with dynamic account ID
resource "aws_iam_policy" "ecr_access_policy" {
  name        = "ecs-task-ecr-access-policy"
  description = "Policy to allow ECS tasks to pull images from ECR"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage",
          "ecr:BatchCheckLayerAvailability"
        ]
        Resource = "arn:aws:ecr:us-east-1:${data.aws_caller_identity.current.account_id}:repository/vse-app"
      },
      {
        Effect = "Allow"
        Action = [
          "ecr:GetAuthorizationToken"
        ]
        Resource = "*"
      }
    ]
  })
}


resource "aws_iam_role_policy_attachment" "ecs_task_ecr_access" {
  role       = aws_iam_role.task_execution_role.name
  policy_arn = aws_iam_policy.ecr_access_policy.arn
}

resource "aws_security_group" "vpc_endpoint" {
  name        = "vpc-endpoint-sg"
  description = "Allow traffic for VPC Endpoints"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"] # Restrict further if necessary
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

# Define the ECS Task Role
resource "aws_iam_role" "ecs_task_role" {
  name = "ecs_task_role"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = "ecs-tasks.amazonaws.com"
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# ECS service discovery policy
resource "aws_iam_policy" "traefik_ecs_discovery_policy" {
  name        = "traefik-ecs-discovery-policy"
  description = "Policy to allow Traefik to discover ECS services and tasks"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ecs:ListClusters",
          "ecs:DescribeClusters",
          "ecs:ListTasks",
          "ecs:DescribeTasks",
          "ecs:DescribeContainerInstances",
          "ecs:DescribeTaskDefinition",
          "ecs:ListServices",
          "ecs:DescribeServices",
          "ec2:DescribeInstances",
          "ec2:DescribeNetworkInterfaces"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach a policy to allow SSM access (you can restrict this further as needed)
resource "aws_iam_policy" "ecs_task_ssm_access_policy" {
  name        = "ecs-task-ssm-access-policy"
  description = "Policy to allow ECS tasks to access SSM Parameter Store"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ssm:*"
        ]
        Resource = [
          "*"
        ]
      },
      {
        Effect = "Allow"
        Action = [
          "kms:Decrypt"
        ]
        Resource = "*"
      }
    ]
  })
}

# Attach ECS discovery policy to task role
resource "aws_iam_role_policy_attachment" "ecs_task_traefik_discovery" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.traefik_ecs_discovery_policy.arn
}

# Attach the SSM policy to the task role
resource "aws_iam_role_policy_attachment" "ecs_task_ssm_access" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.ecs_task_ssm_access_policy.arn
}

resource "aws_cloudwatch_log_group" "ecs_log_group" {
  name              = "/ecs/vse-app"
  retention_in_days = 7 # Adjust the retention period as needed
}

resource "aws_iam_policy" "ecs_task_execution_logs_policy" {
  name        = "ecs-task-execution-logs-policy"
  description = "Policy to allow ECS tasks to write to CloudWatch Logs"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "logs:*"
        ]
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "ecs_task_execution_logs" {
  role       = aws_iam_role.task_execution_role.name
  policy_arn = aws_iam_policy.ecs_task_execution_logs_policy.arn
}

# Attach the SSM policy to the task role
resource "aws_iam_role_policy_attachment" "ecs_task_cw_access" {
  role       = aws_iam_role.ecs_task_role.name
  policy_arn = aws_iam_policy.ecs_task_execution_logs_policy.arn
}

resource "aws_vpc_endpoint" "cloudwatch_logs" {
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.aws_region}.logs"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = module.vpc.private_subnets
  security_group_ids  = [aws_security_group.vpc_endpoint.id]
  private_dns_enabled = true
}

resource "aws_security_group" "cloudwatch_logs_endpoint_sg" {
  name        = "cloudwatch-logs-vpc-endpoint-sg"
  description = "Security group for CloudWatch Logs VPC Endpoint"
  vpc_id      = module.vpc.vpc_id

  ingress {
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = ["10.0.0.0/16"] # Adjust based on your VPC CIDR
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_vpc_endpoint" "ssm" {
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.aws_region}.ssm"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = module.vpc.private_subnets
  security_group_ids  = [aws_security_group.vpc_endpoint.id]
  private_dns_enabled = true
}

resource "aws_vpc_endpoint" "ssm_messages" {
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.aws_region}.ssmmessages"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = module.vpc.private_subnets
  security_group_ids  = [aws_security_group.vpc_endpoint.id]
  private_dns_enabled = true
}

resource "aws_vpc_endpoint" "ec2_messages" {
  vpc_id              = module.vpc.vpc_id
  service_name        = "com.amazonaws.${var.aws_region}.ec2messages"
  vpc_endpoint_type   = "Interface"
  subnet_ids          = module.vpc.private_subnets
  security_group_ids  = [aws_security_group.vpc_endpoint.id]
  private_dns_enabled = true
}
