<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Virtual Sound Engineer Scenes</title>
    <link rel="shortcut icon" href="assets/fav/fav-vse.png" />
    <link rel="stylesheet" href="assets/style/main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
      rel="stylesheet"
    />
    <!-- Include jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include jQuery UI via CDN -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Include touch-punch library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    <style>
      html,
      * {
        box-sizing: border-box;
      }
      body,
      a,
      p,
      div {
        font-family: "Open Sans", sans-serif;
      }
      body {
        background: #626262;
        margin: 0;
        padding: 0;
      }
      .page-wrapper {
        padding: 25px 20px;
        max-height: 100vh;
        max-width: 100%;
      }
      .container {
        display: flex;
        gap: 25px;
        flex-wrap: nowrap;
      }
      .left-sidebar {
        width: 10%;
        text-align: center;
      }
      .main {
        width: 90%;
      }
      /* buttons */

      .btn-block {
        border-radius: 4px;
        border: 3px solid #f5f5f5;
        background: #2d2d2f;
        color: #fff;
        text-transform: uppercase;
        font-size: 14px;
        height: 38px;
        width: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      .btn-block p,
      .left-sidebar p {
        color: #fff;
        font-weight: 600;
        text-transform: uppercase;
        font-size: 14px;
      }
      /* main part header */
      .main-item.navbar {
        display: flex;
        justify-content: space-around;
        align-items: center;
      }
      .main-item.navbar a {
        display: flex;
        flex-direction: column;
        gap: 10px;
        justify-content: center;
        align-items: center;
        text-decoration: none;
        text-transform: uppercase;
        color: #fff;
        font-size: 14px;
        font-weight: bold;
      }
      .navbar-item-img {
        background: #000;
        height: 40px;
        width: 40px;
        border-radius: 100px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 0 0 3px #00000014;
      }
      .navbar-item-img.active_item {
        background: #fff;
        box-shadow: none;
      }
      .navbar-item-img.active_item img {
        filter: invert(1);
      }
      .navbar-item-img.homed img {
        margin-bottom: 2px;
        margin-left: 2px;
      }
      .scenes img {
        margin-top: 7px;
        margin-right: 7px;
      }
      .meters img {
        margin-bottom: 7px;
        margin-left: 5px;
        width: 32px;
      }

      /* sidebar items */
      .sidebar-item.controls {
        width: 100%;
        max-width: 130px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin: 15px auto;
        border: 3px solid #f5f5f5;
        border-radius: 4px;
        gap: 0 !important;
      }
      .sidebar-item.controls .btn-block {
        border: none;
        width: 32%;
        border-radius: 0;
      }
      .sidebar-item.controls .btn-block:nth-of-type(2) {
        border-left: 3px solid #f5f5f5;
        border-right: 3px solid #f5f5f5;
        border-radius: 0 !important;
        width: 36%;
        margin: 0;
      }
      /* siebar - 4 buttons */
      .sidebar-item.addons {
        width: 100%;
        max-width: 134px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        margin: 20px auto;
        gap: 20px;
      }
      .sidebar-item.addons .btn-block {
        width: 42%;
        max-width: 56px;
        max-height: 36px;
      }
      /* fader */
      .activated-faders {
        width: 100%;
        max-width: 130px;
        min-height: 151px;
        margin: auto;
        background: #f5f5f5;
        box-shadow: 0 0 9.2px 1px #f5f5f5;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        gap: 10px;
        display: none;
      }
      .activated-faders img {
        width: 100%;
        max-width: 60%;
      }
      .btn-block.sidebar-item.faders {
        width: 100%;
        max-width: 134px;
        height: 46px;
        display: flex;
        align-items: center;
        margin: 20px auto;
      }
      .btn-block.sidebar-item.faders p {
        font-size: 12px;
      }
      /* 1-2 when fader activated */
      .send-faders-active-options {
        width: 100%;
        max-width: 120px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        display: none;
      }
      .send-faders-active {
        width: 46%;
        max-width: 55px;
        height: 46px;
        margin-bottom: 20px;
      }
      /* X Y auto mixing */
      .sidebar-item.mixing {
        width: 100%;
        max-width: 134px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        margin: 45px auto;
      }

      .sidebar-item.mixing p {
        width: 100%;
      }
      .btn-block.mixing-item {
        width: 46%;
        max-width: 55px;
        height: 46px;
      }
      /* mute groups */
      .sidebar-item.mute-group {
        width: 100%;
        max-width: 134px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex-wrap: wrap;
        margin: 20px auto;
        gap: 15px;
      }
      .sidebar-item.mute-group .btn-block {
        width: 46%;
        max-width: 55px;
        height: 55px;
      }
      .sidebar-item.mute-group p {
        width: 100%;
        margin-bottom: 0;
      }
      .sidebar-item.mute-group .last-mute {
        width: 100%;
        max-width: 100%;
      }
      /* solos  */
      .sidebar-item.show-solos {
        width: 100%;
        max-width: 134px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        margin: 20px auto;
      }

      .sidebar-item.show-solos p {
        width: 100%;
      }
      .show-solos-item-wrap {
        max-width: 134px;
        width: 100%;
        display: flex;
        border: 3px solid #f5f5f5;
        border-radius: 4px;
      }
      .show-solos-item {
        border: none;
        width: 50%;
        border-radius: 0;
        background: #2d2d2f;
        color: #fff;
        text-transform: uppercase;
        font-size: 14px;
        height: 38px;
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .show-solos-item .btn-block:last-of-type {
        border-left: 3px solid #f5f5f5;
        border-radius: 0 !important;
        margin: 0;
      }
      /* lock mutes big */
      .btn-block.sidebar-item.lock-mute {
        width: 100%;
        max-width: 134px;
        height: 46px;
        display: flex;
        align-items: center;
        flex-wrap: wrap;
        margin: 20px auto;
      }
      .btn-block.sidebar-item.lock-mute p {
        margin: 0;
      }
      /* MAIN */
      /* board equalizer */
      .screen-channels-wrapper {
        margin-top: 30px;
        display: flex;
        justify-content: space-between;
        max-width: 97%;
      }
      .screen-channels-item {
        max-width: 96px;
        text-align: center;
        display: flex;
        flex-direction: column;
        justify-content: center;
        gap: 0;
        border-radius: 4px;
        overflow: hidden;
        box-shadow: 0 0 0 3px #00000014;
        cursor: pointer;
      }
      .equalizer-screens {
        display: flex;
        justify-content: center;
        align-items: flex-end;
      }
      .bottom-screen-info {
        background: #000;
        color: #fff;
        height: 32px;
        font-size: 12px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
      .equalizer-board {
        position: relative;
        height: 138px;
        background: linear-gradient(
          rgba(206, 24, 30, 1) 0%,
          rgba(236, 140, 14, 1) 18%,
          rgba(248, 215, 6, 1) 38%,
          rgba(136, 195, 59, 1) 100%
        );
        overflow: hidden;
        z-index: -2;
      }
      .tab-channel-active {
        border: 3px solid #fff;
        box-shadow: none;
        max-width: 102px;
      }
      .equalizer-board img {
        width: 100%;
      }
      /* mixer */
      .main-item.draggers {
        margin-top: 25px;
        margin-bottom: 20px;
        display: flex;
      }
      .mixer-wrap {
        width: 12.5%;
        min-width: 120px;
      }
      .mixer-board {
        max-width: 88px;
      }
      .mixer-container {
        max-width: 120px;
        display: flex;
        gap: 5px;
        align-items: flex-end;
      }
      .mixer-container .inner-container {
        max-width: 88px;
        position: relative;
      }
      .mixer-container .inner-container .tooltip {
        position: absolute;
        z-index: 3;
        top: -12px !important;
        left: 16%;
        background: url(assets/icons/Union.svg);
        background-repeat: no-repeat;
        color: #fff;
        font-size: 12px;
        background-size: cover;
        width: 85px;
        height: 33px;
        display: flex;
        justify-content: center;
        align-items: center;
        padding-bottom: 5px;
        font-weight: bold;
      }
      .number-mixer {
        color: #fff;
        font-size: 20px;
        line-height: 1;
      }
      .volume-board {
        max-width: 24px;
        width: 100%;
        height: 319px;
        border-radius: 10px;
        margin-bottom: 20px;
        background: linear-gradient(
          rgba(206, 24, 30, 1) 0%,
          rgba(236, 140, 14, 1) 18%,
          rgba(248, 215, 6, 1) 38%,
          rgba(136, 195, 59, 1) 100%
        );
        position: relative;
        overflow: hidden;
        z-index: -2;
      }
      .volume-board img {
        max-width: 24px;
        width: 100%;
      }
      .mixer-dragger {
        position: absolute;
        z-index: 2;
        bottom: -5px;
        left: 26px;
        max-width: 59px;
      }
      /* mixer channels */
      .channel-screen {
        width: 120px;
        height: 71px;
        background: #1f1f21;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        padding-bottom: 10px;
        margin-bottom: 20px;
        border-radius: 4px;
        font-weight: 600;
        text-decoration: none;
      }

      /* mixer mute buttons */
      .main-board-buttons.mute-solo {
        max-width: 120px;
        display: flex;
        margin-top: 30px;
        justify-content: center;
        gap: 20px;
      }
      .main-board-buttons.mute-solo .mute-solo-item {
        width: 56px;
        height: 40px;
      }
      /* needle board and needle */

      .needle-screen {
        max-width: 120px;
        margin-bottom: 30px;
        position: relative;
        overflow: hidden;
        /* max-height: 55px;
        cursor: pointer;
        transition: max-height 0.4s ease-out; */
        max-height: 55px; /* Initial height */
        cursor: pointer;
        transition: 0.3s ease-in-out;
        border-radius: 4px;
      }
      .needle-screen:hover {
        max-height: 81px;
        overflow: visible;
        transition: height 0.3s ease;
      }
      .needle-path {
        width: 100%;
      }
      .needle-dragger {
        position: absolute;
        z-index: 1;
        top: 0;
        left: 46%;
      }

      /* ANIMATIONS */
      .btn-activated {
        background: #f5f5f5;
      }

      .overlay {
        position: absolute;
        border-radius: 0;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: #2a2a2a; /* Adjust opacity as needed */
        z-index: -1;
      }
      /* btn animation */
      .active-btn-shadow {
        box-shadow: 0 0 9.2px 1px #f5f5f5;
      }
      .active-btn-background {
        background: #f5f5f5;
        color: #222;
        font-weight: 600;
      }
      .btn-block.active-btn-background p {
        color: #222;
        font-weight: 600;
      }
      .btn-block.active-btn-background img {
        filter: invert(1);
      }

      /* send on faders */
      .send-faders-active-options.show-flex,
      .activated-faders.show-flex {
        display: flex;
      }
      .needle-screen.hide-if-faders,
      .sidebar-item.mixing.hide-if-faders {
        display: none;
      }

      /* TABS Switch channels */

      .main-item.draggers {
        display: none;
      }

      .main-item.draggers.tab-active {
        display: flex;
      }
      .screen-channels-item.tab-active {
        box-shadow: 0 0 0 3px #fff;
      }

      .btn-block.edit-dca-item {
        width: 100%;
        max-width: 120px;
        height: 44px;
        margin-bottom: 15px;
      }
      .edit-dca-wrap {
        height: 55px;
        margin-bottom: 30px;
      }
      /* tab content colors change */
      #channel-aux .channel-screen,
      #channel-fx .channel-screen {
        background: #c8ffa5;
        color: #171100;
      }
      #channel-bus18 .channel-screen,
      #channel-bus916 .channel-screen {
        background: #b9ffff;
        color: #171100;
      }
      #channel-mtx .channel-screen,
      #channel-dca .channel-screen {
        background: #ffc2ff;
        color: #171100;
      }
      #channel-mtx .mixer-wrap:nth-of-type(8) .channel-screen,
      #channel-mtx .mixer-wrap:nth-of-type(7) .channel-screen {
        background: #f2ede9;
        color: #171100;
      }
      #channel-mtx .mixer-wrap .needle-screen {
        visibility: hidden;
      }
      #channel-mtx .mixer-wrap:last-of-type .needle-screen {
        visibility: visible;
      }
      #channel-dca .mixer-wrap .needle-screen {
        display: none;
      }
      /*  TABS END  */
      /* MUTE disable */
      .muted.disabled,
      .no-faders-tab.disabled {
        pointer-events: none;
        opacity: 0.6;
      }
      /*  MUTE Disable end */

      /* SCENE PAGE */

      .below-main {
        background-color: #5a5a5a;
        padding: 10px 6px;
        position: relative;
      }
      .top-scene-part {
        background-color: #c7c7c7;
        display: flex;
      }
      .top-scene-part .show-name-bar {
        width: 40%;
        padding: 10px 12px;
      }
      .top-scene-part .drag-to-bar {
        width: 60%;
        display: flex;
        align-items: center;
      }
      .new-ticket,
      .current-ticket {
        width: 37%;
        display: flex;
        align-items: center;
      }
      .current-ticket img {
        max-width: 239px;
      }
      .vertical-letters {
        writing-mode: vertical-rl;
        transform: scale(-1);
        color: #2d2d2f;
        font-weight: 600;
        font-size: 13px;
      }
      .current-ticket {
        gap: 10px;
      }
      .drag-ticket {
        width: 26%;
        margin: 5px;
        padding: 5px;
        background: url(/assets/icons-scene/dashes-scene-ticket.svg);
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        height: 84px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #fff;
        font-weight: 600;
        gap: 10px;
        filter: invert(1);
      }

      /* names and buttons */
      .show-name-bar {
        display: flex;
        gap: 4%;
        border-right: 1px solid #ffffff1a;
        justify-content: center;
        align-items: center;
      }
      .show-name-bar .name-buttons {
        display: flex;
        width: 85%;
        flex-direction: column;
        gap: 10px;
      }
      .name-scene {
        display: flex;
        justify-content: flex-start;
        align-items: center;
        gap: 30px;
      }
      .reverse-button {
        width: 10%;
      }
      .reverse {
        max-width: 53px;
        height: 53px;
        border-radius: 4px;
        border: 3px solid #383838;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
      }
      .reverse img {
        width: 50%;
        filter: invert(1);
      }
      .show-name {
        font-size: 18px;
        color: #2d2d2f;
        font-weight: 600;
        padding-left: 5px;
      }
      .scene-name {
        font-size: 14px;
        font-weight: 600;
        color: #fff;
        padding: 10px 15px;
        background-color: #383838;
        border-radius: 4px;
        width: 100%;
        max-width: 280px;
      }
      .buttons-scene {
        display: flex;
        gap: 15px;
      }
      .buttons-scene .btn-block,
      .btn-block-scene {
        border-radius: 4px;
        border: 3px solid #2d2d2f;
        background: transparent;
        color: #2d2d2f;
        font-weight: bold;
        text-transform: uppercase;
        font-size: 14px;
        height: 38px;
        width: 42px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
        max-width: 132px;
        width: 100%;
        height: 34px;
      }
      .active-btn-background {
        background: #222 !important;
        color: #fff !important;
      }
      /* accordion part */
      .accordion-row {
        display: flex;
        height: 63px;
      }
      .numbered {
        width: 5%;
        padding-left: 15px;
        display: flex;
        align-items: center;
      }
      .named {
        width: 20%;
        padding-left: 15px;
        display: flex;
        align-items: center;
        border-right: 1px solid #515050;
        border-left: 1px solid #515050;
        border-top: 1px solid #515050;
      }

      .noted {
        width: 35%;
        padding-left: 15px;
        display: flex;
        align-items: center;
        border-right: 1px solid #515050;
        border-top: 1px solid #515050;
      }
      .pins {
        width: 40%;
        padding-left: 15px;
        display: flex;
        border-top: 1px solid #515050;
      }
      .accordion-row > div {
        color: #2d2d2f;
        font-weight: bold;
        font-size: 18px;
      }
      .named,
      .noted {
        outline: none;
      }
      .heading-row .named,
      .heading-row .noted,
      .heading-row .pins {
        border: none !important;
      }
      .accordion-row .pin-item {
        font-size: 10px;
        writing-mode: vertical-rl;
        transform: scale(-1);
        width: 12.4%;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 63px;
      }

      .accordion-item-nav .numbered {
        cursor: pointer;
      }

      .pinned {
        background: #3f3f3f;
        height: 16px;
        width: 16px;
        border-radius: 100px;
        cursor: pointer;
      }
      .pinned-active {
        background: #f2ede9;
      }
      .accordion-item-content {
        max-height: 0;
        overflow: hidden;
        transition: max-height 0.3s ease-out; /* Adjust timing and easing as needed */
        background-color: #3f3f3f;
      }

      .accordion-item-content.activate-edit {
        max-height: 125px; /* Adjust max-height as needed */
      }
      .buttons-scene.accordion-opened {
        max-width: 400px;
        flex-wrap: wrap;
        padding: 20px 0 20px 5%;
      }
      .activate-edit .named,
      .activate-edit .noted {
        background: #3f3f3f;
      }
      /* test scroll bar for scene elements */

      /* Hide the scrollbar for WebKit browsers */
      /* Note: Adjust for Firefox using `scrollbar-width: none;` */
      .bottom-scene-part {
        height: 500px;
        overflow-y: scroll;
        position: relative;
      }
      .bottom-scene-part::-webkit-scrollbar {
        width: 5px;
      }

      /* Optional: Style the scrollbar track */
      .bottom-scene-part::-webkit-scrollbar-track {
        /* Set the track color to match the background */
        background: #f0f0f0; /* Change this to your background color */
      }

      /* Optional: Style the scrollbar thumb */

      .bottom-scene-part::-webkit-scrollbar-thumb {
        /* Make the thumb almost invisible */
        background: rgba(0, 0, 0, 0.3); /* Adjust opacity as needed */
        /* Set a minimum size for the thumb */
        min-height: 20px;
      }

      /* popups */
      .param-safe-wrapper,
      .chain-safe-wrapper {
        display: none;
        position: absolute;
        top: 115px;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 5;
        background: #5a5a5a;
      }
      .chain-safe-wrapper .param-safe-input {
        width: 46%;
      }
      .chain-safe-wrapper .param-safe-input_items_three_col {
        gap: 30px;
      }
      .visible_block {
        display: block;
      }
      .param-safe-flex {
        display: flex;
        width: 100%;
        max-width: 1290px;
        margin: 50px auto 30px;
        gap: 80px;
      }
      .param-safe-input {
        width: 55%;
      }
      .param-safe-buses {
        width: 17%;
      }
      .param-safe-console {
        width: 17%;
      }
      .param-safe-input_title {
        color: #fff;
        font-weight: bold;
        text-align: center;
      }
      .param-safe-input_items_three_col {
        display: flex;
        gap: 70px;
        justify-content: space-around;
      }
      .param-safe-input_items {
        display: flex;
        margin-top: 20px;
        flex-direction: column;
        gap: 20px;
      }
      .single_param_item {
        display: flex;
        align-items: center;
        gap: 20px;
        color: #fff;
        font-weight: 16px;
        font-weight: bold;
      }
      /* .active-btn-background {
        background: #f5f5f5 !important;
        color: #222 !important;
      } */
    </style>
    <link rel="stylesheet" href="assets/style/main-light.css" />
  </head>
  <body id="home-page" class="home scene">
    <div class="page-wrapper">
      <div class="container main-flex">
        <!-- sidebar -->
        <div class="left-sidebar">
          <div class="sidebar-item logo">
            <a class="logo-btn" id="main-logo-btn" href="/index-light.html"
              ><img src="assets/icons-lightmode/light-logo.svg" alt="logo"
            /></a>
          </div>
          <div class="sidebar-item controls">
            <div id="info" class="btn-block controls-item">
              <img src="assets/icons/i.svg" alt="i" />
            </div>
            <div id="mute" class="btn-block controls-item">
              <img src="assets/icons/mute.svg" alt="mute" />
            </div>
            <div id="wifi" class="btn-block controls-item">
              <img src="assets/icons/wifi.svg" alt="wifi" />
            </div>
          </div>
          <div id="addons" class="sidebar-item addons">
            <div id="user" class="btn-block addons-item">
              <img src="assets/icons/user-icon.svg" alt="user" />
            </div>
            <div id="screen" class="btn-block addons-item">
              <img src="assets/icons/screen-icon.svg" alt="screen" />
            </div>
            <div id="chat" class="btn-block addons-item">
              <img src="assets/icons/chat-icon.svg" alt="chat" />
            </div>
            <div id="video" class="btn-block addons-item">
              <img src="assets/icons/video-icon.svg" alt="video" />
            </div>
          </div>
        </div>
        <!-- sidebar end -->
        <!-- main  screen part  -->
        <div class="main">
          <!-- navbar -->
          <div class="main-item navbar">
            <a href="/index-light.html"
              ><div class="navbar-item-img homed">
                <img src="assets/icons/home-icon-white.svg" alt="home" />
              </div>
              <span>home</span></a
            >
            <a href="/detail-light/ch01.html"
              ><div class="navbar-item-img">
                <img src="assets/icons/detail-page-icon.svg" alt="detail" />
              </div>
              <span>detail</span></a
            >
            <a href="/effects-light.html"
              ><div class="navbar-item-img">
                <img src="assets/icons/effects-page-icon.svg" alt="effects" />
              </div>
              <span>effects</span></a
            >
            <a href="/scenes-light.html"
              ><div class="navbar-item-img scenes active_item">
                <img src="assets/icons/scenes-page-icon.svg" alt="scenes" />
              </div>
              <span>scenes</span></a
            >
            <a href="/meters-light.html"
              ><div class="navbar-item-img meters">
                <img src="assets/icons/meters-page-icon.svg" alt="meters" />
              </div>
              <span>meters</span></a
            >
            <a href="/routing-light.html"
              ><div class="navbar-item-img">
                <img src="assets/icons/routing-page-icon.svg" alt="routing" />
              </div>
              <span>routing</span></a
            >
            <a href="/login.html"
              ><div class="navbar-item-img">
                <img src="assets/icons/log-out-icon.svg" alt="log-out" />
              </div>
              <span>logout</span></a
            >
          </div>
          <!-- navbar end -->
          <!-- screen channels swap -->
          <div class="main-item screen-channels-wrapper">
            <a
              href="/detail-light/ch01.html"
              id="channel-select-1-8"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="3">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="7">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="8">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">CH 1-8</div>
            </a>
            <!-- item 2 screens -->
            <a
              href="/detail-light/ch09.html"
              id="channel-select-9-16"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="9">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="10">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="11">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="12">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="13">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="14">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="15">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="18">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">CH 9-16</div>
            </a>
            <!-- item 3 screens -->
            <a
              href="/detail-light/ch17.html"
              id="channel-select-17-24"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="17">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="18">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="19">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="20">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="21">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="22">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="23">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="24">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">CH 17-24</div>
            </a>
            <!-- item 4 screens -->
            <a
              href="/detail-light/ch25.html"
              id="channel-select-25-32"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="25">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="26">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="27">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="28">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="29">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="30">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="31">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="32">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">CH 25-32</div>
            </a>
            <!-- item 5 screens -->
            <a
              href="/detail-light/aux01.html"
              id="channel-select-aux"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="a1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="a2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="a3">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="a4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="a5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="a6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="a7">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="a8">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">AUX 1-8</div>
            </a>
            <!-- items 6 screens -->
            <a
              href="/detail-light/fx1l.html"
              id="channel-select-fx1l"
              class="screen-channels-item"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="fx1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="fx2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="fx3">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="fx4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="fx5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="fx6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="fx7">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="fx8">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">FX 1L-4R</div>
            </a>
            <!-- item 7 screens -->
            <a
              href="/detail-light/bus01.html"
              id="channel-select-bus"
              class="screen-channels-item"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="b1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="b2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="b3">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="b4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="b5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="b6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="b7">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="b8">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">BUS 1-8</div>
            </a>
            <!-- item 8 screens -->
            <a
              href="/detail-light/bus09.html"
              id="channel-select-bus916"
              class="screen-channels-item"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="b9">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="b10">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="b11">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="b12">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="b13">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="b14">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="b15">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="b16">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">BUS 9-16</div>
            </a>
            <!-- screen 9 item -->
            <a
              href="/detail-light/mtx-main01.html"
              id="channel-select-mtx"
              class="screen-channels-item"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="mtx1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="mtx2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-three"
                  data-channel="mtx3"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="mtx4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="mtx5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="mtx6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-seven"
                  data-channel="mtx7"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-eight"
                  data-channel="mtx8"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">MTX-MAIN</div>
            </a>
            <!-- screen 10 item -->
            <a
              href="/detail-light/dca01.html"
              id="channel-select-dca"
              class="screen-channels-item"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="dca1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="dca2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-three"
                  data-channel="dca3"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="dca4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="dca5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="dca6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-seven"
                  data-channel="dca7"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-eight"
                  data-channel="dca8"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="../assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">DCA 1-8</div>
            </a>
          </div>
          <!-- screen channels swap -->
          <!-- mixer wrapper container channels 1-8 -->

          <!-- mixer wrapper container DCA 1-8  end  -->
        </div>
        <!-- main  screen part  -->
      </div>

      <div class="below-main">
        <div class="top-scene-part">
          <div class="show-name-bar">
            <div class="name-buttons">
              <div class="name-scene">
                <div id="show-name" class="show-name">SHOW NAME</div>
                <div id="scene-name" class="scene-name">DEMO SHOW</div>
              </div>
              <div class="buttons-scene">
                <div class="editable btn-block">EDIT</div>
                <div class="btn-block-scene param-safe">PARAM SAFE</div>
                <div class="btn-block-scene chain-safe">CHAIN SAFE</div>
              </div>
            </div>
            <div class="reverse-button">
              <div class="reverse">
                <img src="/assets/icons-scene/revert-scene-ticket.svg" />
              </div>
            </div>
          </div>
          <div class="drag-to-bar">
            <div id="new-ticket" class="new-ticket"></div>
            <div id="drag-ticket" class="drag-ticket">
              DRAG TO GO
              <img src="/assets/icons-scene/drag-to-scene-ticket.svg" />
            </div>
            <div id="current-ticket" class="current-ticket">
              <div class="vertical-letters">CURRENT</div>
              <img src="/assets/icons-scene/ticket-light.png" />
            </div>
          </div>
        </div>
        <div class="bottom-scene-part">
          <div id="heading-row-list" class="heading-row accordion-row">
            <div class="numbered">#</div>
            <div class="named">NAME</div>
            <div class="noted">NOTES</div>
            <div class="pins">
              <div class="pin-item">ROUT</div>
              <div class="pin-item">OUT</div>
              <div class="pin-item">HA</div>
              <div class="pin-item">CONF</div>
              <div class="pin-item">CHAN</div>
              <div class="pin-item">BUS</div>
              <div class="pin-item">FX</div>
              <div class="pin-item">TB</div>
            </div>
          </div>
          <!-- single accordion -->
          <div id="accordion-000" class="accordion-item-nav accordion-row">
            <div class="numbered">000</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins">
              <div class="pin-item">-</div>
              <div class="pin-item">-</div>
              <div class="pin-item">-</div>
              <div class="pin-item">-</div>
              <div class="pin-item">-</div>
              <div class="pin-item">-</div>
              <div class="pin-item">-</div>
              <div class="pin-item">-</div>
            </div>
          </div>
          <div id="accordion-000-content" class="accordion-item-content"></div>
          <!-- single accordion end 000 -->
          <!-- single accordion -->
          <div id="accordion-001" class="accordion-item-nav accordion-row">
            <div class="numbered">001</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-001" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-01" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-001" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-001" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-001" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-001" class="pinned"></div>
              </div>
              <div class="pin-item"><div id="fx-001" class="pinned"></div></div>
              <div class="pin-item"><div id="tb-001" class="pinned"></div></div>
            </div>
          </div>
          <div id="accordion-001-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-001" class="btn-block">SAVE</div>
              <div id="delete-001" class="btn-block">DELETE</div>
              <div id="copy-001" class="btn-block">COPY</div>
              <div id="paste-001" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 001 -->
          <!-- single accordion -->
          <div id="accordion-002" class="accordion-item-nav accordion-row">
            <div class="numbered">002</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-002" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-002" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-002" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-002" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-002" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-002" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-002" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-002" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-002-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-002" class="btn-block">SAVE</div>
              <div id="delete-002" class="btn-block">DELETE</div>
              <div id="copy-002" class="btn-block">COPY</div>
              <div id="paste-002" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 002 -->
          <!-- single accordion -->
          <div id="accordion-003" class="accordion-item-nav accordion-row">
            <div class="numbered">003</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-003" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-003" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-003" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-003" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-003" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-003" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-003" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-003" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-003-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-003" class="btn-block">SAVE</div>
              <div id="delete-003" class="btn-block">DELETE</div>
              <div id="copy-003" class="btn-block">COPY</div>
              <div id="paste-003" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 003 -->
          <!-- single accordion -->
          <div id="accordion-004" class="accordion-item-nav accordion-row">
            <div class="numbered">004</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-004" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-004" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-004" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-004" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-004" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-004" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-004" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-004" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-004-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-004" class="btn-block">SAVE</div>
              <div id="delete-004" class="btn-block">DELETE</div>
              <div id="copy-004" class="btn-block">COPY</div>
              <div id="paste-004" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 004 -->
          <!-- single accordion -->
          <div id="accordion-005" class="accordion-item-nav accordion-row">
            <div class="numbered">005</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-005" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-005" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-005" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-005" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-005" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-005" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-005" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-005" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-005-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-005" class="btn-block">SAVE</div>
              <div id="delete-005" class="btn-block">DELETE</div>
              <div id="copy-005" class="btn-block">COPY</div>
              <div id="paste-005" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 005 -->
          <!-- single accordion -->
          <div id="accordion-006" class="accordion-item-nav accordion-row">
            <div class="numbered">006</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-006" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-006" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-006" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-006" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-006" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-006" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-006" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-006" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-006-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-006" class="btn-block">SAVE</div>
              <div id="delete-006" class="btn-block">DELETE</div>
              <div id="copy-006" class="btn-block">COPY</div>
              <div id="paste-006" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 006 -->
          <!-- single accordion -->
          <div id="accordion-007" class="accordion-item-nav accordion-row">
            <div class="numbered">007</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-007" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-007" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-007" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-007" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-007" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-007" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-007" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-007" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-007-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-007" class="btn-block">SAVE</div>
              <div id="delete-007" class="btn-block">DELETE</div>
              <div id="copy-007" class="btn-block">COPY</div>
              <div id="paste-007" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 007 -->
          <!-- single accordion -->
          <div id="accordion-008" class="accordion-item-nav accordion-row">
            <div class="numbered">008</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-008" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-008" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-008" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-008" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-008" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-008" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-008" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-008" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-008-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-008" class="btn-block">SAVE</div>
              <div id="delete-008" class="btn-block">DELETE</div>
              <div id="copy-008" class="btn-block">COPY</div>
              <div id="paste-008" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 008 -->
          <!-- single accordion -->
          <div id="accordion-009" class="accordion-item-nav accordion-row">
            <div class="numbered">009</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-009" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-009" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-009" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-009" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-009" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-009" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-009" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-009" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-009-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-009" class="btn-block">SAVE</div>
              <div id="delete-009" class="btn-block">DELETE</div>
              <div id="copy-009" class="btn-block">COPY</div>
              <div id="paste-009" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 009 -->
          <!-- single accordion -->
          <div id="accordion-010" class="accordion-item-nav accordion-row">
            <div class="numbered">010</div>
            <div class="named" contenteditable="true"></div>
            <div class="noted" contenteditable="true"></div>
            <div class="pins activate-pin">
              <div class="pin-item">
                <div id="rout-010" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="out-010" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="ha-010" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="conf-010" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="chan-010" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="bus-010" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="fx-010" class="pinned"></div>
              </div>
              <div class="pin-item">
                <div id="tb-010" class="pinned"></div>
              </div>
            </div>
          </div>
          <div id="accordion-010-content" class="accordion-item-content">
            <div class="buttons-scene accordion-opened">
              <div id="save-010" class="btn-block">SAVE</div>
              <div id="delete-010" class="btn-block">DELETE</div>
              <div id="copy-010" class="btn-block">COPY</div>
              <div id="paste-010" class="btn-block">PASTE</div>
            </div>
          </div>
          <!-- single accordion end 010 -->
        </div>

        <!-- param safe -->
        <div class="param-safe-wrapper">
          <div class="param-safe-flex">
            <div class="param-safe-input">
              <div class="param-safe-input_title">INPUT CHANNELS</div>
              <div class="param-safe-input_items_three_col">
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-preamp" class="btn-block"></div>
                    <div>Preamp (HA)</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-config" class="btn-block"></div>
                    <div>Config</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-eq" class="btn-block"></div>
                    <div>EQ</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-gate" class="btn-block"></div>
                    <div>Gate & Comp</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-insert" class="btn-block"></div>
                    <div>Insert</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-groups" class="btn-block"></div>
                    <div>Groups</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-fader" class="btn-block"></div>
                    <div>Fader, Pan</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mute" class="btn-block"></div>
                    <div>Mute</div>
                  </div>
                  <!-- single item end -->
                </div>
                <!-- 2nd column -->
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix1" class="btn-block"></div>
                    <div>Mix 1 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix2" class="btn-block"></div>
                    <div>Mix 2 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix3" class="btn-block"></div>
                    <div>Mix 3 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix4" class="btn-block"></div>
                    <div>Mix 4 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix5" class="btn-block"></div>
                    <div>Mix 5 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix6" class="btn-block"></div>
                    <div>Mix 6 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix7" class="btn-block"></div>
                    <div>Mix 7 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix8" class="btn-block"></div>
                    <div>Mix 8 sends</div>
                  </div>
                  <!-- single item end -->
                </div>
                <!-- third column -->
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix9" class="btn-block"></div>
                    <div>Mix 9 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix10" class="btn-block"></div>
                    <div>Mix 10 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix11" class="btn-block"></div>
                    <div>Mix 11 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix12" class="btn-block"></div>
                    <div>Mix 12 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix13" class="btn-block"></div>
                    <div>Mix 13 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix14" class="btn-block"></div>
                    <div>Mix 14 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix15" class="btn-block"></div>
                    <div>Mix 15 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix16" class="btn-block"></div>
                    <div>Mix 16 sends</div>
                  </div>
                  <!-- single item end -->
                </div>
              </div>
            </div>
            <div class="param-safe-buses">
              <div class="param-safe-input_title">MIX BUSES</div>
              <div class="param-safe-input_items">
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-mix-matrix" class="btn-block"></div>
                  <div>Matrix Sends</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-mix-config" class="btn-block"></div>
                  <div>Config</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-mix-eq" class="btn-block"></div>
                  <div>EQ</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-mix-gate" class="btn-block"></div>
                  <div>Gate & Comp</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-mix-insert" class="btn-block"></div>
                  <div>Insert</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-mix-groups" class="btn-block"></div>
                  <div>Groups</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-mix-fader" class="btn-block"></div>
                  <div>Fader, Pan</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-mix-mute" class="btn-block"></div>
                  <div>Mute</div>
                </div>
                <!-- single item end -->
              </div>
            </div>
            <div class="param-safe-console">
              <div class="param-safe-input_title">CONSOLE</div>
              <div class="param-safe-input_items">
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-console-config" class="btn-block"></div>
                  <div>Configuration</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-console-solo" class="btn-block"></div>
                  <div>Solo</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-console-routing" class="btn-block"></div>
                  <div>Routing</div>
                </div>
                <!-- single item end -->
                <!-- single item -->
                <div class="single_param_item">
                  <div id="param-console-outpatch" class="btn-block"></div>
                  <div>Out Patch</div>
                </div>
                <!-- single item end -->
              </div>
            </div>
          </div>
        </div>
        <!-- param safe end -->

        <!-- chain safe -->
        <div class="chain-safe-wrapper">
          <div class="param-safe-flex">
            <div class="param-safe-input">
              <div class="param-safe-input_title">CHANNELS</div>
              <div class="param-safe-input_items_three_col">
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-preamp" class="btn-block"></div>
                    <div>Preamp (HA)</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-config" class="btn-block"></div>
                    <div>Config</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-eq" class="btn-block"></div>
                    <div>EQ</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-gate" class="btn-block"></div>
                    <div>Gate & Comp</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-insert" class="btn-block"></div>
                    <div>Insert</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-groups" class="btn-block"></div>
                    <div>Groups</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-fader" class="btn-block"></div>
                    <div>Fader, Pan</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mute" class="btn-block"></div>
                    <div>Mute</div>
                  </div>
                  <!-- single item end -->
                </div>
                <!-- 2nd column -->
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix1" class="btn-block"></div>
                    <div>Mix 1 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix2" class="btn-block"></div>
                    <div>Mix 2 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix3" class="btn-block"></div>
                    <div>Mix 3 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix4" class="btn-block"></div>
                    <div>Mix 4 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix5" class="btn-block"></div>
                    <div>Mix 5 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix6" class="btn-block"></div>
                    <div>Mix 6 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix7" class="btn-block"></div>
                    <div>Mix 7 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix8" class="btn-block"></div>
                    <div>Mix 8 sends</div>
                  </div>
                  <!-- single item end -->
                </div>
                <!-- third column -->
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix9" class="btn-block"></div>
                    <div>Mix 9 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix10" class="btn-block"></div>
                    <div>Mix 10 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix11" class="btn-block"></div>
                    <div>Mix 11 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix12" class="btn-block"></div>
                    <div>Mix 12 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix13" class="btn-block"></div>
                    <div>Mix 13 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix14" class="btn-block"></div>
                    <div>Mix 14 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix15" class="btn-block"></div>
                    <div>Mix 15 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix16" class="btn-block"></div>
                    <div>Mix 16 sends</div>
                  </div>
                  <!-- single item end -->
                </div>
              </div>
            </div>
            <div class="param-safe-input">
              <div class="param-safe-input_title">RETURNS</div>
              <div class="param-safe-input_items_three_col">
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-preamp" class="btn-block"></div>
                    <div>Preamp (HA)</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-config" class="btn-block"></div>
                    <div>Config</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-eq" class="btn-block"></div>
                    <div>EQ</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-gate" class="btn-block"></div>
                    <div>Gate & Comp</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-insert" class="btn-block"></div>
                    <div>Insert</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-groups" class="btn-block"></div>
                    <div>Groups</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-fader" class="btn-block"></div>
                    <div>Fader, Pan</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mute" class="btn-block"></div>
                    <div>Mute</div>
                  </div>
                  <!-- single item end -->
                </div>
                <!-- 2nd column -->
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix1" class="btn-block"></div>
                    <div>Mix 1 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix2" class="btn-block"></div>
                    <div>Mix 2 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix3" class="btn-block"></div>
                    <div>Mix 3 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix4" class="btn-block"></div>
                    <div>Mix 4 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix5" class="btn-block"></div>
                    <div>Mix 5 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix6" class="btn-block"></div>
                    <div>Mix 6 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix7" class="btn-block"></div>
                    <div>Mix 7 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix8" class="btn-block"></div>
                    <div>Mix 8 sends</div>
                  </div>
                  <!-- single item end -->
                </div>
                <!-- third column -->
                <div class="param-safe-input_items">
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix9" class="btn-block"></div>
                    <div>Mix 9 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix10" class="btn-block"></div>
                    <div>Mix 10 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix11" class="btn-block"></div>
                    <div>Mix 11 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix12" class="btn-block"></div>
                    <div>Mix 12 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix13" class="btn-block"></div>
                    <div>Mix 13 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix14" class="btn-block"></div>
                    <div>Mix 14 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix15" class="btn-block"></div>
                    <div>Mix 15 sends</div>
                  </div>
                  <!-- single item end -->
                  <!-- single item -->
                  <div class="single_param_item">
                    <div id="param-mix16" class="btn-block"></div>
                    <div>Mix 16 sends</div>
                  </div>
                  <!-- single item end -->
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- chain safe end -->
      </div>

      <!--  popup -->
      <div id="popup-selection" class="popup-selection">
        <div class="popup-content">
          <div class="column-pop theme-toggle-pop">
            <span>Light mode</span>
            <a href="/" class="toggle-modes" onclick="toggleTheme()">
              <div class="circle"></div>
            </a>
            <span>Dark mode</span>
          </div>
          <div
            id="app-window"
            class="column-pop preview-pop preview-window selected"
          >
            <img src="assets/icons-popups/screen-preview.png" alt="preview" />
          </div>
          <div id="video-window" class="column-pop purple-bg preview-window">
            <img src="assets/icons-popups/video.svg" alt="video" />
          </div>
          <div
            id="screen-share"
            class="column-pop light-purple-bg preview-window"
          >
            <img src="assets/icons-popups/screen.svg" alt="screen" />
          </div>
          <div id="chat-window" class="column-pop chat-pop">
            <img src="assets/icons-popups/chat.svg" alt="chat" />
          </div>
          <button id="close-pop-btn" class="close-pop-btn">
            <img src="assets/icons-popups/x-close.svg" alt="xlose" />
          </button>
        </div>
      </div>
      <!-- end popup -->
    </div>
    <!-- js -->
    <script src="assets/script.js"></script>
    <script src="assets/backend.js"></script>
    <script src="assets/script-animations.js"></script>
  </body>
</html>
