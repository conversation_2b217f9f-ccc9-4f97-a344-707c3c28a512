#
# This file is autogenerated by pip-compile with Python 3.11
# by the following command:
#
#    pip-compile requirements.in
#
bidict==0.23.1
    # via python-socketio
blinker==1.9.0
    # via flask
boto3==1.37.2
    # via -r requirements.in
botocore==1.37.2
    # via
    #   boto3
    #   s3transfer
cffi==1.17.1
    # via cryptography
click==8.1.8
    # via flask
colorama==0.4.6
    # via click
cryptography==44.0.1
    # via pyopenssl
dnspython==2.7.0
    # via eventlet
eventlet==0.39.0
    # via -r requirements.in
flask==3.1.0
    # via
    #   -r requirements.in
    #   flask-socketio
flask-socketio==5.5.1
    # via -r requirements.in
greenlet==3.1.1
    # via eventlet
h11==0.14.0
    # via wsproto
itsdangerous==2.2.0
    # via flask
jinja2==3.1.5
    # via flask
jmespath==1.0.1
    # via
    #   boto3
    #   botocore
markupsafe==3.0.2
    # via
    #   jinja2
    #   werkzeug
pycparser==2.22
    # via cffi
pyopenssl==25.0.0
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via botocore
python-engineio==4.11.2
    # via python-socketio
python-osc==1.9.3
    # via xair-api
python-socketio==5.12.1
    # via flask-socketio
s3transfer==0.11.3
    # via boto3
simple-websocket==1.1.0
    # via python-engineio
six==1.17.0
    # via python-dateutil
typing-extensions==4.12.2
    # via pyopenssl
urllib3==2.3.0
    # via botocore
werkzeug==3.1.3
    # via flask
wsproto==1.2.0
    # via simple-websocket
xair-api==2.4.1
    # via -r requirements.in