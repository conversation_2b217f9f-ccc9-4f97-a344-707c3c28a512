#!/bin/bash

# Local Kill Everything Script for Fresh AWS Account
# Run this manually to destroy everything except default VPC

echo "💀 KILL EVERYTHING - FRESH AWS ACCOUNT"
echo "======================================"
echo ""
echo "⚠️  This will destroy ALL resources in your AWS account!"
echo "⚠️  Only the default VPC will be preserved."
echo ""

# Ask for confirmation
read -p "Are you sure you want to destroy everything? (type 'KILL' to confirm): " confirmation

if [ "$confirmation" != "KILL" ]; then
    echo "❌ Cleanup cancelled."
    exit 1
fi

echo ""
echo "💀 Starting kill everything cleanup..."

# Make the script executable and run it
chmod +x .github/scripts/kill-everything.sh
./.github/scripts/kill-everything.sh

echo ""
echo "✅ Kill everything completed!"
echo ""
echo "🚀 Next steps:"
echo "1. Check the final verification output above to see what VPCs remain"
echo "2. If VPCs are still there, check the debug output for dependencies"
echo "3. Push your changes to trigger GitHub Actions deployment:"
echo "   git add . && git commit -m 'Enhanced VPC cleanup' && git push origin main"
echo "4. Or run Terraform manually after successful cleanup:"
echo "   cd terraform/environments/beta/ecr && terraform init && terraform apply"
echo "   cd ../ecs && terraform init && terraform apply"
