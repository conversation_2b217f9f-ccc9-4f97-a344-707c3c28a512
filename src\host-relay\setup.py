#!/usr/bin/env python3
"""
Setup script for VSE Host Relay

This script provides easy installation and setup for the VSE Host Relay application.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path


def check_python_version():
    """Check if Python version is compatible"""
    if sys.version_info < (3, 7):
        print("Error: Python 3.7 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    
    print(f"✓ Python {sys.version.split()[0]} detected")
    return True


def install_dependencies():
    """Install required dependencies"""
    print("Installing dependencies...")
    
    requirements_file = Path(__file__).parent / "requirements.txt"
    
    try:
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", "-r", str(requirements_file)
        ])
        print("✓ Dependencies installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ Failed to install dependencies: {e}")
        return False


def create_desktop_shortcut():
    """Create desktop shortcut (platform-specific)"""
    system = platform.system().lower()
    script_path = Path(__file__).parent / "vse_host_relay.py"
    
    if system == "windows":
        create_windows_shortcut(script_path)
    elif system == "darwin":
        create_macos_shortcut(script_path)
    elif system == "linux":
        create_linux_shortcut(script_path)


def create_windows_shortcut(script_path):
    """Create Windows desktop shortcut"""
    try:
        import winshell
        from win32com.client import Dispatch
        
        desktop = winshell.desktop()
        shortcut_path = os.path.join(desktop, "VSE Host Relay.lnk")
        
        shell = Dispatch('WScript.Shell')
        shortcut = shell.CreateShortCut(shortcut_path)
        shortcut.Targetpath = sys.executable
        shortcut.Arguments = f'"{script_path}"'
        shortcut.WorkingDirectory = str(script_path.parent)
        shortcut.IconLocation = sys.executable
        shortcut.save()
        
        print(f"✓ Desktop shortcut created: {shortcut_path}")
    except ImportError:
        print("! Windows shortcut creation requires pywin32 and winshell")
        print("  Install with: pip install pywin32 winshell")
    except Exception as e:
        print(f"! Failed to create Windows shortcut: {e}")


def create_macos_shortcut(script_path):
    """Create macOS desktop shortcut"""
    try:
        desktop = Path.home() / "Desktop"
        shortcut_path = desktop / "VSE Host Relay.command"
        
        shortcut_content = f'''#!/bin/bash
cd "{script_path.parent}"
python3 "{script_path}"
'''
        
        with open(shortcut_path, 'w') as f:
            f.write(shortcut_content)
        
        os.chmod(shortcut_path, 0o755)
        print(f"✓ Desktop shortcut created: {shortcut_path}")
    except Exception as e:
        print(f"! Failed to create macOS shortcut: {e}")


def create_linux_shortcut(script_path):
    """Create Linux desktop shortcut"""
    try:
        desktop = Path.home() / "Desktop"
        shortcut_path = desktop / "VSE Host Relay.desktop"
        
        shortcut_content = f'''[Desktop Entry]
Version=1.0
Type=Application
Name=VSE Host Relay
Comment=VSE Host Relay Application
Exec=python3 "{script_path}"
Icon=applications-multimedia
Path={script_path.parent}
Terminal=false
StartupNotify=true
'''
        
        with open(shortcut_path, 'w') as f:
            f.write(shortcut_content)
        
        os.chmod(shortcut_path, 0o755)
        print(f"✓ Desktop shortcut created: {shortcut_path}")
    except Exception as e:
        print(f"! Failed to create Linux shortcut: {e}")


def run_application():
    """Run the VSE Host Relay application"""
    script_path = Path(__file__).parent / "vse_host_relay.py"
    
    print("Starting VSE Host Relay...")
    try:
        subprocess.run([sys.executable, str(script_path)])
    except KeyboardInterrupt:
        print("\nApplication stopped by user")
    except Exception as e:
        print(f"Error running application: {e}")


def main():
    """Main setup process"""
    print("VSE Host Relay Setup")
    print("=" * 30)
    
    # Check Python version
    if not check_python_version():
        sys.exit(1)
    
    # Install dependencies
    if not install_dependencies():
        sys.exit(1)
    
    # Create desktop shortcut
    create_desktop_shortcut()
    
    print("\n" + "=" * 30)
    print("Setup completed successfully!")
    print("\nOptions:")
    print("1. Run now")
    print("2. Exit")
    
    try:
        choice = input("\nEnter choice (1-2): ").strip()
        
        if choice == "1":
            run_application()
        elif choice == "2":
            print("Setup complete. You can run the application anytime.")
        else:
            print("Invalid choice. Setup complete.")
            
    except KeyboardInterrupt:
        print("\nSetup interrupted by user")


if __name__ == "__main__":
    main()
