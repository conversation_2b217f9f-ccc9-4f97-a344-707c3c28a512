<svg width="59" height="91" viewBox="0 0 59 91" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_930_348795)">
<rect x="9" y="10" width="41" height="73" rx="3" fill="#F2EDE9"/>
<rect x="9.5" y="10.5" width="40" height="72" rx="2.5" stroke="#BAB8B8"/>
</g>
<line x1="16" y1="47" x2="44" y2="47" stroke="#2D2D2F" stroke-width="2"/>
<line x1="16" y1="43" x2="44" y2="43" stroke="#D8D2CD" stroke-width="2"/>
<line x1="16" y1="39" x2="44" y2="39" stroke="#D8D2CD" stroke-width="2"/>
<line x1="16" y1="51" x2="44" y2="51" stroke="#D8D2CD" stroke-width="2"/>
<line x1="16" y1="55" x2="44" y2="55" stroke="#D8D2CD" stroke-width="2"/>
<defs>
<filter id="filter0_d_930_348795" x="0.3" y="0.3" width="58.4" height="90.4" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feMorphology radius="3" operator="dilate" in="SourceAlpha" result="effect1_dropShadow_930_348795"/>
<feOffset dy="-1"/>
<feGaussianBlur stdDeviation="2.85"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.46 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_930_348795"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_930_348795" result="shape"/>
</filter>
</defs>
</svg>
