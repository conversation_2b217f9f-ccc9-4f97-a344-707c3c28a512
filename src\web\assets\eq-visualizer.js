/**
 * EQ Visualization Controller
 * Handles drawing and updating the EQ curve visualization
 */
class EQVisualizer {
  constructor(canvasSelector) {
    this.canvas = document.querySelector(canvasSelector);
    if (!this.canvas) {
      console.error("Canvas element not found:", canvasSelector);
      return;
    }
    
    this.ctx = this.canvas.getContext('2d');
    this.width = this.canvas.width;
    this.height = this.canvas.height;
    
    // Configuration
    this.minDb = -18;
    this.maxDb = 18;
    this.gridSize = 6;
    
    // Generate frequency array
    this.frequencies = EQMath.createFrequencyArray();
    
    // EQ bands with types and modes based on X32 capabilities
    this.bands = [
      { type: 'lowshelf', freq: 100, Q: 0.7, gain: 0, enabled: true, name: 'LOW' },
      { type: 'peak', mode: 'PEQ', freq: 300, Q: 1, gain: 0, enabled: true, name: 'LOW2' },
      { type: 'peak', mode: 'PEQ', freq: 500, Q: 1, gain: 0, enabled: true, name: 'LOMID' },
      { type: 'peak', mode: 'PEQ', freq: 2500, Q: 1, gain: 0, enabled: true, name: 'HIMID' },
      { type: 'peak', mode: 'PEQ', freq: 4000, Q: 1, gain: 0, enabled: true, name: 'HIGH2' },
      { type: 'highshelf', freq: 5000, Q: 0.7, gain: 0, enabled: true, name: 'HIGH' },
    ];
    
    // Low-cut (highpass) filter
    this.lowCut = {
      type: 'highpass',
      freq: 80, // Default frequency
      Q: 0.707,
      enabled: false,
    };
    
    // Store original values for reset
    this.bands.forEach(band => {
      band.originalType = band.type;
      band.originalMode = band.mode;
      band.originalFreq = band.freq;
      band.originalQ = band.Q;
      band.originalGain = band.gain;
    });
    this.lowCut.originalFreq = this.lowCut.freq;
    
    // Add a cache for response calculations
    this.responseCache = null;
    this.bandsDirty = true;

    this.draw();
  }

  calculateResponse() {
    // Check if we can use the cached response
    if (!this.bandsDirty && this.responseCache) {
      return this.responseCache;
    }
    
    const filterResponses = [];
    
    // Add low-cut filter response if enabled
    if (this.lowCut.enabled) {
      const response = EQMath.highPassFilter(this.frequencies, this.lowCut.freq, this.lowCut.Q);
      filterResponses.push(response);
    }
    
    for (const band of this.bands) {
      if (!band.enabled) continue;
      
      let response;
      switch (band.type) {
        case 'peak':
          let actualQ = band.Q;
          if (band.mode === 'VEQ') {
            actualQ = band.Q / 2.3; // Broader Q for Vintage EQ
          }
          response = EQMath.bellFilter(this.frequencies, band.freq, actualQ, band.gain);
          break;
        case 'lowshelf':
          response = EQMath.lowShelfFilter(this.frequencies, band.freq, band.Q, band.gain);
          break;
        case 'highshelf':
          response = EQMath.highShelfFilter(this.frequencies, band.freq, band.Q, band.gain);
          break;
        case 'highpass':
          response = EQMath.highPassFilter(this.frequencies, band.freq, band.Q);
          break;
        case 'lowpass':
          response = EQMath.lowPassFilter(this.frequencies, band.freq, band.Q);
          break;
      }
      
      filterResponses.push(response);
    }
    
    this.responseCache = filterResponses.length ? 
      EQMath.calculateCombinedResponse(this.frequencies, filterResponses) : 
      new Array(this.frequencies.length).fill(0);
    
    this.bandsDirty = false;
    return this.responseCache;
  }

  freqToX(freq) {
    const minLog = Math.log10(20);
    const maxLog = Math.log10(20000);
    const logPos = (Math.log10(freq) - minLog) / (maxLog - minLog);
    return logPos * this.width;
  }

  dbToY(db) {
    const normalizedDb = (db - this.minDb) / (this.maxDb - this.minDb);
    return this.height - (normalizedDb * this.height);
  }

  draw() {
    this.ctx.clearRect(0, 0, this.width, this.height);
    this.drawGrid();
    const response = this.calculateResponse();
    const zeroDbY = this.dbToY(0);
    this.drawAreaFill(response, zeroDbY);
    this.drawEQCurve(response, zeroDbY);
    this.drawBandMarkers();
    if (this.lowCut.enabled) this.drawLowCutLine();
  }

  drawGrid() {
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    this.ctx.lineWidth = 1;
    
    for (let i = 0; i <= this.gridSize; i++) {
      const db = this.minDb + (i / this.gridSize) * (this.maxDb - this.minDb);
      const y = this.dbToY(db);
      this.ctx.beginPath();
      this.ctx.moveTo(0, y);
      this.ctx.lineTo(this.width, y);
      this.ctx.stroke();
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
      this.ctx.font = '10px Arial';
      this.ctx.textAlign = 'left';
      this.ctx.fillText(`${Math.round(db)}dB`, 5, y + 12);
    }
    
    const freqMarkers = [20, 50, 100, 200, 500, 1000, 2000, 5000, 10000, 20000];
    for (const freq of freqMarkers) {
      const x = this.freqToX(freq);
      this.ctx.beginPath();
      this.ctx.moveTo(x, 0);
      this.ctx.lineTo(x, this.height);
      this.ctx.stroke();
      this.ctx.fillStyle = 'rgba(255, 255, 255, 0.5)';
      this.ctx.font = '10px Arial';
      this.ctx.textAlign = 'center';
      this.ctx.fillText(freq >= 1000 ? `${freq/1000}k` : freq.toString(), x, this.height - 5);
    }
    
    this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.3)';
    this.ctx.beginPath();
    this.ctx.moveTo(0, this.dbToY(0));
    this.ctx.lineTo(this.width, this.dbToY(0));
    this.ctx.stroke();
  }

  drawAreaFill(response, zeroDbY) {
    this.ctx.fillStyle = this.fillColor || '#808080';
    this.ctx.globalAlpha = 0.3;
    this.ctx.beginPath();
    this.ctx.moveTo(0, zeroDbY);
    for (let i = 0; i < this.frequencies.length; i++) {
      const x = this.freqToX(this.frequencies[i]);
      const y = this.dbToY(response[i]);
      this.ctx.lineTo(x, y);
    }
    this.ctx.lineTo(this.width, zeroDbY);
    this.ctx.closePath();
    this.ctx.fill();
    this.ctx.globalAlpha = 1.0;
  }

  drawEQCurve(response, zeroDbY) {
    this.ctx.strokeStyle = '#FF9500';
    this.ctx.lineWidth = 2;
    this.ctx.beginPath();
    this.ctx.moveTo(0, zeroDbY);
    const firstX = this.freqToX(this.frequencies[0]);
    this.ctx.lineTo(firstX, zeroDbY);
    for (let i = 1; i < this.frequencies.length; i++) {
      const x = this.freqToX(this.frequencies[i]);
      const y = this.dbToY(response[i]);
      this.ctx.lineTo(x, y);
    }
    this.ctx.lineTo(this.width, zeroDbY);
    this.ctx.stroke();
  }

  drawBandMarkers() {
    const response = this.calculateResponse();
    this.ctx.fillStyle = '#FF9500';
    
    this.bands.forEach(band => {
      if (!band.enabled) return;
      const x = this.freqToX(band.freq);
      let closestIndex = 0;
      let minDistance = Number.MAX_VALUE;
      for (let i = 0; i < this.frequencies.length; i++) {
        const freqX = this.freqToX(this.frequencies[i]);
        const distance = Math.abs(freqX - x);
        if (distance < minDistance) {
          minDistance = distance;
          closestIndex = i;
        }
      }
      const y = this.dbToY(response[closestIndex]);
      this.ctx.beginPath();
      this.ctx.arc(x, y, 5, 0, Math.PI * 2);
      this.ctx.fill();
    });
  }

  drawLowCutLine() {
    const x = this.freqToX(this.lowCut.freq);
    this.ctx.lineWidth = 2;
    this.ctx.beginPath();
    this.ctx.moveTo(x, 0);
    this.ctx.lineTo(x, this.height);
    this.ctx.stroke();
  }

  updateBand(index, params) {
    if (index < 0 || index >= this.bands.length) return;
    const band = this.bands[index];
    if (params.type) {
      band.type = params.type;
      if (params.type === 'peak') {
        band.mode = params.mode || 'PEQ';
      } else {
        band.mode = null;
        // Reset Q for non-peak modes
        if (params.type === 'lowshelf' || params.type === 'highshelf') {
          band.Q = 0.7; // Default Q for shelving filters
        } else if (params.type === 'highpass' || params.type === 'lowpass') {
          band.Q = 0.707; // Default Q for highpass/lowpass filters
          band.gain = 0; // Reset gain for highpass/lowpass
        }
      }
    } else if (params.mode && band.type === 'peak') {
      band.mode = params.mode;
    }
    if (params.freq) band.freq = params.freq;
    if (params.Q) band.Q = params.Q;
    if (params.gain) band.gain = params.gain;
    // Ensure gain is 0 for highpass/lowpass even if params.gain is provided
    if (band.type === 'highpass' || band.type === 'lowpass') {
      band.gain = 0;
    }
    this.bandsDirty = true;
    this.draw();
  }

  updateLowCut(freq) {
    this.lowCut.freq = Math.max(20, Math.min(20000, freq)); // Clamp frequency
    this.bandsDirty = true;
    this.draw();
  }

  toggleLowCut(enabled) {
    this.lowCut.enabled = enabled;
    this.bandsDirty = true;
    this.draw();
  }

  setBandType(index, type) {
    if (this.bands[index]) {
      this.bands[index].type = type;
      if (type === 'peak') {
        this.bands[index].mode = this.bands[index].mode || 'PEQ';
      } else {
        this.bands[index].mode = null;
        // Reset Q for non-peak modes
        if (type === 'lowshelf' || type === 'highshelf') {
          this.bands[index].Q = 0.7; // Default Q for shelving filters
        } else if (type === 'highpass' || type === 'lowpass') {
          this.bands[index].Q = 0.707; // Default Q for highpass/lowpass filters
          this.bands[index].gain = 0; // Reset gain for highpass/lowpass
        }
      }
      this.bandsDirty = true;
      this.draw();
    }
  }

  setBandMode(index, mode) {
    if (this.bands[index] && this.bands[index].type === 'peak') {
      this.bands[index].mode = mode;
      this.bandsDirty = true;
      this.draw();
    }
  }

  setBandEnabled(index, enabled) {
    if (index < 0 || index >= this.bands.length) return;
    this.bands[index].enabled = enabled;
    this.bandsDirty = true;
    this.draw();
  }

  reset() {
    this.bands.forEach(band => {
      band.type = band.originalType;
      band.mode = band.originalMode;
      band.freq = band.originalFreq;
      band.Q = band.originalQ;
      band.gain = band.originalGain;
    });
    this.lowCut.freq = this.lowCut.originalFreq;
    this.lowCut.enabled = false;
    this.bandsDirty = true;
    this.draw();
  }

  resize(width, height) {
    this.canvas.width = width;
    this.canvas.height = height;
    this.width = width;
    this.height = height;
    this.bandsDirty = true;
    this.draw();
  }

  setFillColor(color) {
    this.fillColor = color;
  }
}

window.EQVisualizer = EQVisualizer;