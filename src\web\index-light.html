<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Virtual Sound Engineer</title>
    <link rel="shortcut icon" href="assets/fav/fav-vse.png" />
    <link rel="stylesheet" href="assets/style/main.css" />
    <link rel="stylesheet" href="assets/style/index-light.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
      rel="stylesheet"
    />
    <!-- Include jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include jQuery UI via CDN -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Include touch-punch library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
  </head>
  <body id="home-page" class="home light-theme">
    <div id="preloader">
      <img id="preloader-logo" src="" alt="Loading..." />
    </div>
    <div class="page-wrapper">
      <div class="container main-flex">
        <!-- sidebar -->
        <div class="left-sidebar">
          <div class="sidebar-item logo">
            <a class="logo-btn" id="main-logo-btn" href="#"
              ><img src="assets/icons-lightmode/light-logo.svg" alt="logo"
            /></a>
          </div>
          <div class="sidebar-item controls">
            <div
              id="info"
              class="btn-block controls-item active-btn-background active-btn-shadow"
            >
              <img src="assets/icons/i.svg" alt="i" />
            </div>
            <div id="mute" class="btn-block controls-item">
              <img src="assets/icons/mute.svg" alt="mute" />
            </div>
            <div id="wifi" class="btn-block controls-item">
              <img src="assets/icons/wifi.svg" alt="wifi" />
            </div>
          </div>
          <div id="addons" class="sidebar-item addons">
            <div
              id="user"
              class="btn-block addons-item active-btn-background active-btn-shadow"
            >
              <img src="assets/icons/user-icon.svg" alt="user" />
            </div>
            <div id="screen" class="btn-block addons-item">
              <img src="assets/icons/screen-icon.svg" alt="screen" />
            </div>
            <div id="chat" class="btn-block addons-item">
              <img src="assets/icons/chat-icon.svg" alt="chat" />
            </div>
            <div id="video" class="btn-block addons-item">
              <img src="assets/icons/video-icon.svg" alt="video" />
            </div>
          </div>
          <div id="faders" class="btn-block sidebar-item faders">
            <p>Sends on Faders</p>
          </div>
          <div class="activated-faders">
            <img src="assets/icons/arrow-faders-x2.svg" alt="arrow" />
            <img src="assets/icons/arrow-faders-x2.svg" alt="arrow" />
            <img src="assets/icons/arrow-faders-x2.svg" alt="arrow" />
          </div>
          <div class="sidebar-item mixing">
            <p>Auto Mixing</p>
            <div id="auto-x" class="btn-block mixing-item">
              <span>X</span>
            </div>
            <div id="auto-y" class="btn-block mixing-item">
              <span>Y</span>
            </div>
          </div>
          <div class="sidebar-item mute-group">
            <p>Mute Groups</p>
            <div id="mute-one" class="btn-block mute-group-item muted">
              <span>1</span>
            </div>
            <div id="mute-two" class="btn-block mute-group-item muted">
              <span>2</span>
            </div>
            <div id="mute-three" class="btn-block mute-group-item muted">
              <span>3</span>
            </div>
            <div id="mute-four" class="btn-block mute-group-item muted">
              <span>4</span>
            </div>
            <div id="mute-five" class="btn-block mute-group-item muted">
              <span>5</span>
            </div>
            <div id="mute-six" class="btn-block mute-group-item muted">
              <span>6</span>
            </div>
            <div id="mute-edit" class="btn-block mute-group-item last-mute">
              edit
            </div>
          </div>
          <div class="sidebar-item show-solos">
            <p>show solos</p>
            <div class="show-solos-item-wrap">
              <div
                id="solo-on"
                class="show-solos-item active-btn-background active-btn-shadow"
              >
                <span>on</span>
              </div>
              <div id="solo-off" class="show-solos-item">
                <span>off</span>
              </div>
            </div>
          </div>
          <div id="mute-locks" class="btn-block sidebar-item lock-mute">
            <p>lock mutes</p>
          </div>
        </div>
        <!-- sidebar end -->
        <!-- main  screen part  -->
        <div class="main">
          <!-- navbar -->
          <div class="main-item navbar">
            <a href="/index-light.html"
              ><div class="navbar-item-img active_item">
                <img src="assets/icons/home-icon.svg" alt="home" />
              </div>
              <span>home</span></a
            >
            <a href="/detail-light/ch01.html"
              ><div class="navbar-item-img">
                <img src="assets/icons/detail-page-icon.svg" alt="detail" />
              </div>
              <span>detail</span></a
            >
            <a href="/effects-light.html"
              ><div class="navbar-item-img">
                <img src="assets/icons/effects-page-icon.svg" alt="effects" />
              </div>
              <span>effects</span></a
            >
            <a href="/scenes-light.html"
              ><div class="navbar-item-img scenes">
                <img src="assets/icons/scenes-page-icon.svg" alt="scenes" />
              </div>
              <span>scenes</span></a
            >
            <a href="/meters-light.html"
              ><div class="navbar-item-img meters">
                <img src="assets/icons/meters-page-icon.svg" alt="meters" />
              </div>
              <span>meters</span></a
            >
            <a href="/routing-light.html"
              ><div class="navbar-item-img">
                <img src="assets/icons/routing-page-icon.svg" alt="routing" />
              </div>
              <span>routing</span></a
            >
            <a href="/login.html"
              ><div class="navbar-item-img">
                <img src="assets/icons/log-out-icon.svg" alt="log-out" />
              </div>
              <span>logout</span></a
            >
          </div>
          <!-- navbar end -->
          <!-- screen channels swap -->
          <div class="main-item screen-channels-wrapper">
            <div
              id="channel-select-1-8"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="3">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="7">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="8">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">CH 1-8</div>
            </div>
            <!-- item 2 screens -->
            <div
              id="channel-select-9-16"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="9">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="10">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="11">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="12">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="13">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="14">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="15">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="18">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">CH 9-16</div>
            </div>
            <!-- item 3 screens -->
            <div
              id="channel-select-17-24"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="17">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="18">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="19">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="20">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="21">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="22">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="23">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="24">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">CH 17-24</div>
            </div>
            <!-- item 4 screens -->
            <div
              id="channel-select-25-32"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="25">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="26">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="27">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="28">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="29">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="30">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="31">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="32">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">CH 25-32</div>
            </div>
            <!-- item 5 screens -->
            <div
              id="channel-select-aux"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="a1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="a2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="a3">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="a4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="a5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="a6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="a7">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="a8">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">AUX 1-8</div>
            </div>
            <!-- items 6 screens -->
            <div
              id="channel-select-fx"
              class="screen-channels-item yes-fader-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="fx1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="fx2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="fx3">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="fx4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="fx5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="fx6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="fx7">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="fx8">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">FX 1L-4R</div>
            </div>
            <!-- item 7 screens -->
            <div
              id="channel-select-bus18"
              class="screen-channels-item no-faders-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="b1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="b2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="b3">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="b4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="b5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="b6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="b7">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="b8">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">BUS 1-8</div>
            </div>
            <!-- item 8 screens -->
            <div
              id="channel-select-bus916"
              class="screen-channels-item no-faders-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="b9">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="b10">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-three" data-channel="b11">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="b12">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="b13">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="b14">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-seven" data-channel="b15">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-eight" data-channel="b16">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">BUS 9-16</div>
            </div>
            <!-- screen 9 item -->
            <div
              id="channel-select-mtx"
              class="screen-channels-item no-faders-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="mtx1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="mtx2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-three"
                  data-channel="mtx3"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="mtx4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="mtx5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="mtx6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-seven"
                  data-channel="mtx7"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-eight"
                  data-channel="mtx8"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">MTX-MAIN</div>
            </div>
            <!-- screen 10 item -->
            <div
              id="channel-select-dca"
              class="screen-channels-item no-faders-tab"
            >
              <div class="equalizer-screens">
                <div class="equalizer-board equalizer-one" data-channel="dca1">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-one"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-two" data-channel="dca2">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-two"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-three"
                  data-channel="dca3"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-three"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-four" data-channel="dca4">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-four"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-five" data-channel="dca5">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-five"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div class="equalizer-board equalizer-six" data-channel="dca6">
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-six"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-seven"
                  data-channel="dca7"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-seven"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
                <div
                  class="equalizer-board equalizer-eight"
                  data-channel="dca8"
                >
                  <div class="overlay"></div>
                  <img
                    class="equalizer-border equalizer-channel-eight"
                    src="assets/icons/equalizer-x2.svg"
                    alt="equalizer"
                  />
                </div>
              </div>

              <div class="bottom-screen-info">DCA 1-8</div>
            </div>
          </div>
          <!-- screen channels swap -->
          <!-- mixer wrapper container channels 1-8 -->
          <div
            id="channel-1-8"
            class="main-item draggers channel-one-tab tab-active"
          >
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="1">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="detail-light/ch01.html"
                class="channel-screen channel-screen-one"
                >CH 01
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-1" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-1" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">01</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch1"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="1">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-1" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-1" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="2">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/ch02.html"
                class="channel-screen channel-screen-two"
              >
                CH 02
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-2" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-2" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">02</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch2"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="2">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-2" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-2" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="3">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch03.html"
                class="channel-screen channel-screen-three"
              >
                CH 03
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-3" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-3" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">03</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch3"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-three" data-channel="3">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-3" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-3" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="4">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch04.html"
                class="channel-screen channel-screen-four"
              >
                CH 04
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-4" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-4" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">04</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch4"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-four" data-channel="4">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-4" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-4" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="5">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch05.html"
                class="channel-screen channel-screen-five"
              >
                CH 05
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-5" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-5" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">05</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch5"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-five" data-channel="5">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-5" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-5" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="6">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch06.html"
                class="channel-screen channel-screen-six"
              >
                CH 06
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-6" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-6" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">06</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch6"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="6">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-6" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-6" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="7">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch07.html"
                class="channel-screen channel-screen-seven"
              >
                CH 07
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-7" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-7" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">07</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch7"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-seven" data-channel="7">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-7" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-7" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="8">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch08.html"
                class="channel-screen channel-screen-eight"
              >
                CH 08
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-8" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-8" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">08</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch8"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-eight" data-channel="8">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-8" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-8" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container end 1-8 -->

          <!-- mixer wrapper container channels 9-16 -->
          <div id="channel-9-16" class="main-item draggers channel-two-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="9">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch09.html"
                class="channel-screen channel-screen-one"
                >CH 09
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-9" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-9" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">09</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch9"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="9">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-9" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-9" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="10">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/ch10 "
                class="channel-screen channel-screen-two"
              >
                CH 10
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-10"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-10" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">10</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch10"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="10">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-10" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-10" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="11">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch11.html"
                class="channel-screen channel-screen-three"
              >
                CH 11
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-11"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-11" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">11</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch111"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-three" data-channel="11">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-11" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-11" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="12">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch12.html"
                class="channel-screen channel-screen-four"
              >
                CH 12
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-12"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-12" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">12</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch12"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-four" data-channel="12">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-12" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-12" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="13">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch13.html"
                class="channel-screen channel-screen-five"
              >
                CH 13
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-13"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-13" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">13</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch13"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-five" data-channel="13">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-13" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-13" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="14">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch14.html"
                class="channel-screen channel-screen-six"
              >
                CH 14
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-14"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-14" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">14</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch14"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="14">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-14" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-14" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="15">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch15.html"
                class="channel-screen channel-screen-seven"
              >
                CH 15
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-15"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-15" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">15</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch15"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-seven" data-channel="15">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-15" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-15" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="16">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch16.html"
                class="channel-screen channel-screen-eight"
              >
                CH 16
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-16"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-16" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">16</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch16"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-eight" data-channel="16">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-16" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-16" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container CH 9-16 -->

          <!-- mixer wrapper container channels 17-24 -->
          <div id="channel-17-24" class="main-item draggers channel-three-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="17">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch17.html"
                class="channel-screen channel-screen-one"
                >CH 17
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-17"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-17" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">17</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch17"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="17">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-17" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-17" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="18">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/ch18.html"
                class="channel-screen channel-screen-two"
              >
                CH 18
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-18"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-18" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">18</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch18"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="18">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-18" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-18" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="19">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch19.html"
                class="channel-screen channel-screen-three"
              >
                CH 19
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-19"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-19" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">19</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch19"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-three" data-channel="19">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-19" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-19" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="20">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch20.html"
                class="channel-screen channel-screen-four"
              >
                CH 20
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-20"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-20" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">20</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch20"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-four" data-channel="20">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-20" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-20" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="21">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch21.html"
                class="channel-screen channel-screen-five"
              >
                CH 21
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-21"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-21" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">21</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch21"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-five" data-channel="21">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-21" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-21" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="22">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch22.html"
                class="channel-screen channel-screen-six"
              >
                CH 22
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-22"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-22" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">22</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch22"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="22">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-22" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-22" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="23">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch23.html"
                class="channel-screen channel-screen-seven"
              >
                CH 23
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-23"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-23" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">15</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch23"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-seven" data-channel="23">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-23" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-23" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="24">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch24.html"
                class="channel-screen channel-screen-eight"
              >
                CH 24
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-24"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-24" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">24</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch24"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-eight" data-channel="24">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-24" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-24" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container CH 17-24 -->

          <!-- mixer wrapper container channels 25-32 -->
          <div id="channel-25-32" class="main-item draggers channel-three-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="25">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch25.html"
                class="channel-screen channel-screen-one"
                >CH 25
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-25"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-25" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">25</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch25"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="25">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-25" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-25" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="26">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/ch26.html"
                class="channel-screen channel-screen-two"
              >
                CH 26
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-26"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-26" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">26</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch26"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="26">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-26" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-26" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="27">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch27.html"
                class="channel-screen channel-screen-three"
              >
                CH 27
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-27"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-27" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">27</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch27"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-three" data-channel="27">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-27" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-27" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="28">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch28.html"
                class="channel-screen channel-screen-four"
              >
                CH 28
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-28"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-28" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">28</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch28"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-four" data-channel="28">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-28" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-28" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="29">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch29.html"
                class="channel-screen channel-screen-five"
              >
                CH 29
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-29"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-29" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">29</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch29"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-five" data-channel="29">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-29" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-29" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="30">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch30.html"
                class="channel-screen channel-screen-six"
              >
                CH 30
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-30"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-30" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">30</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch30"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="30">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-30" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-30" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="31">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch31.html"
                class="channel-screen channel-screen-seven"
              >
                CH 31
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-31"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-31" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">31</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch31"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-seven" data-channel="31">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-31" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-31" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="32">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/ch32.html"
                class="channel-screen channel-screen-eight"
              >
                CH 32
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-32"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-32" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">32</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-ch32"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-eight" data-channel="32">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-32" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-32" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container CH 25-32 -->

          <!-- mixer wrapper container channels AUX  1-8 -->
          <div id="channel-aux" class="main-item draggers channel-one-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="aux-1">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/aux01.html"
                class="channel-screen channel-screen-one"
                >AUX1
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-aux-1"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-aux-1" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">01</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-aux1"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="aux-1">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-aux-1" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-aux-1" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="aux-2">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/aux02.html"
                class="channel-screen channel-screen-two"
              >
                AUX2
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-aux-2"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-aux-2" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">02</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-aux2"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="aux-2">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-aux-2" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-aux-2" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="aux-3">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/aux03.html"
                class="channel-screen channel-screen-three"
              >
                AUX3
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-aux-3"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-aux-3" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">03</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-aux3"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-three"
                  data-channel="aux-3"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-aux-3" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-aux-3" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="aux-4">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/aux04.html"
                class="channel-screen channel-screen-four"
              >
                AUX4
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-aux-4"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-aux-4" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">04</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-aux4"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-four"
                  data-channel="aux-4"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-aux-4" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-aux-4" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="aux-5">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/aux05.html"
                class="channel-screen channel-screen-five"
              >
                AUX5
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-aux-5"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-aux-5" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">05</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-aux5"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-five"
                  data-channel="aux-5"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-aux-5" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-aux-5" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="aux-6">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/aux06.html"
                class="channel-screen channel-screen-six"
              >
                AUX6
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-aux-6"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-aux-6" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">06</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-aux6"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="aux-6">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-aux-6" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-aux-6" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="aux-7">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/aux07.html"
                class="channel-screen channel-screen-seven"
              >
                AUX7
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-aux-7"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-aux-7" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">07</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-aux7"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-seven"
                  data-channel="aux-7"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-aux-7" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-aux-7" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="aux-8">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/aux08.html"
                class="channel-screen channel-screen-eight"
              >
                AUX8
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-aux-8"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-aux-8" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">08</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-aux8"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-eight"
                  data-channel="aux-8"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-aux-8" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-aux-8" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container end aux 1-8 -->

          <!-- mixer wrapper container channels FX -->
          <div id="channel-fx" class="main-item draggers channel-one-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="fx-1">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/fx01.html"
                class="channel-screen channel-screen-one"
                >FX1L
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-fx-1"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-fx-1" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">1L</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-fx1"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="fx-1">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-fx-1" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-fx-1" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="fx-2">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/fx02.html"
                class="channel-screen channel-screen-two"
              >
                FX1R
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-fx-2"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-fx-2" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">1R</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-fx2"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="fx-2">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-fx-2" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-fx-2" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="fx-3">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/fx03.html"
                class="channel-screen channel-screen-three"
              >
                FX2L
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-fx-3"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-fx-3" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">2L</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-fx3"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-three"
                  data-channel="fx-3"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-fx-3" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-fx-3" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="fx-4">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/fx04.html"
                class="channel-screen channel-screen-four"
              >
                FX2R
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-fx-4"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-fx-4" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">2R</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-fx4"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-four" data-channel="fx-4">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-fx-4" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-fx-4" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="fx-5">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/fx05.html"
                class="channel-screen channel-screen-five"
              >
                FX3L
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-fx-5"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-fx-5" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">3L</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-fx5"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-five" data-channel="fx-5">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-fx-5" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-fx-5" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="fx-6">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/fx06.html"
                class="channel-screen channel-screen-six"
              >
                FX3R
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-fx-6"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-fx-6" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">3R</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-fx6"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="fx-6">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-fx-6" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-fx-6" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="fx-7">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/fx07.html"
                class="channel-screen channel-screen-seven"
              >
                FX4L
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-fx-7"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-fx-7" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">4L</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-fx7"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-seven"
                  data-channel="fx-7"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-fx-7" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-fx-7" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="fx-8">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/fx08.html"
                class="channel-screen channel-screen-eight"
              >
                FX4R
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-fx-8"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-fx-8" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">4R</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-fx8"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-eight"
                  data-channel="fx-8"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-fx-8" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-fx-8" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container end FX -->

          <!-- mixer wrapper container BUS 1-8 -->
          <div id="channel-bus18" class="main-item draggers channel-one-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="bus-1">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus01.html"
                class="channel-screen channel-screen-one"
                >BUS1
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-1"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-1" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">01</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus1"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="bus-1">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-1" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-1" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="bus-2">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/bus02.html"
                class="channel-screen channel-screen-two"
              >
                BUS2
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-2"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-2" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">02</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus2"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="bus-2">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-2" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-2" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="bus-3">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus03.html"
                class="channel-screen channel-screen-three"
              >
                BUS3
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-3"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-3" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">03</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus3"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-three"
                  data-channel="bus-3"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-3" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-3" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="bus-4">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus04.html"
                class="channel-screen channel-screen-four"
              >
                BUS4
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-4"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-4" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">04</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus4"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-four"
                  data-channel="bus-4"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-4" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-4" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="bus-5">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus05.html"
                class="channel-screen channel-screen-five"
              >
                BUS5
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-5" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-5" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">05</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus5"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-five"
                  data-channel="bus-5"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-5" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-5" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="bus-6">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus06.html"
                class="channel-screen channel-screen-six"
              >
                BUS6
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-6"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-6" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">06</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus6"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="bus-6">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-6" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-6" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="bus-7">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus07.html"
                class="channel-screen channel-screen-seven"
              >
                BUS7
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-7"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-7" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">07</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus7"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-seven"
                  data-channel="bus-7"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-7" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-7" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="bus-8">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus08.html"
                class="channel-screen channel-screen-eight"
              >
                BUS8
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-8"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-8" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">08</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus8"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-eight"
                  data-channel="bus-8"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-8" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-8" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container end BUS 1-8 -->

          <!-- mixer wrapper container BUS 9-16 -->
          <div id="channel-bus916" class="main-item draggers channel-two-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="bus-9">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus09.html"
                class="channel-screen channel-screen-one"
                >BUS9
              </a>
              <!-- needle controll left right -->
              <div id="needle-screen-9" class="needle-screen needle-screen-one">
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-9" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">09</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus9"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="bus-9">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-9" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-9" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="bus-10">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/bus10.html"
                class="channel-screen channel-screen-two"
              >
                BUS10
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-10"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-10" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">10</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus10"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-two"
                  data-channel="bus-10"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-10" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-10" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="bus-11">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus11.html"
                class="channel-screen channel-screen-three"
              >
                BUS11
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-11"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-11bus-" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">11</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus11"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-three"
                  data-channel="bus-11"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-11" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-11" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="bus-12">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus12.html"
                class="channel-screen channel-screen-four"
              >
                BUS12
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-12"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-12" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">12</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus12"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-four"
                  data-channel="bus-12"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-12" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-12" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="bus-13">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus13.html"
                class="channel-screen channel-screen-five"
              >
                BUS13
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-13"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-13" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">13</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus13"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-five"
                  data-channel="bus-13"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-13" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-13" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="bus-14">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus14.html"
                class="channel-screen channel-screen-six"
              >
                BUS14
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-14"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-14" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">14</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus14"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-six"
                  data-channel="bus-14"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-14" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-14" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="bus-15">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus15.html"
                class="channel-screen channel-screen-seven"
              >
                BUS15
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-bus-15"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-15" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">15</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus15"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-seven"
                  data-channel="bus-15"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-15" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-15" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="bus-16">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/bus16.html"
                class="channel-screen channel-screen-eight"
              >
                BUS16
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-16"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-bus-16" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">16</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-bus16"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-eight"
                  data-channel="bus-16"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-bus-16" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-bus-16" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container BUS 9-16 -->

          <!-- mixer wrapper container channels MTX 1-8 -->
          <div id="channel-mtx" class="main-item draggers channel-one-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="mtx-1">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/mtx01.html"
                class="channel-screen channel-screen-one"
                >MTX 1
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-mtx-1"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-mtx-1" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">01</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-mtx1"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="mtx-1">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-mtx-1" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-mtx-1" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="mtx-2">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/mtx02.html"
                class="channel-screen channel-screen-two"
              >
                MTX 2
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-mtx-2"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-mtx-2" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">02</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-mtx2"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="mtx-2">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-mtx-2" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-mtx-2" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="mtx-3">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/mtx03.html"
                class="channel-screen channel-screen-three"
              >
                MTX 3
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-mtx-3"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-mtx-3" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">03</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-mtx3"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-three"
                  data-channel="mtx-3"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-mtx-3" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-mtx-3" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="mtx-4">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/mtx04.html"
                class="channel-screen channel-screen-four"
              >
                MTX 4
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-mtx-4"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-mtx-4" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">04</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-mtx4"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-four"
                  data-channel="mtx-4"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-mtx-4" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-mtx-4" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="mtx-5">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/mtx05.html"
                class="channel-screen channel-screen-five"
              >
                MTX 5
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-mtx-5"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-mtx-5" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">05</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-mtx5"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-five"
                  data-channel="mtx-5"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-mtx-5" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-mtx-5" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="mtx-6">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/mtx06.html"
                class="channel-screen channel-screen-six"
              >
                MTX 6
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-mtx-6"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-mtx-6" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">06</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-mtx6"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="mtx-6">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-mtx-6" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-mtx-6" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="mtx-7">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/mtx07.html"
                class="channel-screen channel-screen-seven"
              >
                MAIN C
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-mtx-7"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-mtx-7" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">C</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-mtx7"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-seven"
                  data-channel="mtx-7"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-mtx-7" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-mtx-7" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="mtx-8">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/mtx08.html"
                class="channel-screen channel-screen-eight"
              >
                MAIN LR
              </a>
              <!-- needle controll left right -->
              <div
                id="needle-screen-mtx-8"
                class="needle-screen needle-screen-one"
              >
                <img
                  class="needle-path"
                  src="assets/icons/board-needle.svg"
                  alt="mixer"
                />
                <img
                  class="needle-dragger needle-dragger-one"
                  src="assets/icons/needle.svg"
                  alt="needle"
                />
              </div>
              <!-- needle controll left right end -->
              <div id="board-mtx-8" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">LR</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-mtx8"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-eight"
                  data-channel="mtx-8"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-mtx-8" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-mtx-8" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container MTX 1-8  end  -->

          <!-- mixer wrapper container channels DCA 1-8 -->
          <div id="channel-dca" class="main-item draggers channel-one-tab">
            <!-- single item 1 -->
            <div class="mixer-wrap channel-one" data-channel="dca-1">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>1</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>2</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/dca01.html"
                class="channel-screen channel-screen-one"
                >DCA 1
              </a>
              <!-- edit control left right -->
              <div class="edit-dca-wrap">
                <div class="btn-block edit-dca-item toggle-dca1">edit</div>
              </div>
              <!-- edit control left right end -->
              <div id="board-dca-1" class="mixer-container board-one">
                <div class="inner-container">
                  <div class="number-mixer">01</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-dca1"
                    class="mixer-dragger dragger-channel-one"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-one" data-channel="dca-1">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-one"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-dca-1" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-dca-1" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 2 -->
            <div class="mixer-wrap channel-two" data-channel="dca-2">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>3</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>4</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->

              <a
                href="/detail-light/dca02.html"
                class="channel-screen channel-screen-two"
              >
                DCA 2
              </a>
              <!-- edit control left right -->
              <div class="edit-dca-wrap">
                <div class="btn-block edit-dca-item toggle-dca2">edit</div>
              </div>
              <!-- edit control left right end -->
              <div id="board-dca-2" class="mixer-container board-two">
                <div class="inner-container">
                  <div class="number-mixer">02</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-dca2"
                    class="mixer-dragger dragger-channel-two"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-two" data-channel="dca-2">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-two"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-dca-2" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-dca-2" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 3 -->
            <div class="mixer-wrap channel-three" data-channel="dca-3">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>5</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>6</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/dca03.html"
                class="channel-screen channel-screen-three"
              >
                DCA 3
              </a>
              <!-- edit control left right -->
              <div class="edit-dca-wrap">
                <div class="btn-block edit-dca-item toggle-dca3">edit</div>
              </div>
              <!-- edit control left right end -->
              <div id="board-dca-3" class="mixer-container board-three">
                <div class="inner-container">
                  <div class="number-mixer">03</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-dca3"
                    class="mixer-dragger dragger-channel-three"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-three"
                  data-channel="dca-3"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-three"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-dca-3" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-dca-3" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 4 -->
            <div class="mixer-wrap channel-four" data-channel="dca-4">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>7</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>8</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/dca04.html"
                class="channel-screen channel-screen-four"
              >
                DCA 4
              </a>
              <!-- edit control left right -->
              <div class="edit-dca-wrap">
                <div class="btn-block edit-dca-item toggle-dca4">edit</div>
              </div>
              <!-- edit control left right end -->
              <div id="board-dca-4" class="mixer-container board-four">
                <div class="inner-container">
                  <div class="number-mixer">04</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-dca4"
                    class="mixer-dragger dragger-channel-four"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-four"
                  data-channel="dca-4"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-four"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-dca-4" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-dca-4" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 5 -->
            <div class="mixer-wrap channel-five" data-channel="dca-5">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>9</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>10</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/dca05.html"
                class="channel-screen channel-screen-five"
              >
                DCA 5
              </a>
              <!-- edit control left right -->
              <div class="edit-dca-wrap">
                <div class="btn-block edit-dca-item toggle-dca5">edit</div>
              </div>
              <!-- edit control left right end -->
              <div id="board-dca-5" class="mixer-container board-five">
                <div class="inner-container">
                  <div class="number-mixer">05</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-dca5"
                    class="mixer-dragger dragger-channel-five"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-five"
                  data-channel="dca-5"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-five"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-dca-5" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-dca-5" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 6 -->
            <div class="mixer-wrap channel-six" data-channel="dca-6">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>11</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>12</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/dca06.html"
                class="channel-screen channel-screen-six"
              >
                DCA 6
              </a>
              <!-- edit control left right -->
              <div class="edit-dca-wrap">
                <div class="btn-block edit-dca-item toggle-dca6">edit</div>
              </div>
              <!-- edit control left right end -->
              <div id="board-dca-6" class="mixer-container board-six">
                <div class="inner-container">
                  <div class="number-mixer">06</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-dca6"
                    class="mixer-dragger dragger-channel-six"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div class="volume-board board-volume-six" data-channel="dca-6">
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-six"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-dca-6" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-dca-6" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 7 -->
            <div class="mixer-wrap channel-seven" data-channel="dca-7">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>13</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>14</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/dca07.html"
                class="channel-screen channel-screen-seven"
              >
                DCA 7
              </a>
              <!-- edit control left right -->
              <div class="edit-dca-wrap">
                <div class="btn-block edit-dca-item toggle-dca7">edit</div>
              </div>
              <!-- edit control left right end -->
              <div id="board-dca-7" class="mixer-container board-seven">
                <div class="inner-container">
                  <div class="number-mixer">07</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-dca7"
                    class="mixer-dragger dragger-channel-seven"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-seven"
                  data-channel="dca-7"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-seven"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-dca-7" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-dca-7" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->

            <!-- single item 8 -->
            <div class="mixer-wrap channel-eight" data-channel="dca-8">
              <!-- send fader active -->
              <div class="send-faders-active-options">
                <div class="btn-block send-faders-active send-faders-one">
                  <span>15</span>
                </div>
                <div class="btn-block send-faders-active send-faders-two">
                  <span>16</span>
                </div>
              </div>
              <!-- send fader active end -->
              <!-- mixer -->
              <a
                href="/detail-light/dca08.html"
                class="channel-screen channel-screen-eight"
              >
                DCA 8
              </a>
              <!-- edit control left right -->
              <div class="edit-dca-wrap">
                <div class="btn-block edit-dca-item toggle-dca8">edit</div>
              </div>
              <!-- edit control left right end -->
              <div id="board-dca-8" class="mixer-container board-eight">
                <div class="inner-container">
                  <div class="number-mixer">08</div>
                  <img
                    class="mixer-board"
                    src="assets/icons-lightmode/light-mixer.svg"
                    alt="mixer"
                  />
                  <img
                    id="dragger-dca8"
                    class="mixer-dragger dragger-channel-eight"
                    src="assets/icons/dragger-x2.svg"
                    alt="mixer"
                  />
                </div>
                <div
                  class="volume-board board-volume-eight"
                  data-channel="dca-8"
                >
                  <div class="overlay"></div>
                  <img
                    class="mixer-volume volume-channel-eight"
                    src="assets/icons/volume-board-main.svg"
                    alt="mixer"
                  />
                </div>
              </div>

              <!-- mixer end -->
              <!-- buttons - mute and solo -->
              <div class="main-board-buttons mute-solo">
                <div id="mute-dca-8" class="btn-block mute-solo-item muted">
                  <span>Mute</span>
                </div>
                <div id="solo-dca-8" class="btn-block mute-solo-item soloed">
                  <span>Solo</span>
                </div>
              </div>
            </div>
            <!-- single item end -->
          </div>
          <!-- mixer wrapper container DCA 1-8  end  -->
        </div>
        <!-- main  screen part  -->
      </div>
    </div>
    <!--  popup -->
    <div id="popup-selection" class="popup-selection show">
      <div class="popup-content">
        <div id="profile-window" class="column-pop acc-pop">
          <img src="assets/icons-popups/account-swap.svg" alt="acc" />
        </div>
        <div
          id="app-window"
          class="column-pop preview-pop preview-window selected"
        >
          <img src="assets/icons-popups/screen-preview.png" alt="preview" />
        </div>
        <div id="video-window" class="column-pop purple-bg preview-window">
          <img src="assets/icons-popups/video-preview.png" alt="video" />
        </div>
        <div
          id="screen-share"
          class="column-pop light-purple-bg preview-window"
        >
          <img
            src="assets/icons-popups/screen-share-preview.png"
            alt="screen"
          />
        </div>
        <div id="chat-window" class="column-pop chat-pop">
          <img src="assets/icons-popups/chat.svg" alt="chat" />
        </div>
        <button id="close-pop-btn" class="close-pop-btn">
          <img src="assets/icons-popups/x-close.svg" alt="xlose" />
        </button>
      </div>
    </div>

    <button id="arrow-pop-btn" class="arrow-pop-btn">
      <img src="assets/icons-popups/arrow-up-toggle-bar.svg" alt="arropn" />
    </button>
    <!-- end popup -->
    <!-- session bar  -->
    <div id="popup-session-bar-video" class="popup-session-bar activated">
      <div class="popup-session-content">
        <div class="inner-bar">
          <div id="direct-room" class="column-pop theme-toggle-pop">
            <div class="direct_block">
              <div class="vol-direct vol-items">
                <span>Direct</span>
              </div>
              <div class="vol_mute_block">
                <div class="volume-display">
                  <div class="progress-container">
                    <div class="bar_controller">
                      <!-- <img src="assets/icons/knob_vol.svg" alt="knob" /> -->
                    </div>
                    <div class="progress-bar" style="width: 0%"></div>
                  </div>
                  <div class="percentage-text" style="left: 0%"></div>
                </div>
                <div id="vol-mute" class="btn_org">
                  <img
                    class="mute_vol"
                    src="assets/icons-popups/plus-vol.svg"
                    alt="vol"
                  />
                  <img
                    class="mute_vol muted_vol"
                    src="assets/icons-popups/mute-vol.svg"
                    alt="vol"
                  />
                </div>
              </div>
            </div>
            <div class="direct_block">
              <div class="vol-direct vol-items">
                <span>Room</span>
              </div>
              <div class="vol_mute_block">
                <div class="volume-display">
                  <div class="progress-container">
                    <div class="bar_controller">
                      <!-- <img src="assets/icons/knob_vol.svg" alt="knob" /> -->
                    </div>
                    <div class="progress-bar" style="width: 0%"></div>
                  </div>
                  <div class="percentage-text" style="left: 0%"></div>
                </div>
                <div id="vol-mute-room" class="btn_org">
                  <img
                    class="mute_vol"
                    src="assets/icons-popups/plus-vol.svg"
                    alt="vol"
                  />
                  <img
                    class="mute_vol muted_vol"
                    src="assets/icons-popups/mute-vol.svg"
                    alt="vol"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="session_flex">
            <div id="connect-board-id-a" class="btn_orange_full active_session">
              Session A
            </div>
            <div id="connect-board-id-b" class="btn_orange_full">Session B</div>
            <div id="connect-board-id-c" class="btn_orange_full">Session C</div>
            <div id="connect-board-id-d" class="btn_orange_full">Session D</div>
            <div id="create-session-btn" class="btn_orange_full create-session">
              Create Session
            </div>
          </div>
          <!-- Session Key Display -->
          <div
            id="session-info-display"
            class="session-info-display"
            style="
              display: none;
              padding: 4px 6px;
              text-align: center;
              background: rgba(255, 107, 53, 0.1);
              border-radius: 4px;
              margin: 4px auto;
              width: fit-content;
              max-width: 280px;
              box-sizing: border-box;
            "
          >
            <div
              class="session-key-info"
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 6px;
                white-space: nowrap;
              "
            >
              <span
                class="session-label"
                style="color: #000000; font-weight: bold; font-size: 12px"
                >Key:</span
              >
              <input
                id="current-session-key"
                class="session-key"
                readonly
                style="
                  color: #000000;
                  font-size: 16px;
                  font-weight: bold;
                  font-family: 'Courier New', monospace;
                  background: #ffffff;
                  padding: 4px 8px;
                  border-radius: 4px;
                  letter-spacing: 2px;
                  border: 2px solid #ff6b35;
                  text-align: center;
                  cursor: pointer;
                  user-select: all;
                  width: 100px;
                  min-width: 100px;
                  max-width: 100px;
                  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
                "
                value="------"
                title="Click to copy session key"
                onclick="copySessionKey()"
              />
              <span
                id="session-status"
                class="session-status"
                style="color: #000000; font-size: 11px; font-weight: bold"
                >Creating...</span
              >
            </div>
          </div>
          <div class="btn_account_wrap">
            <!-- <a href="#">Send session key</a> -->
            <a href="#">My Account</a>
          </div>
          <div class="column-pop theme-toggle-pop">
            <span>Light</span>
            <a href="index.html" class="toggle-modes" onclick="toggleTheme()">
              <div class="circle"></div>
            </a>
            <span>Dark</span>
          </div>
        </div>
      </div>
    </div>
    <!-- end session bar  -->
    <!-- popup video screen -->
    <div id="popup-session" class="popup-session">
      <button id="x-session-start" class="x-session-start">
        <img src="assets/icons-popups/x-close.svg" alt="xlose" />
      </button>
      <button id="x-session-minmax-1" class="x-session-minmax">
        <img src="assets/icons-popups/min-max.svg" alt="minmax" />
      </button>
      <div class="form-box">
        <div style="margin-bottom: 10%">
          <img
            src="assets/icons-popups/VSE-logo-light-big.svg"
            alt="logo-btn"
          />
        </div>
      </div>
    </div>
    <!-- end video screen -->
    <!-- popup video screen -->
    <div id="popup-share" class="popup-session">
      <button id="x-session-start-2" class="x-session-start">
        <img src="assets/icons-popups/x-close.svg" alt="xlose" />
      </button>
      <button id="x-session-minmax-2" class="x-session-minmax">
        <img src="assets/icons-popups/min-max.svg" alt="minmax" />
      </button>
      <div class="form-box">
        <div style="margin-bottom: 10%">
          <img
            src="assets/icons-popups/VSE-logo-light-big.svg"
            alt="logo-btn"
          />
        </div>
      </div>
    </div>
    <!-- end video screen -->
    <!-- popup login session -->
    <div id="popup-session-choice" class="popup-session popup-session-choice">
      <button id="x-session-start" class="x-session-start">
        <img src="assets/icons-popups/x-close.svg" alt="xlose" />
      </button>
      <div class="form-box">
        <h2 class="session-title"></h2>
        <p>Enter Session Key</p>
        <form
          action="#"
          id="verification-form"
          class="verification-form"
          method="GET"
        >
          <div class="verification-code">
            <input
              type="text"
              name="code1"
              maxlength="1"
              class="code-input"
              required
            />
            <input
              type="text"
              name="code2"
              maxlength="1"
              class="code-input"
              required
            />
            <input
              type="text"
              name="code3"
              maxlength="1"
              class="code-input"
              required
            />
            <input
              type="text"
              name="code4"
              maxlength="1"
              class="code-input"
              required
            />
            <input
              type="text"
              name="code5"
              maxlength="1"
              class="code-input"
              required
            />
            <input
              type="text"
              name="code6"
              maxlength="1"
              class="code-input"
              required
            />
          </div>
          <button type="submit">Join Live Session</button>
        </form>
      </div>
    </div>
    <!-- popup login end -->
    <!-- EDIT dca -->
    <!-- dca 1 -->
    <div id="dca1-pop" class="dca-pop-screen dca1-pop-screen">
      <div class="heading-dca">DCA 1</div>
      <div id="dca-1-row1" class="dca-edit-screen-flex">
        <div class="row-one-dca">
          <div class="channel-screen dca-ch1">CH 01</div>
          <div class="channel-screen dca-ch2">CH 02</div>
          <div class="channel-screen dca-ch3">CH 03</div>
          <div class="channel-screen dca-ch4">CH 04</div>
          <div class="channel-screen dca-ch5">CH 05</div>
          <div class="channel-screen dca-ch6">CH 06</div>
          <div class="channel-screen dca-ch7">CH 07</div>
          <div class="channel-screen dca-ch8">CH 08</div>
        </div>
        <div id="dca-1-row2" class="row-one-dca">
          <div class="channel-screen dca-ch09">CH 09</div>
          <div class="channel-screen dca-ch10">CH 10</div>
          <div class="channel-screen dca-">CH 11</div>
          <div class="channel-screen dca-ch12">CH 12</div>
          <div class="channel-screen dca-ch13">CH 13</div>
          <div class="channel-screen dca-ch14">CH 14</div>
          <div class="channel-screen dca-ch15">CH 15</div>
          <div class="channel-screen dca-ch16">CH 16</div>
        </div>
        <div id="dca-1-row3" class="row-one-dca">
          <div class="channel-screen dca-ch17">CH 17</div>
          <div class="channel-screen dca-ch18">CH 18</div>
          <div class="channel-screen dca-ch19">CH 19</div>
          <div class="channel-screen dca-ch20">CH 20</div>
          <div class="channel-screen dca-ch21">CH 21</div>
          <div class="channel-screen dca-ch22">CH 22</div>
          <div class="channel-screen dca-ch23">CH 23</div>
          <div class="channel-screen dca-ch24">CH 24</div>
        </div>
        <div id="dca-1-row4" class="row-one-dca">
          <div class="channel-screen dca-ch25">CH 25</div>
          <div class="channel-screen dca-ch26">CH 26</div>
          <div class="channel-screen dca-ch27">CH 27</div>
          <div class="channel-screen dca-ch28">CH 28</div>
          <div class="channel-screen dca-ch29">CH 29</div>
          <div class="channel-screen dca-ch30">CH 30</div>
          <div class="channel-screen dca-ch31">CH 31</div>
          <div class="channel-screen dca-ch32">CH 32</div>
        </div>
        <div id="dca-1-row5" class="row-one-dca aux-row">
          <div class="channel-screen aux-ch1">AUX 1</div>
          <div class="channel-screen aux-ch2">AUX 2</div>
          <div class="channel-screen aux-ch3">AUX 3</div>
          <div class="channel-screen aux-ch4">AUX 4</div>
          <div class="channel-screen aux-ch5">AUX 5</div>
          <div class="channel-screen aux-ch6">AUX 6</div>
          <div class="channel-screen aux-ch7">AUX 7</div>
          <div class="channel-screen aux-ch8">AUX 8</div>
        </div>
        <div id="dca-1-row6" class="row-one-dca fx-row">
          <div class="channel-screen fx-ch1">FX1L</div>
          <div class="channel-screen fx-ch2">FX1R</div>
          <div class="channel-screen fx-ch3">FX2L</div>
          <div class="channel-screen fx-ch4">FX2R</div>
          <div class="channel-screen fx-ch5">FX3L</div>
          <div class="channel-screen fx-ch6">FX3R</div>
          <div class="channel-screen fx-ch7">FX4L</div>
          <div class="channel-screen fx-ch8">FX4R</div>
        </div>
        <div id="dca-1-row7" class="row-one-dca bus18-row">
          <div class="channel-screen bus-ch1">BUS 1</div>
          <div class="channel-screen bus-ch2">BUS 2</div>
          <div class="channel-screen bus-ch3">BUS 3</div>
          <div class="channel-screen bus-ch4">BUS 4</div>
          <div class="channel-screen bus-ch5">BUS 5</div>
          <div class="channel-screen bus-ch6">BUS 6</div>
          <div class="channel-screen bus-ch7">BUS 7</div>
          <div class="channel-screen bus-ch8">BUS 8</div>
        </div>
        <div id="dca-1-row8" class="row-one-dca bus916-row">
          <div class="channel-screen bus-ch9">BUS 9</div>
          <div class="channel-screen bus-ch10">BUS 10</div>
          <div class="channel-screen bus-">BUS 11</div>
          <div class="channel-screen bus-ch12">BUS 12</div>
          <div class="channel-screen bus-ch13">BUS 13</div>
          <div class="channel-screen bus-ch14">BUS 14</div>
          <div class="channel-screen bus-ch15">BUS 15</div>
          <div class="channel-screen bus-ch16">BUS 16</div>
        </div>
      </div>
      <div id="close-dca1-btn" class="btn-block close">CLOSE</div>
    </div>
    <!-- dca 2 -->
    <div id="dca2-pop" class="dca-pop-screen dca2-pop-screen">
      <div class="heading-dca">DCA 2</div>
      <div id="dca-2-row1" class="dca-edit-screen-flex">
        <div class="row-one-dca">
          <div class="channel-screen dca-ch1">CH 01</div>
          <div class="channel-screen dca-ch2">CH 02</div>
          <div class="channel-screen dca-ch3">CH 03</div>
          <div class="channel-screen dca-ch4">CH 04</div>
          <div class="channel-screen dca-ch5">CH 05</div>
          <div class="channel-screen dca-ch6">CH 06</div>
          <div class="channel-screen dca-ch7">CH 07</div>
          <div class="channel-screen dca-ch8">CH 08</div>
        </div>
        <div id="dca-2-row2" class="row-one-dca">
          <div class="channel-screen dca-ch09">CH 09</div>
          <div class="channel-screen dca-ch10">CH 10</div>
          <div class="channel-screen dca-">CH 11</div>
          <div class="channel-screen dca-ch12">CH 12</div>
          <div class="channel-screen dca-ch13">CH 13</div>
          <div class="channel-screen dca-ch14">CH 14</div>
          <div class="channel-screen dca-ch15">CH 15</div>
          <div class="channel-screen dca-ch16">CH 16</div>
        </div>
        <div id="dca-2-row3" class="row-one-dca">
          <div class="channel-screen dca-ch17">CH 17</div>
          <div class="channel-screen dca-ch18">CH 18</div>
          <div class="channel-screen dca-ch19">CH 19</div>
          <div class="channel-screen dca-ch20">CH 20</div>
          <div class="channel-screen dca-ch21">CH 21</div>
          <div class="channel-screen dca-ch22">CH 22</div>
          <div class="channel-screen dca-ch23">CH 23</div>
          <div class="channel-screen dca-ch24">CH 24</div>
        </div>
        <div id="dca-2-row4" class="row-one-dca">
          <div class="channel-screen dca-ch25">CH 25</div>
          <div class="channel-screen dca-ch26">CH 26</div>
          <div class="channel-screen dca-ch27">CH 27</div>
          <div class="channel-screen dca-ch28">CH 28</div>
          <div class="channel-screen dca-ch29">CH 29</div>
          <div class="channel-screen dca-ch30">CH 30</div>
          <div class="channel-screen dca-ch31">CH 31</div>
          <div class="channel-screen dca-ch32">CH 32</div>
        </div>
        <div id="dca-2-row5" class="row-one-dca aux-row">
          <div class="channel-screen aux-ch1">AUX 1</div>
          <div class="channel-screen aux-ch2">AUX 2</div>
          <div class="channel-screen aux-ch3">AUX 3</div>
          <div class="channel-screen aux-ch4">AUX 4</div>
          <div class="channel-screen aux-ch5">AUX 5</div>
          <div class="channel-screen aux-ch6">AUX 6</div>
          <div class="channel-screen aux-ch7">AUX 7</div>
          <div class="channel-screen aux-ch8">AUX 8</div>
        </div>
        <div id="dca-2-row6" class="row-one-dca fx-row">
          <div class="channel-screen fx-ch1">FX1L</div>
          <div class="channel-screen fx-ch2">FX1R</div>
          <div class="channel-screen fx-ch3">FX2L</div>
          <div class="channel-screen fx-ch4">FX2R</div>
          <div class="channel-screen fx-ch5">FX3L</div>
          <div class="channel-screen fx-ch6">FX3R</div>
          <div class="channel-screen fx-ch7">FX4L</div>
          <div class="channel-screen fx-ch8">FX4R</div>
        </div>
        <div id="dca-2-row7" class="row-one-dca bus18-row">
          <div class="channel-screen bus-ch1">BUS 1</div>
          <div class="channel-screen bus-ch2">BUS 2</div>
          <div class="channel-screen bus-ch3">BUS 3</div>
          <div class="channel-screen bus-ch4">BUS 4</div>
          <div class="channel-screen bus-ch5">BUS 5</div>
          <div class="channel-screen bus-ch6">BUS 6</div>
          <div class="channel-screen bus-ch7">BUS 7</div>
          <div class="channel-screen bus-ch8">BUS 8</div>
        </div>
        <div id="dca-2-row8" class="row-one-dca bus916-row">
          <div class="channel-screen bus-ch9">BUS 9</div>
          <div class="channel-screen bus-ch10">BUS 10</div>
          <div class="channel-screen bus-">BUS 11</div>
          <div class="channel-screen bus-ch12">BUS 12</div>
          <div class="channel-screen bus-ch13">BUS 13</div>
          <div class="channel-screen bus-ch14">BUS 14</div>
          <div class="channel-screen bus-ch15">BUS 15</div>
          <div class="channel-screen bus-ch16">BUS 16</div>
        </div>
      </div>
      <div id="close-dca2-btn" class="btn-block close">CLOSE</div>
    </div>

    <!-- dca 3 -->
    <div id="dca3-pop" class="dca-pop-screen dca3-pop-screen">
      <div class="heading-dca">DCA 3</div>
      <div id="dca-3-row1" class="dca-edit-screen-flex">
        <div class="row-one-dca">
          <div class="channel-screen dca-ch1">CH 01</div>
          <div class="channel-screen dca-ch2">CH 02</div>
          <div class="channel-screen dca-ch3">CH 03</div>
          <div class="channel-screen dca-ch4">CH 04</div>
          <div class="channel-screen dca-ch5">CH 05</div>
          <div class="channel-screen dca-ch6">CH 06</div>
          <div class="channel-screen dca-ch7">CH 07</div>
          <div class="channel-screen dca-ch8">CH 08</div>
        </div>
        <div id="dca-3-row2" class="row-one-dca">
          <div class="channel-screen dca-ch09">CH 09</div>
          <div class="channel-screen dca-ch10">CH 10</div>
          <div class="channel-screen dca-">CH 11</div>
          <div class="channel-screen dca-ch12">CH 12</div>
          <div class="channel-screen dca-ch13">CH 13</div>
          <div class="channel-screen dca-ch14">CH 14</div>
          <div class="channel-screen dca-ch15">CH 15</div>
          <div class="channel-screen dca-ch16">CH 16</div>
        </div>
        <div id="dca-3-row3" class="row-one-dca">
          <div class="channel-screen dca-ch17">CH 17</div>
          <div class="channel-screen dca-ch18">CH 18</div>
          <div class="channel-screen dca-ch19">CH 19</div>
          <div class="channel-screen dca-ch20">CH 20</div>
          <div class="channel-screen dca-ch21">CH 21</div>
          <div class="channel-screen dca-ch22">CH 22</div>
          <div class="channel-screen dca-ch23">CH 23</div>
          <div class="channel-screen dca-ch24">CH 24</div>
        </div>
        <div id="dca-3-row4" class="row-one-dca">
          <div class="channel-screen dca-ch25">CH 25</div>
          <div class="channel-screen dca-ch26">CH 26</div>
          <div class="channel-screen dca-ch27">CH 27</div>
          <div class="channel-screen dca-ch28">CH 28</div>
          <div class="channel-screen dca-ch29">CH 29</div>
          <div class="channel-screen dca-ch30">CH 30</div>
          <div class="channel-screen dca-ch31">CH 31</div>
          <div class="channel-screen dca-ch32">CH 32</div>
        </div>
        <div id="dca-3-row5" class="row-one-dca aux-row">
          <div class="channel-screen aux-ch1">AUX 1</div>
          <div class="channel-screen aux-ch2">AUX 2</div>
          <div class="channel-screen aux-ch3">AUX 3</div>
          <div class="channel-screen aux-ch4">AUX 4</div>
          <div class="channel-screen aux-ch5">AUX 5</div>
          <div class="channel-screen aux-ch6">AUX 6</div>
          <div class="channel-screen aux-ch7">AUX 7</div>
          <div class="channel-screen aux-ch8">AUX 8</div>
        </div>
        <div id="dca-3-row6" class="row-one-dca fx-row">
          <div class="channel-screen fx-ch1">FX1L</div>
          <div class="channel-screen fx-ch2">FX1R</div>
          <div class="channel-screen fx-ch3">FX2L</div>
          <div class="channel-screen fx-ch4">FX2R</div>
          <div class="channel-screen fx-ch5">FX3L</div>
          <div class="channel-screen fx-ch6">FX3R</div>
          <div class="channel-screen fx-ch7">FX4L</div>
          <div class="channel-screen fx-ch8">FX4R</div>
        </div>
        <div id="dca-3-row7" class="row-one-dca bus18-row">
          <div class="channel-screen bus-ch1">BUS 1</div>
          <div class="channel-screen bus-ch2">BUS 2</div>
          <div class="channel-screen bus-ch3">BUS 3</div>
          <div class="channel-screen bus-ch4">BUS 4</div>
          <div class="channel-screen bus-ch5">BUS 5</div>
          <div class="channel-screen bus-ch6">BUS 6</div>
          <div class="channel-screen bus-ch7">BUS 7</div>
          <div class="channel-screen bus-ch8">BUS 8</div>
        </div>
        <div id="dca-3-row8" class="row-one-dca bus916-row">
          <div class="channel-screen bus-ch9">BUS 9</div>
          <div class="channel-screen bus-ch10">BUS 10</div>
          <div class="channel-screen bus-">BUS 11</div>
          <div class="channel-screen bus-ch12">BUS 12</div>
          <div class="channel-screen bus-ch13">BUS 13</div>
          <div class="channel-screen bus-ch14">BUS 14</div>
          <div class="channel-screen bus-ch15">BUS 15</div>
          <div class="channel-screen bus-ch16">BUS 16</div>
        </div>
      </div>
      <div id="close-dca3-btn" class="btn-block close">CLOSE</div>
    </div>

    <!-- dca 4 -->
    <div id="dca4-pop" class="dca-pop-screen dca4-pop-screen">
      <div class="heading-dca">DCA 4</div>
      <div id="dca-4-row1" class="dca-edit-screen-flex">
        <div class="row-one-dca">
          <div class="channel-screen dca-ch1">CH 01</div>
          <div class="channel-screen dca-ch2">CH 02</div>
          <div class="channel-screen dca-ch3">CH 03</div>
          <div class="channel-screen dca-ch4">CH 04</div>
          <div class="channel-screen dca-ch5">CH 05</div>
          <div class="channel-screen dca-ch6">CH 06</div>
          <div class="channel-screen dca-ch7">CH 07</div>
          <div class="channel-screen dca-ch8">CH 08</div>
        </div>
        <div id="dca-4-row2" class="row-one-dca">
          <div class="channel-screen dca-ch09">CH 09</div>
          <div class="channel-screen dca-ch10">CH 10</div>
          <div class="channel-screen dca-">CH 11</div>
          <div class="channel-screen dca-ch12">CH 12</div>
          <div class="channel-screen dca-ch13">CH 13</div>
          <div class="channel-screen dca-ch14">CH 14</div>
          <div class="channel-screen dca-ch15">CH 15</div>
          <div class="channel-screen dca-ch16">CH 16</div>
        </div>
        <div id="dca-4-row3" class="row-one-dca">
          <div class="channel-screen dca-ch17">CH 17</div>
          <div class="channel-screen dca-ch18">CH 18</div>
          <div class="channel-screen dca-ch19">CH 19</div>
          <div class="channel-screen dca-ch20">CH 20</div>
          <div class="channel-screen dca-ch21">CH 21</div>
          <div class="channel-screen dca-ch22">CH 22</div>
          <div class="channel-screen dca-ch23">CH 23</div>
          <div class="channel-screen dca-ch24">CH 24</div>
        </div>
        <div id="dca-4-row4" class="row-one-dca">
          <div class="channel-screen dca-ch25">CH 25</div>
          <div class="channel-screen dca-ch26">CH 26</div>
          <div class="channel-screen dca-ch27">CH 27</div>
          <div class="channel-screen dca-ch28">CH 28</div>
          <div class="channel-screen dca-ch29">CH 29</div>
          <div class="channel-screen dca-ch30">CH 30</div>
          <div class="channel-screen dca-ch31">CH 31</div>
          <div class="channel-screen dca-ch32">CH 32</div>
        </div>
        <div id="dca-4-row5" class="row-one-dca aux-row">
          <div class="channel-screen aux-ch1">AUX 1</div>
          <div class="channel-screen aux-ch2">AUX 2</div>
          <div class="channel-screen aux-ch3">AUX 3</div>
          <div class="channel-screen aux-ch4">AUX 4</div>
          <div class="channel-screen aux-ch5">AUX 5</div>
          <div class="channel-screen aux-ch6">AUX 6</div>
          <div class="channel-screen aux-ch7">AUX 7</div>
          <div class="channel-screen aux-ch8">AUX 8</div>
        </div>
        <div id="dca-4-row6" class="row-one-dca fx-row">
          <div class="channel-screen fx-ch1">FX1L</div>
          <div class="channel-screen fx-ch2">FX1R</div>
          <div class="channel-screen fx-ch3">FX2L</div>
          <div class="channel-screen fx-ch4">FX2R</div>
          <div class="channel-screen fx-ch5">FX3L</div>
          <div class="channel-screen fx-ch6">FX3R</div>
          <div class="channel-screen fx-ch7">FX4L</div>
          <div class="channel-screen fx-ch8">FX4R</div>
        </div>
        <div id="dca-4-row7" class="row-one-dca bus18-row">
          <div class="channel-screen bus-ch1">BUS 1</div>
          <div class="channel-screen bus-ch2">BUS 2</div>
          <div class="channel-screen bus-ch3">BUS 3</div>
          <div class="channel-screen bus-ch4">BUS 4</div>
          <div class="channel-screen bus-ch5">BUS 5</div>
          <div class="channel-screen bus-ch6">BUS 6</div>
          <div class="channel-screen bus-ch7">BUS 7</div>
          <div class="channel-screen bus-ch8">BUS 8</div>
        </div>
        <div id="dca-4-row8" class="row-one-dca bus916-row">
          <div class="channel-screen bus-ch9">BUS 9</div>
          <div class="channel-screen bus-ch10">BUS 10</div>
          <div class="channel-screen bus-">BUS 11</div>
          <div class="channel-screen bus-ch12">BUS 12</div>
          <div class="channel-screen bus-ch13">BUS 13</div>
          <div class="channel-screen bus-ch14">BUS 14</div>
          <div class="channel-screen bus-ch15">BUS 15</div>
          <div class="channel-screen bus-ch16">BUS 16</div>
        </div>
      </div>
      <div id="close-dca4-btn" class="btn-block close">CLOSE</div>
    </div>
    <!-- dca 5 -->
    <div id="dca5-pop" class="dca-pop-screen dca5-pop-screen">
      <div class="heading-dca">DCA 5</div>
      <div id="dca-5-row1" class="dca-edit-screen-flex">
        <div class="row-one-dca">
          <div class="channel-screen dca-ch1">CH 01</div>
          <div class="channel-screen dca-ch2">CH 02</div>
          <div class="channel-screen dca-ch3">CH 03</div>
          <div class="channel-screen dca-ch4">CH 04</div>
          <div class="channel-screen dca-ch5">CH 05</div>
          <div class="channel-screen dca-ch6">CH 06</div>
          <div class="channel-screen dca-ch7">CH 07</div>
          <div class="channel-screen dca-ch8">CH 08</div>
        </div>
        <div id="dca-5-row2" class="row-one-dca">
          <div class="channel-screen dca-ch09">CH 09</div>
          <div class="channel-screen dca-ch10">CH 10</div>
          <div class="channel-screen dca-">CH 11</div>
          <div class="channel-screen dca-ch12">CH 12</div>
          <div class="channel-screen dca-ch13">CH 13</div>
          <div class="channel-screen dca-ch14">CH 14</div>
          <div class="channel-screen dca-ch15">CH 15</div>
          <div class="channel-screen dca-ch16">CH 16</div>
        </div>
        <div id="dca-5-row3" class="row-one-dca">
          <div class="channel-screen dca-ch17">CH 17</div>
          <div class="channel-screen dca-ch18">CH 18</div>
          <div class="channel-screen dca-ch19">CH 19</div>
          <div class="channel-screen dca-ch20">CH 20</div>
          <div class="channel-screen dca-ch21">CH 21</div>
          <div class="channel-screen dca-ch22">CH 22</div>
          <div class="channel-screen dca-ch23">CH 23</div>
          <div class="channel-screen dca-ch24">CH 24</div>
        </div>
        <div id="dca-5-row4" class="row-one-dca">
          <div class="channel-screen dca-ch25">CH 25</div>
          <div class="channel-screen dca-ch26">CH 26</div>
          <div class="channel-screen dca-ch27">CH 27</div>
          <div class="channel-screen dca-ch28">CH 28</div>
          <div class="channel-screen dca-ch29">CH 29</div>
          <div class="channel-screen dca-ch30">CH 30</div>
          <div class="channel-screen dca-ch31">CH 31</div>
          <div class="channel-screen dca-ch32">CH 32</div>
        </div>
        <div id="dca-5-row5" class="row-one-dca aux-row">
          <div class="channel-screen aux-ch1">AUX 1</div>
          <div class="channel-screen aux-ch2">AUX 2</div>
          <div class="channel-screen aux-ch3">AUX 3</div>
          <div class="channel-screen aux-ch4">AUX 4</div>
          <div class="channel-screen aux-ch5">AUX 5</div>
          <div class="channel-screen aux-ch6">AUX 6</div>
          <div class="channel-screen aux-ch7">AUX 7</div>
          <div class="channel-screen aux-ch8">AUX 8</div>
        </div>
        <div id="dca-5-row6" class="row-one-dca fx-row">
          <div class="channel-screen fx-ch1">FX1L</div>
          <div class="channel-screen fx-ch2">FX1R</div>
          <div class="channel-screen fx-ch3">FX2L</div>
          <div class="channel-screen fx-ch4">FX2R</div>
          <div class="channel-screen fx-ch5">FX3L</div>
          <div class="channel-screen fx-ch6">FX3R</div>
          <div class="channel-screen fx-ch7">FX4L</div>
          <div class="channel-screen fx-ch8">FX4R</div>
        </div>
        <div id="dca-5-row7" class="row-one-dca bus18-row">
          <div class="channel-screen bus-ch1">BUS 1</div>
          <div class="channel-screen bus-ch2">BUS 2</div>
          <div class="channel-screen bus-ch3">BUS 3</div>
          <div class="channel-screen bus-ch4">BUS 4</div>
          <div class="channel-screen bus-ch5">BUS 5</div>
          <div class="channel-screen bus-ch6">BUS 6</div>
          <div class="channel-screen bus-ch7">BUS 7</div>
          <div class="channel-screen bus-ch8">BUS 8</div>
        </div>
        <div id="dca-5-row8" class="row-one-dca bus916-row">
          <div class="channel-screen bus-ch9">BUS 9</div>
          <div class="channel-screen bus-ch10">BUS 10</div>
          <div class="channel-screen bus-">BUS 11</div>
          <div class="channel-screen bus-ch12">BUS 12</div>
          <div class="channel-screen bus-ch13">BUS 13</div>
          <div class="channel-screen bus-ch14">BUS 14</div>
          <div class="channel-screen bus-ch15">BUS 15</div>
          <div class="channel-screen bus-ch16">BUS 16</div>
        </div>
      </div>
      <div id="close-dca5-btn" class="btn-block close">CLOSE</div>
    </div>
    <!-- dca 6 -->
    <div id="dca6-pop" class="dca-pop-screen dca6-pop-screen">
      <div class="heading-dca">DCA 6</div>
      <div id="dca-6-row1" class="dca-edit-screen-flex">
        <div class="row-one-dca">
          <div class="channel-screen dca-ch1">CH 01</div>
          <div class="channel-screen dca-ch2">CH 02</div>
          <div class="channel-screen dca-ch3">CH 03</div>
          <div class="channel-screen dca-ch4">CH 04</div>
          <div class="channel-screen dca-ch5">CH 05</div>
          <div class="channel-screen dca-ch6">CH 06</div>
          <div class="channel-screen dca-ch7">CH 07</div>
          <div class="channel-screen dca-ch8">CH 08</div>
        </div>
        <div id="dca-6-row2" class="row-one-dca">
          <div class="channel-screen dca-ch09">CH 09</div>
          <div class="channel-screen dca-ch10">CH 10</div>
          <div class="channel-screen dca-">CH 11</div>
          <div class="channel-screen dca-ch12">CH 12</div>
          <div class="channel-screen dca-ch13">CH 13</div>
          <div class="channel-screen dca-ch14">CH 14</div>
          <div class="channel-screen dca-ch15">CH 15</div>
          <div class="channel-screen dca-ch16">CH 16</div>
        </div>
        <div id="dca-6-row3" class="row-one-dca">
          <div class="channel-screen dca-ch17">CH 17</div>
          <div class="channel-screen dca-ch18">CH 18</div>
          <div class="channel-screen dca-ch19">CH 19</div>
          <div class="channel-screen dca-ch20">CH 20</div>
          <div class="channel-screen dca-ch21">CH 21</div>
          <div class="channel-screen dca-ch22">CH 22</div>
          <div class="channel-screen dca-ch23">CH 23</div>
          <div class="channel-screen dca-ch24">CH 24</div>
        </div>
        <div id="dca-6-row4" class="row-one-dca">
          <div class="channel-screen dca-ch25">CH 25</div>
          <div class="channel-screen dca-ch26">CH 26</div>
          <div class="channel-screen dca-ch27">CH 27</div>
          <div class="channel-screen dca-ch28">CH 28</div>
          <div class="channel-screen dca-ch29">CH 29</div>
          <div class="channel-screen dca-ch30">CH 30</div>
          <div class="channel-screen dca-ch31">CH 31</div>
          <div class="channel-screen dca-ch32">CH 32</div>
        </div>
        <div id="dca-6-row5" class="row-one-dca aux-row">
          <div class="channel-screen aux-ch1">AUX 1</div>
          <div class="channel-screen aux-ch2">AUX 2</div>
          <div class="channel-screen aux-ch3">AUX 3</div>
          <div class="channel-screen aux-ch4">AUX 4</div>
          <div class="channel-screen aux-ch5">AUX 5</div>
          <div class="channel-screen aux-ch6">AUX 6</div>
          <div class="channel-screen aux-ch7">AUX 7</div>
          <div class="channel-screen aux-ch8">AUX 8</div>
        </div>
        <div id="dca-6-row6" class="row-one-dca fx-row">
          <div class="channel-screen fx-ch1">FX1L</div>
          <div class="channel-screen fx-ch2">FX1R</div>
          <div class="channel-screen fx-ch3">FX2L</div>
          <div class="channel-screen fx-ch4">FX2R</div>
          <div class="channel-screen fx-ch5">FX3L</div>
          <div class="channel-screen fx-ch6">FX3R</div>
          <div class="channel-screen fx-ch7">FX4L</div>
          <div class="channel-screen fx-ch8">FX4R</div>
        </div>
        <div id="dca-6-row7" class="row-one-dca bus18-row">
          <div class="channel-screen bus-ch1">BUS 1</div>
          <div class="channel-screen bus-ch2">BUS 2</div>
          <div class="channel-screen bus-ch3">BUS 3</div>
          <div class="channel-screen bus-ch4">BUS 4</div>
          <div class="channel-screen bus-ch5">BUS 5</div>
          <div class="channel-screen bus-ch6">BUS 6</div>
          <div class="channel-screen bus-ch7">BUS 7</div>
          <div class="channel-screen bus-ch8">BUS 8</div>
        </div>
        <div id="dca-6-row8" class="row-one-dca bus916-row">
          <div class="channel-screen bus-ch9">BUS 9</div>
          <div class="channel-screen bus-ch10">BUS 10</div>
          <div class="channel-screen bus-">BUS 11</div>
          <div class="channel-screen bus-ch12">BUS 12</div>
          <div class="channel-screen bus-ch13">BUS 13</div>
          <div class="channel-screen bus-ch14">BUS 14</div>
          <div class="channel-screen bus-ch15">BUS 15</div>
          <div class="channel-screen bus-ch16">BUS 16</div>
        </div>
      </div>
      <div id="close-dca6-btn" class="btn-block close">CLOSE</div>
    </div>
    <!-- dca 7 -->
    <div id="dca7-pop" class="dca-pop-screen dca7-pop-screen">
      <div class="heading-dca">DCA 7</div>
      <div id="dca-7-row1" class="dca-edit-screen-flex">
        <div class="row-one-dca">
          <div class="channel-screen dca-ch1">CH 01</div>
          <div class="channel-screen dca-ch2">CH 02</div>
          <div class="channel-screen dca-ch3">CH 03</div>
          <div class="channel-screen dca-ch4">CH 04</div>
          <div class="channel-screen dca-ch5">CH 05</div>
          <div class="channel-screen dca-ch6">CH 06</div>
          <div class="channel-screen dca-ch7">CH 07</div>
          <div class="channel-screen dca-ch8">CH 08</div>
        </div>
        <div id="dca-7-row2" class="row-one-dca">
          <div class="channel-screen dca-ch09">CH 09</div>
          <div class="channel-screen dca-ch10">CH 10</div>
          <div class="channel-screen dca-">CH 11</div>
          <div class="channel-screen dca-ch12">CH 12</div>
          <div class="channel-screen dca-ch13">CH 13</div>
          <div class="channel-screen dca-ch14">CH 14</div>
          <div class="channel-screen dca-ch15">CH 15</div>
          <div class="channel-screen dca-ch16">CH 16</div>
        </div>
        <div id="dca-7-row3" class="row-one-dca">
          <div class="channel-screen dca-ch17">CH 17</div>
          <div class="channel-screen dca-ch18">CH 18</div>
          <div class="channel-screen dca-ch19">CH 19</div>
          <div class="channel-screen dca-ch20">CH 20</div>
          <div class="channel-screen dca-ch21">CH 21</div>
          <div class="channel-screen dca-ch22">CH 22</div>
          <div class="channel-screen dca-ch23">CH 23</div>
          <div class="channel-screen dca-ch24">CH 24</div>
        </div>
        <div id="dca-7-row4" class="row-one-dca">
          <div class="channel-screen dca-ch25">CH 25</div>
          <div class="channel-screen dca-ch26">CH 26</div>
          <div class="channel-screen dca-ch27">CH 27</div>
          <div class="channel-screen dca-ch28">CH 28</div>
          <div class="channel-screen dca-ch29">CH 29</div>
          <div class="channel-screen dca-ch30">CH 30</div>
          <div class="channel-screen dca-ch31">CH 31</div>
          <div class="channel-screen dca-ch32">CH 32</div>
        </div>
        <div id="dca-7-row5" class="row-one-dca aux-row">
          <div class="channel-screen aux-ch1">AUX 1</div>
          <div class="channel-screen aux-ch2">AUX 2</div>
          <div class="channel-screen aux-ch3">AUX 3</div>
          <div class="channel-screen aux-ch4">AUX 4</div>
          <div class="channel-screen aux-ch5">AUX 5</div>
          <div class="channel-screen aux-ch6">AUX 6</div>
          <div class="channel-screen aux-ch7">AUX 7</div>
          <div class="channel-screen aux-ch8">AUX 8</div>
        </div>
        <div id="dca-7-row6" class="row-one-dca fx-row">
          <div class="channel-screen fx-ch1">FX1L</div>
          <div class="channel-screen fx-ch2">FX1R</div>
          <div class="channel-screen fx-ch3">FX2L</div>
          <div class="channel-screen fx-ch4">FX2R</div>
          <div class="channel-screen fx-ch5">FX3L</div>
          <div class="channel-screen fx-ch6">FX3R</div>
          <div class="channel-screen fx-ch7">FX4L</div>
          <div class="channel-screen fx-ch8">FX4R</div>
        </div>
        <div id="dca-7-row7" class="row-one-dca bus18-row">
          <div class="channel-screen bus-ch1">BUS 1</div>
          <div class="channel-screen bus-ch2">BUS 2</div>
          <div class="channel-screen bus-ch3">BUS 3</div>
          <div class="channel-screen bus-ch4">BUS 4</div>
          <div class="channel-screen bus-ch5">BUS 5</div>
          <div class="channel-screen bus-ch6">BUS 6</div>
          <div class="channel-screen bus-ch7">BUS 7</div>
          <div class="channel-screen bus-ch8">BUS 8</div>
        </div>
        <div id="dca-7-row8" class="row-one-dca bus916-row">
          <div class="channel-screen bus-ch9">BUS 9</div>
          <div class="channel-screen bus-ch10">BUS 10</div>
          <div class="channel-screen bus-">BUS 11</div>
          <div class="channel-screen bus-ch12">BUS 12</div>
          <div class="channel-screen bus-ch13">BUS 13</div>
          <div class="channel-screen bus-ch14">BUS 14</div>
          <div class="channel-screen bus-ch15">BUS 15</div>
          <div class="channel-screen bus-ch16">BUS 16</div>
        </div>
      </div>
      <div id="close-dca7-btn" class="btn-block close">CLOSE</div>
    </div>
    <!-- dca 8 -->
    <div id="dca8-pop" class="dca-pop-screen dca8-pop-screen">
      <div class="heading-dca">DCA 8</div>
      <div id="dca-8-row1" class="dca-edit-screen-flex">
        <div class="row-one-dca">
          <div class="channel-screen dca-ch1">CH 01</div>
          <div class="channel-screen dca-ch2">CH 02</div>
          <div class="channel-screen dca-ch3">CH 03</div>
          <div class="channel-screen dca-ch4">CH 04</div>
          <div class="channel-screen dca-ch5">CH 05</div>
          <div class="channel-screen dca-ch6">CH 06</div>
          <div class="channel-screen dca-ch7">CH 07</div>
          <div class="channel-screen dca-ch8">CH 08</div>
        </div>
        <div id="dca-8-row2" class="row-one-dca">
          <div class="channel-screen dca-ch09">CH 09</div>
          <div class="channel-screen dca-ch10">CH 10</div>
          <div class="channel-screen dca-">CH 11</div>
          <div class="channel-screen dca-ch12">CH 12</div>
          <div class="channel-screen dca-ch13">CH 13</div>
          <div class="channel-screen dca-ch14">CH 14</div>
          <div class="channel-screen dca-ch15">CH 15</div>
          <div class="channel-screen dca-ch16">CH 16</div>
        </div>
        <div id="dca-8-row3" class="row-one-dca">
          <div class="channel-screen dca-ch17">CH 17</div>
          <div class="channel-screen dca-ch18">CH 18</div>
          <div class="channel-screen dca-ch19">CH 19</div>
          <div class="channel-screen dca-ch20">CH 20</div>
          <div class="channel-screen dca-ch21">CH 21</div>
          <div class="channel-screen dca-ch22">CH 22</div>
          <div class="channel-screen dca-ch23">CH 23</div>
          <div class="channel-screen dca-ch24">CH 24</div>
        </div>
        <div id="dca-8-row4" class="row-one-dca">
          <div class="channel-screen dca-ch25">CH 25</div>
          <div class="channel-screen dca-ch26">CH 26</div>
          <div class="channel-screen dca-ch27">CH 27</div>
          <div class="channel-screen dca-ch28">CH 28</div>
          <div class="channel-screen dca-ch29">CH 29</div>
          <div class="channel-screen dca-ch30">CH 30</div>
          <div class="channel-screen dca-ch31">CH 31</div>
          <div class="channel-screen dca-ch32">CH 32</div>
        </div>
        <div id="dca-8-row5" class="row-one-dca aux-row">
          <div class="channel-screen aux-ch1">AUX 1</div>
          <div class="channel-screen aux-ch2">AUX 2</div>
          <div class="channel-screen aux-ch3">AUX 3</div>
          <div class="channel-screen aux-ch4">AUX 4</div>
          <div class="channel-screen aux-ch5">AUX 5</div>
          <div class="channel-screen aux-ch6">AUX 6</div>
          <div class="channel-screen aux-ch7">AUX 7</div>
          <div class="channel-screen aux-ch8">AUX 8</div>
        </div>
        <div id="dca-8-row6" class="row-one-dca fx-row">
          <div class="channel-screen fx-ch1">FX1L</div>
          <div class="channel-screen fx-ch2">FX1R</div>
          <div class="channel-screen fx-ch3">FX2L</div>
          <div class="channel-screen fx-ch4">FX2R</div>
          <div class="channel-screen fx-ch5">FX3L</div>
          <div class="channel-screen fx-ch6">FX3R</div>
          <div class="channel-screen fx-ch7">FX4L</div>
          <div class="channel-screen fx-ch8">FX4R</div>
        </div>
        <div id="dca-8-row7" class="row-one-dca bus18-row">
          <div class="channel-screen bus-ch1">BUS 1</div>
          <div class="channel-screen bus-ch2">BUS 2</div>
          <div class="channel-screen bus-ch3">BUS 3</div>
          <div class="channel-screen bus-ch4">BUS 4</div>
          <div class="channel-screen bus-ch5">BUS 5</div>
          <div class="channel-screen bus-ch6">BUS 6</div>
          <div class="channel-screen bus-ch7">BUS 7</div>
          <div class="channel-screen bus-ch8">BUS 8</div>
        </div>
        <div id="dca-8-row8" class="row-one-dca bus916-row">
          <div class="channel-screen bus-ch9">BUS 9</div>
          <div class="channel-screen bus-ch10">BUS 10</div>
          <div class="channel-screen bus-">BUS 11</div>
          <div class="channel-screen bus-ch12">BUS 12</div>
          <div class="channel-screen bus-ch13">BUS 13</div>
          <div class="channel-screen bus-ch14">BUS 14</div>
          <div class="channel-screen bus-ch15">BUS 15</div>
          <div class="channel-screen bus-ch16">BUS 16</div>
        </div>
      </div>
      <div id="close-dca8-btn" class="btn-block close">CLOSE</div>
    </div>
    <!-- edit dca end -->
    <!-- js -->
    <script src="assets/script.js"></script>
    <script src="assets/backend.js"></script>
    <script src="assets/script-animations.js"></script>
    <!-- Include SocketIO for session management -->
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="assets/session-manager.js"></script>
    <script>
      // Session Management for Main App - Make it globally available
      window.mainAppSessionManager = null;

      console.log(
        "Script loaded - VSESessionManager available:",
        typeof VSESessionManager
      );

      // Function to copy session key to clipboard
      function copySessionKey() {
        const sessionKeyInput = document.getElementById("current-session-key");
        const sessionKey = sessionKeyInput.value;

        if (sessionKey === "------" || !sessionKey) {
          alert("No session key to copy");
          return;
        }

        // Modern clipboard API
        if (navigator.clipboard && window.isSecureContext) {
          navigator.clipboard
            .writeText(sessionKey)
            .then(function () {
              // Visual feedback
              const originalBg = sessionKeyInput.style.background;
              sessionKeyInput.style.background = "#4CAF50";
              sessionKeyInput.title = "Copied!";

              setTimeout(function () {
                sessionKeyInput.style.background = originalBg;
                sessionKeyInput.title = "Click to copy session key";
              }, 1000);

              console.log("Session key copied to clipboard:", sessionKey);
            })
            .catch(function (err) {
              console.error("Failed to copy session key:", err);
              fallbackCopy();
            });
        } else {
          // Fallback for older browsers
          fallbackCopy();
        }

        function fallbackCopy() {
          sessionKeyInput.select();
          sessionKeyInput.setSelectionRange(0, 99999); // For mobile
          try {
            document.execCommand("copy");
            alert("Session key copied: " + sessionKey);
          } catch (err) {
            alert(
              "Could not copy session key. Please select and copy manually: " +
                sessionKey
            );
          }
          sessionKeyInput.blur();
        }
      }

      // Function to request board state synchronization
      function requestBoardStateSync() {
        console.log("Requesting board state sync...");

        if (
          !window.mainAppSessionManager ||
          !window.mainAppSessionManager.sessionKey
        ) {
          console.error("Cannot sync board state - not in session");
          return;
        }

        // Send a special OSC command to request board state
        const syncCommand = {
          type: "sync_request",
          channels: Array.from({ length: 32 }, (_, i) => i + 1), // Request all 32 main channels
        };

        if (window.mainAppSessionManager.sendOSCCommand(syncCommand)) {
          console.log("Board state sync requested");
        } else {
          console.error("Failed to request board state sync");
        }
      }

      // Function to update UI with board state
      function updateBoardState(boardState) {
        const faderCount = boardState.faders
          ? Object.keys(boardState.faders).length
          : 0;
        const muteCount = boardState.mutes
          ? Object.keys(boardState.mutes).length
          : 0;
        console.log(
          `Updating board state: ${faderCount} faders, ${muteCount} mutes`
        );

        if (boardState.faders) {
          Object.keys(boardState.faders).forEach((channel) => {
            updateFaderPosition(parseInt(channel), boardState.faders[channel]);
          });
        }

        if (boardState.mutes) {
          Object.keys(boardState.mutes).forEach((channel) => {
            updateMuteState(parseInt(channel), boardState.mutes[channel]);
          });
        }

        console.log(
          `✅ Board state sync complete: ${faderCount} channels synchronized`
        );
      }

      // Function to update fader position
      function updateFaderPosition(channel, value) {
        const faderElement = document.getElementById(`dragger-ch${channel}`);
        if (faderElement) {
          const container = faderElement.parentElement;
          const containerHeight = container.offsetHeight;
          const faderHeight = faderElement.offsetHeight;

          // Convert 0-1 value to position (1 = top, 0 = bottom)
          const position = (1 - value) * (containerHeight - faderHeight);
          faderElement.style.top = position + "px";

          console.log(
            `Updated fader CH${channel}: ${value} (position: ${position}px)`
          );
        }
      }

      // Function to update mute state
      function updateMuteState(channel, isMuted) {
        const muteButton = document.getElementById(`mute-${channel}`);
        if (muteButton) {
          if (isMuted) {
            muteButton.classList.add("active-btn-background");
          } else {
            muteButton.classList.remove("active-btn-background");
          }
          console.log(
            `Updated mute CH${channel}: ${isMuted ? "muted" : "unmuted"}`
          );
        }
      }

      document.addEventListener("DOMContentLoaded", function () {
        console.log("DOM Content Loaded - initializing session manager");

        // Check if VSESessionManager exists
        if (typeof VSESessionManager === "undefined") {
          console.error(
            "VSESessionManager is not defined! Check if session-manager.js loaded properly."
          );
          return;
        }

        // Check if button exists
        const createBtn = document.getElementById("create-session-btn");
        if (!createBtn) {
          console.error("Create session button not found!");
          return;
        }
        console.log("Found create session button:", createBtn);

        // Initialize session manager
        try {
          window.mainAppSessionManager = new VSESessionManager();
          window.mainAppSessionManager.init();
          console.log("Session manager initialized successfully");
        } catch (error) {
          console.error("Failed to initialize session manager:", error);
          return;
        }

        // Session creation success handler
        window.mainAppSessionManager.onSessionCreated = function (sessionData) {
          console.log("Session created:", sessionData);
          document.getElementById("current-session-key").value =
            sessionData.session_key;
          document.getElementById("session-status").textContent =
            "Active (Host)";
          document.getElementById("session-status").style.color = "#000000";
          document.getElementById("session-info-display").style.display =
            "block";
        };

        // Board state update handler
        window.mainAppSessionManager.onBoardStateUpdate = function (
          boardState
        ) {
          console.log("Received board state update:", boardState);
          updateBoardState(boardState);
        };

        // Error handler
        window.mainAppSessionManager.onError = function (error) {
          console.error("Session error:", error);
          document.getElementById("session-status").textContent =
            "Error: " + error;
          document.getElementById("session-status").style.color = "#000000";
          document.getElementById("session-info-display").style.display =
            "block";
        };

        // Create Session button click handler
        createBtn.addEventListener("click", async function (event) {
          console.log("CREATE SESSION BUTTON CLICKED!");
          event.preventDefault();
          event.stopPropagation();

          console.log("Creating session...");
          document.getElementById("session-status").textContent = "Creating...";
          document.getElementById("session-status").style.color = "#000000";
          document.getElementById("current-session-key").value = "------";
          document.getElementById("session-info-display").style.display =
            "block";

          try {
            const result = await window.mainAppSessionManager.createSession();
            console.log("Create session result:", result);
            if (result.success) {
              console.log("Session created successfully:", result.session_key);
            } else {
              console.error("Failed to create session:", result.error);
              document.getElementById("session-status").textContent =
                "Error: " + result.error;
              document.getElementById("session-status").style.color = "#000000";
            }
          } catch (error) {
            console.error("Error creating session:", error);
            document.getElementById("session-status").textContent =
              "Error creating session";
            document.getElementById("session-status").style.color = "#000000";
          }
        });

        console.log("Event listener attached to create session button");

        // Session Join Form Handler
        const sessionJoinForm = document.getElementById("verification-form");
        if (sessionJoinForm) {
          sessionJoinForm.addEventListener("submit", async function (event) {
            event.preventDefault();

            // Collect session key from 6 input fields
            const codeInputs = document.querySelectorAll(".code-input");
            let sessionKey = "";
            codeInputs.forEach((input) => {
              sessionKey += input.value;
            });

            if (sessionKey.length !== 6) {
              alert("Please enter a complete 6-digit session key");
              return;
            }

            console.log("Attempting to join session:", sessionKey);

            try {
              const result = await window.mainAppSessionManager.joinSession(
                sessionKey,
                null
              );

              if (result.success) {
                console.log("Successfully joined session:", sessionKey);

                // Close the popup
                document
                  .getElementById("popup-session-choice")
                  .classList.remove("active-session");

                // Update session display
                document.getElementById("current-session-key").value =
                  sessionKey;
                document.getElementById("session-status").textContent =
                  "Active (Client)";
                document.getElementById("session-status").style.color =
                  "#000000";
                document.getElementById("session-info-display").style.display =
                  "block";

                // Request board state sync
                requestBoardStateSync();
              } else {
                alert("Failed to join session: " + result.error);
              }
            } catch (error) {
              console.error("Error joining session:", error);
              alert("Error joining session: " + error.message);
            }
          });
        }
      });
    </script>
  </body>
</html>
