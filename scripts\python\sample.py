import numpy as np
import matplotlib.pyplot as plt
from scipy.signal import freqz
import gradio as gr


def bell_filter(frequencies, f0, Q, gain_db, fs=48000):
    A = 10 ** (gain_db / 40)
    w0 = 2 * np.pi * f0 / fs
    alpha = np.sin(w0) / (2 * Q)

    b0 = 1 + alpha * A
    b1 = -2 * np.cos(w0)
    b2 = 1 - alpha * A
    a0 = 1 + alpha / A
    a1 = -2 * np.cos(w0)
    a2 = 1 - alpha / A

    b = np.array([b0, b1, b2]) / a0
    a = np.array([a0, a1, a2]) / a0

    w, h = freqz(b, a, worN=frequencies, fs=fs)
    return h


def plot_eq(f1, g1, q1, f2, g2, q2, f3, g3, q3, f4, g4, q4):
    fs = 48000
    freqs = np.logspace(np.log10(20), np.log10(20000), 2048)

    H1 = bell_filter(freqs, f1, q1, g1)
    H2 = bell_filter(freqs, f2, q2, g2)
    H3 = bell_filter(freqs, f3, q3, g3)
    H4 = bell_filter(freqs, f4, q4, g4)

    H_total = H1 * H2 * H3 * H4
    H_db = 20 * np.log10(np.abs(H_total))

    fig, ax = plt.subplots(figsize=(10, 5))
    ax.semilogx(freqs, H_db, label="Combined EQ Curve", color="blue")
    ax.set_title("Multiband Parametric EQ Simulation")
    ax.set_xlabel("Frequency (Hz)")
    ax.set_ylabel("Gain (dB)")
    ax.set_xlim(20, 20000)
    ax.set_ylim(-24, 24)
    ax.grid(True, which="both", linestyle="--", alpha=0.5)
    ax.axhline(0, color="black", linewidth=1)
    ax.legend()
    plt.tight_layout()

    return fig


# Launch Gradio Interface
interface = gr.Interface(
    fn=plot_eq,
    inputs=[
        gr.Slider(20, 20000, value=100, label="Band 1 Frequency (Hz)", step=1),
        gr.Slider(-24, 24, value=0, label="Band 1 Gain (dB)"),
        gr.Slider(0.1, 10, value=1, label="Band 1 Q"),
        gr.Slider(20, 20000, value=500, label="Band 2 Frequency (Hz)", step=1),
        gr.Slider(-24, 24, value=0, label="Band 2 Gain (dB)"),
        gr.Slider(0.1, 10, value=1, label="Band 2 Q"),
        gr.Slider(20, 20000, value=2000, label="Band 3 Frequency (Hz)", step=1),
        gr.Slider(-24, 24, value=0, label="Band 3 Gain (dB)"),
        gr.Slider(0.1, 10, value=1, label="Band 3 Q"),
        gr.Slider(20, 20000, value=8000, label="Band 4 Frequency (Hz)", step=1),
        gr.Slider(-24, 24, value=0, label="Band 4 Gain (dB)"),
        gr.Slider(0.1, 10, value=1, label="Band 4 Q"),
    ],
    outputs=gr.Plot(),
    title="X32 Mix Style EQ Simulator",
)

interface.launch()
