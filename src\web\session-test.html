<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VSE Session Management Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
        }
        .container {
            display: flex;
            gap: 20px;
        }
        .panel {
            flex: 1;
            background: #2a2a2a;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #444;
        }
        .panel h2 {
            color: #ff6b35;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            color: #ccc;
        }
        input, button {
            width: 100%;
            padding: 10px;
            border: 1px solid #555;
            border-radius: 4px;
            background: #333;
            color: #fff;
            font-size: 14px;
        }
        button {
            background: #ff6b35;
            border: none;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            background: #e55a2e;
        }
        button:disabled {
            background: #666;
            cursor: not-allowed;
        }
        .status {
            margin-top: 20px;
            padding: 10px;
            border-radius: 4px;
            font-size: 14px;
        }
        .success {
            background: #2d5a2d;
            border: 1px solid #4a8a4a;
        }
        .error {
            background: #5a2d2d;
            border: 1px solid #8a4a4a;
        }
        .info {
            background: #2d3a5a;
            border: 1px solid #4a5a8a;
        }
        .log {
            margin-top: 20px;
            padding: 10px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 4px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .session-info {
            margin-top: 15px;
            padding: 10px;
            background: #333;
            border-radius: 4px;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <h1>VSE Session Management Test</h1>
    
    <div class="container">
        <!-- Host Panel -->
        <div class="panel">
            <h2>Host Panel</h2>
            
            <div class="form-group">
                <button id="createSessionBtn">Create Session</button>
            </div>
            
            <div class="form-group">
                <label for="hostUserId">Host User ID (optional):</label>
                <input type="text" id="hostUserId" placeholder="Leave empty for auto-generated">
            </div>
            
            <div class="form-group">
                <button id="sendOSCBtn" disabled>Send Test OSC Command</button>
            </div>
            
            <div id="hostStatus" class="status info" style="display: none;"></div>
            
            <div id="hostSessionInfo" class="session-info" style="display: none;"></div>
        </div>
        
        <!-- Client Panel -->
        <div class="panel">
            <h2>Client Panel</h2>
            
            <div class="form-group">
                <label for="sessionKeyInput">Session Key:</label>
                <input type="text" id="sessionKeyInput" placeholder="Enter 6-digit session key" maxlength="6">
            </div>
            
            <div class="form-group">
                <label for="clientUserId">Client User ID (optional):</label>
                <input type="text" id="clientUserId" placeholder="Leave empty for auto-generated">
            </div>
            
            <div class="form-group">
                <button id="joinSessionBtn">Join Session</button>
            </div>
            
            <div class="form-group">
                <button id="leaveSessionBtn" disabled>Leave Session</button>
            </div>
            
            <!-- OSC Test Buttons -->
            <div class="form-group">
                <h4 style="color: #ff6b35; margin: 20px 0 10px 0;">OSC Test Commands</h4>
                <button id="testMuteBtn">CLIENT TEST: Toggle Mute Ch1</button>
            </div>
            
            <div class="form-group">
                <button id="testFaderBtn">CLIENT TEST: Animate Fader Ch6</button>
            </div>
            
            <div id="clientStatus" class="status info" style="display: none;"></div>
            
            <div id="clientSessionInfo" class="session-info" style="display: none;"></div>
        </div>
    </div>
    
    <!-- Activity Log -->
    <div class="panel">
        <h2>Activity Log</h2>
        <div id="activityLog" class="log"></div>
        <button id="clearLogBtn">Clear Log</button>
    </div>

    <!-- Include SocketIO and our session manager -->
    <script src="https://cdn.socket.io/4.5.0/socket.io.min.js"></script>
    <script src="assets/session-manager.js"></script>
    
    <script>
        // DOM elements
        const createSessionBtn = document.getElementById('createSessionBtn');
        const hostUserId = document.getElementById('hostUserId');
        const sendOSCBtn = document.getElementById('sendOSCBtn');
        const hostStatus = document.getElementById('hostStatus');
        const hostSessionInfo = document.getElementById('hostSessionInfo');
        
        const sessionKeyInput = document.getElementById('sessionKeyInput');
        const clientUserId = document.getElementById('clientUserId');
        const joinSessionBtn = document.getElementById('joinSessionBtn');
        const leaveSessionBtn = document.getElementById('leaveSessionBtn');
        const testMuteBtn = document.getElementById('testMuteBtn');
        const testFaderBtn = document.getElementById('testFaderBtn');
        const clientStatus = document.getElementById('clientStatus');
        const clientSessionInfo = document.getElementById('clientSessionInfo');
        
        const activityLog = document.getElementById('activityLog');
        const clearLogBtn = document.getElementById('clearLogBtn');

        // Initialize session managers for host and client
        const hostSessionManager = new VSESessionManager();
        const clientSessionManager = new VSESessionManager();

        // Logging function
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.style.color = type === 'error' ? '#ff6b6b' : type === 'success' ? '#51cf66' : '#74c0fc';
            logEntry.textContent = `[${timestamp}] ${message}`;
            activityLog.appendChild(logEntry);
            activityLog.scrollTop = activityLog.scrollHeight;
        }

        // Status update functions
        function updateHostStatus(message, type) {
            hostStatus.textContent = message;
            hostStatus.className = `status ${type}`;
            hostStatus.style.display = 'block';
        }

        function updateClientStatus(message, type) {
            clientStatus.textContent = message;
            clientStatus.className = `status ${type}`;
            clientStatus.style.display = 'block';
        }

        function updateHostSessionInfo(sessionData) {
            if (sessionData) {
                hostSessionInfo.innerHTML = `
                    <strong>Session Key:</strong> ${sessionData.sessionKey}<br>
                    <strong>User ID:</strong> ${sessionData.userId}<br>
                    <strong>Role:</strong> Host
                `;
                hostSessionInfo.style.display = 'block';
            } else {
                hostSessionInfo.style.display = 'none';
            }
        }

        function updateClientSessionInfo(sessionData) {
            if (sessionData) {
                clientSessionInfo.innerHTML = `
                    <strong>Session Key:</strong> ${sessionData.sessionKey}<br>
                    <strong>User ID:</strong> ${sessionData.userId}<br>
                    <strong>Role:</strong> Client
                `;
                clientSessionInfo.style.display = 'block';
            } else {
                clientSessionInfo.style.display = 'none';
            }
        }

        // Initialize session managers
        hostSessionManager.init();
        clientSessionManager.init();

        // Set up event handlers for host
        hostSessionManager.onSessionJoined = (data) => {
            log(`Host: Joined session room successfully`, 'success');
            sendOSCBtn.disabled = false;
        };

        hostSessionManager.onUserJoined = (data) => {
            log(`Host: User ${data.user_id} (${data.role}) joined session`, 'info');
        };

        hostSessionManager.onUserLeft = (data) => {
            log(`Host: User ${data.user_id} left session`, 'info');
        };

        hostSessionManager.onError = (error) => {
            log(`Host Error: ${error}`, 'error');
            updateHostStatus(`Error: ${error}`, 'error');
        };

        hostSessionManager.onOSCCommand = (data) => {
            log(`Host: Received OSC command from ${data.user_id}: ${JSON.stringify(data.command)}`, 'info');
        };

        // Set up event handlers for client
        clientSessionManager.onSessionJoined = (data) => {
            log(`Client: Joined session room successfully`, 'success');
            leaveSessionBtn.disabled = false;
            // OSC buttons always enabled for testing
        };

        clientSessionManager.onUserJoined = (data) => {
            log(`Client: User ${data.user_id} (${data.role}) joined session`, 'info');
        };

        clientSessionManager.onUserLeft = (data) => {
            log(`Client: User ${data.user_id} left session`, 'info');
        };

        clientSessionManager.onError = (error) => {
            log(`Client Error: ${error}`, 'error');
            updateClientStatus(`Error: ${error}`, 'error');
        };

        clientSessionManager.onAwaitingApproval = (data) => {
            log(`Client: Awaiting host approval...`, 'info');
            updateClientStatus('Waiting for host approval...', 'info');
        };

        clientSessionManager.onConnectionApproved = (data) => {
            log(`Client: Connection approved by host`, 'success');
            updateClientStatus('Connection approved! Joining session...', 'success');
        };

        clientSessionManager.onConnectionDenied = (data) => {
            log(`Client: Connection denied by host - ${data.message}`, 'error');
            updateClientStatus(`Connection denied: ${data.message}`, 'error');
            joinSessionBtn.disabled = false;
        };

        clientSessionManager.onOSCCommand = (data) => {
            log(`Client: Received OSC command from ${data.user_id}: ${JSON.stringify(data.command)}`, 'info');
        };

        // Button event handlers
        createSessionBtn.addEventListener('click', async () => {
            createSessionBtn.disabled = true;
            updateHostStatus('Creating session...', 'info');
            log('Host: Creating session...', 'info');

            const result = await hostSessionManager.createSession(hostUserId.value || null);
            
            if (result.success) {
                updateHostStatus(`Session created successfully! Key: ${result.sessionKey}`, 'success');
                updateHostSessionInfo(result);
                log(`Host: Session created with key ${result.sessionKey}`, 'success');
            } else {
                updateHostStatus(`Failed to create session: ${result.error}`, 'error');
                log(`Host: Failed to create session - ${result.error}`, 'error');
                createSessionBtn.disabled = false;
            }
        });

        sendOSCBtn.addEventListener('click', () => {
            const testCommand = {
                type: 'mute',
                channel: 1,
                action: 'mute'
            };

            const success = hostSessionManager.sendOSCCommand(testCommand);
            if (success) {
                log(`Host: Sent OSC command: ${JSON.stringify(testCommand)}`, 'info');
            } else {
                log('Host: Failed to send OSC command', 'error');
            }
        });

        joinSessionBtn.addEventListener('click', async () => {
            const sessionKey = sessionKeyInput.value.trim();
            
            if (!sessionKey || sessionKey.length !== 6) {
                updateClientStatus('Please enter a valid 6-digit session key', 'error');
                return;
            }

            joinSessionBtn.disabled = true;
            updateClientStatus('Joining session...', 'info');
            log(`Client: Attempting to join session ${sessionKey}...`, 'info');

            const result = await clientSessionManager.joinSession(sessionKey, clientUserId.value || null);
            
            if (result.success) {
                updateClientStatus(`Joined session successfully!`, 'success');
                updateClientSessionInfo(result);
                log(`Client: Successfully joined session ${sessionKey}`, 'success');
            } else {
                updateClientStatus(`Failed to join session: ${result.error}`, 'error');
                log(`Client: Failed to join session - ${result.error}`, 'error');
                joinSessionBtn.disabled = false;
            }
        });

        leaveSessionBtn.addEventListener('click', async () => {
            leaveSessionBtn.disabled = true;
            updateClientStatus('Leaving session...', 'info');
            log('Client: Leaving session...', 'info');

            const result = await clientSessionManager.leaveSession();
            
            if (result.success) {
                updateClientStatus('Left session successfully', 'success');
                updateClientSessionInfo(null);
                joinSessionBtn.disabled = false;
                // OSC buttons always enabled for testing
                log('Client: Left session successfully', 'success');
            } else {
                updateClientStatus(`Failed to leave session: ${result.error}`, 'error');
                log(`Client: Failed to leave session - ${result.error}`, 'error');
                leaveSessionBtn.disabled = false;
            }
        });

        clearLogBtn.addEventListener('click', () => {
            activityLog.innerHTML = '';
        });

        // OSC Test Button Event Handlers
        testMuteBtn.addEventListener('click', () => {
            if (!clientSessionManager.sessionKey) {
                log('Client: Cannot send OSC - not in a session', 'error');
                return;
            }
            
            const startTime = performance.now();
            const testCommand = {
                type: 'mute',
                channel: 1,
                action: 'toggle'  // Toggle current state
            };
            
            log('Client: Sending mute test command...', 'info');
            
            if (clientSessionManager.sendOSCCommandWithTimestamp(testCommand, startTime)) {
                log(`CLIENT TEST: Sent mute command for channel 1`, 'success');
            } else {
                log('Client: Failed to send mute test command', 'error');
            }
        });

        testFaderBtn.addEventListener('click', () => {
            if (!clientSessionManager.sessionKey) {
                log('Client: Cannot send OSC - not in a session', 'error');
                return;
            }
            
            log('CLIENT TEST: Starting fader animation on channel 6...', 'info');
            
            // Animate fader for 10 steps like the host version
            let step = 0;
            const maxSteps = 10;
            
            const animate = () => {
                if (step >= maxSteps) {
                    log('CLIENT TEST: Fader animation complete', 'success');
                    return;
                }
                
                const startTime = performance.now();
                const val = Math.random(); // 0.0 to 1.0
                const testCommand = {
                    type: 'fader',
                    channel: 6,
                    value: val
                };
                
                if (clientSessionManager.sendOSCCommandWithTimestamp(testCommand, startTime)) {
                    step++;
                    setTimeout(animate, 100); // 100ms delay between steps
                } else {
                    log('Client: Failed to send fader test command', 'error');
                }
            };
            
            animate();
        });

        // Initial log
        log('VSE Session Management Test initialized', 'success');
    </script>
</body>
</html>
