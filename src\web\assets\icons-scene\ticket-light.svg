<svg width="287" height="101" viewBox="0 0 287 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_347_368961)">
<rect x="5" y="7" width="276" height="86" fill="#F2EDE9"/>
</g>
<g filter="url(#filter1_d_347_368961)">
<path d="M69.1879 38.3599C69.1879 39.1089 69.1286 39.7775 69.0101 40.3657C68.8959 40.9539 68.7097 41.4533 68.4515 41.8638C68.1976 42.2743 67.8633 42.5874 67.4486 42.8032C67.0339 43.019 66.5303 43.127 65.9379 43.127C65.1931 43.127 64.5816 42.9386 64.1034 42.562C63.6252 42.1812 63.2718 41.6353 63.0433 40.9243C62.8148 40.2091 62.7005 39.3543 62.7005 38.3599C62.7005 37.3569 62.8042 36.5 63.0116 35.7891C63.2232 35.0739 63.5659 34.5259 64.0399 34.145C64.5139 33.7642 65.1465 33.5737 65.9379 33.5737C66.6784 33.5737 67.2878 33.7642 67.766 34.145C68.2484 34.5216 68.606 35.0675 68.8387 35.7827C69.0715 36.4937 69.1879 37.3527 69.1879 38.3599ZM64.6493 38.3599C64.6493 39.0666 64.6874 39.6569 64.7635 40.1309C64.8439 40.6006 64.9772 40.9539 65.1634 41.1909C65.3496 41.4279 65.6078 41.5464 65.9379 41.5464C66.2637 41.5464 66.5197 41.43 66.7059 41.1973C66.8963 40.9603 67.0318 40.6069 67.1122 40.1372C67.1926 39.6632 67.2328 39.0708 67.2328 38.3599C67.2328 37.6532 67.1926 37.0628 67.1122 36.5889C67.0318 36.1149 66.8963 35.7594 66.7059 35.5225C66.5197 35.2812 66.2637 35.1606 65.9379 35.1606C65.6078 35.1606 65.3496 35.2812 65.1634 35.5225C64.9772 35.7594 64.8439 36.1149 64.7635 36.5889C64.6874 37.0628 64.6493 37.6532 64.6493 38.3599ZM76.2373 38.3599C76.2373 39.1089 76.1781 39.7775 76.0596 40.3657C75.9453 40.9539 75.7591 41.4533 75.501 41.8638C75.2471 42.2743 74.9128 42.5874 74.498 42.8032C74.0833 43.019 73.5798 43.127 72.9873 43.127C72.2425 43.127 71.631 42.9386 71.1528 42.562C70.6746 42.1812 70.3213 41.6353 70.0928 40.9243C69.8643 40.2091 69.75 39.3543 69.75 38.3599C69.75 37.3569 69.8537 36.5 70.061 35.7891C70.2726 35.0739 70.6154 34.5259 71.0894 34.145C71.5633 33.7642 72.196 33.5737 72.9873 33.5737C73.7279 33.5737 74.3372 33.7642 74.8154 34.145C75.2979 34.5216 75.6554 35.0675 75.8882 35.7827C76.1209 36.4937 76.2373 37.3527 76.2373 38.3599ZM71.6987 38.3599C71.6987 39.0666 71.7368 39.6569 71.813 40.1309C71.8934 40.6006 72.0267 40.9539 72.2129 41.1909C72.3991 41.4279 72.6572 41.5464 72.9873 41.5464C73.3132 41.5464 73.5692 41.43 73.7554 41.1973C73.9458 40.9603 74.0812 40.6069 74.1616 40.1372C74.242 39.6632 74.2822 39.0708 74.2822 38.3599C74.2822 37.6532 74.242 37.0628 74.1616 36.5889C74.0812 36.1149 73.9458 35.7594 73.7554 35.5225C73.5692 35.2812 73.3132 35.1606 72.9873 35.1606C72.6572 35.1606 72.3991 35.2812 72.2129 35.5225C72.0267 35.7594 71.8934 36.1149 71.813 36.5889C71.7368 37.0628 71.6987 37.6532 71.6987 38.3599ZM83.2868 38.3599C83.2868 39.1089 83.2275 39.7775 83.109 40.3657C82.9948 40.9539 82.8086 41.4533 82.5504 41.8638C82.2965 42.2743 81.9622 42.5874 81.5475 42.8032C81.1328 43.019 80.6292 43.127 80.0368 43.127C79.292 43.127 78.6805 42.9386 78.2023 42.562C77.7241 42.1812 77.3707 41.6353 77.1422 40.9243C76.9137 40.2091 76.7995 39.3543 76.7995 38.3599C76.7995 37.3569 76.9031 36.5 77.1105 35.7891C77.3221 35.0739 77.6649 34.5259 78.1388 34.145C78.6128 33.7642 79.2454 33.5737 80.0368 33.5737C80.7773 33.5737 81.3867 33.7642 81.8649 34.145C82.3473 34.5216 82.7049 35.0675 82.9376 35.7827C83.1704 36.4937 83.2868 37.3527 83.2868 38.3599ZM78.7482 38.3599C78.7482 39.0666 78.7863 39.6569 78.8624 40.1309C78.9428 40.6006 79.0761 40.9539 79.2623 41.1909C79.4485 41.4279 79.7067 41.5464 80.0368 41.5464C80.3626 41.5464 80.6186 41.43 80.8048 41.1973C80.9953 40.9603 81.1307 40.6069 81.2111 40.1372C81.2915 39.6632 81.3317 39.0708 81.3317 38.3599C81.3317 37.6532 81.2915 37.0628 81.2111 36.5889C81.1307 36.1149 80.9953 35.7594 80.8048 35.5225C80.6186 35.2812 80.3626 35.1606 80.0368 35.1606C79.7067 35.1606 79.4485 35.2812 79.2623 35.5225C79.0761 35.7594 78.9428 36.1149 78.8624 36.5889C78.7863 37.0628 78.7482 37.6532 78.7482 38.3599Z" fill="#3F3F3F"/>
</g>
<circle cx="204.5" cy="6.5" r="6.5" fill="#C7C7C7"/>
<circle cx="204.5" cy="92.5" r="6.5" fill="#C7C7C7"/>
<circle cx="280.5" cy="93.5" r="6.5" fill="#3F3F3F"/>
<circle cx="280.5" cy="6.5" r="6.5" fill="#3F3F3F"/>
<circle cx="280.5" cy="93.5" r="6.5" fill="#C7C7C7"/>
<circle cx="6.5" cy="93.5" r="6.5" fill="#C7C7C7"/>
<circle cx="280.5" cy="6.5" r="6.5" fill="#C7C7C7"/>
<circle cx="6.5" cy="6.5" r="6.5" fill="#C7C7C7"/>
<g filter="url(#filter2_d_347_368961)">
<line x1="236" y1="31.5" x2="263" y2="31.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter3_d_347_368961)">
<line x1="236" y1="49.5" x2="263" y2="49.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter4_d_347_368961)">
<line x1="236" y1="66.5" x2="263" y2="66.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter5_d_347_368961)">
<line x1="236" y1="46.5" x2="263" y2="46.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter6_d_347_368961)">
<line x1="236" y1="64.5" x2="263" y2="64.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter7_d_347_368961)">
<line x1="236" y1="81.5" x2="263" y2="81.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter8_d_347_368961)">
<line x1="236" y1="24.5" x2="263" y2="24.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter9_d_347_368961)">
<line x1="236" y1="40.5" x2="263" y2="40.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter10_d_347_368961)">
<line x1="236" y1="58.5" x2="263" y2="58.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter11_d_347_368961)">
<line x1="236" y1="75.5" x2="263" y2="75.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter12_d_347_368961)">
<line x1="236" y1="21.5" x2="263" y2="21.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter13_d_347_368961)">
<line x1="236" y1="37.5" x2="263" y2="37.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter14_d_347_368961)">
<line x1="236" y1="55.5" x2="263" y2="55.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter15_d_347_368961)">
<line x1="236" y1="72.5" x2="263" y2="72.5" stroke="#3F3F3F"/>
</g>
<g filter="url(#filter16_d_347_368961)">
<line x1="236" y1="28" x2="263" y2="28" stroke="#3F3F3F" stroke-width="2"/>
</g>
<g filter="url(#filter17_d_347_368961)">
<line x1="236" y1="44" x2="263" y2="44" stroke="#3F3F3F" stroke-width="2"/>
</g>
<g filter="url(#filter18_d_347_368961)">
<line x1="236" y1="62" x2="263" y2="62" stroke="#3F3F3F" stroke-width="2"/>
</g>
<g filter="url(#filter19_d_347_368961)">
<line x1="236" y1="79" x2="263" y2="79" stroke="#3F3F3F" stroke-width="2"/>
</g>
<g filter="url(#filter20_d_347_368961)">
<line x1="236" y1="18" x2="263" y2="18" stroke="#3F3F3F" stroke-width="2"/>
</g>
<g filter="url(#filter21_d_347_368961)">
<line x1="236" y1="34" x2="263" y2="34" stroke="#3F3F3F" stroke-width="2"/>
</g>
<g filter="url(#filter22_d_347_368961)">
<line x1="236" y1="52" x2="263" y2="52" stroke="#3F3F3F" stroke-width="2"/>
</g>
<g filter="url(#filter23_d_347_368961)">
<line x1="236" y1="69" x2="263" y2="69" stroke="#3F3F3F" stroke-width="2"/>
</g>
<g filter="url(#filter24_d_347_368961)">
<line x1="62" y1="47.5" x2="194" y2="47.5" stroke="black" stroke-dasharray="2 2"/>
</g>
<g filter="url(#filter25_d_347_368961)">
<line x1="62" y1="66.5" x2="194" y2="66.5" stroke="black" stroke-dasharray="2 2"/>
</g>
<defs>
<filter id="filter0_d_347_368961" x="1" y="7" width="284" height="94" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter1_d_347_368961" x="58.7004" y="33.5737" width="28.5864" height="17.5532" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter2_d_347_368961" x="232" y="31" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter3_d_347_368961" x="232" y="49" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter4_d_347_368961" x="232" y="66" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter5_d_347_368961" x="232" y="46" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter6_d_347_368961" x="232" y="64" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter7_d_347_368961" x="232" y="81" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter8_d_347_368961" x="232" y="24" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter9_d_347_368961" x="232" y="40" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter10_d_347_368961" x="232" y="58" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter11_d_347_368961" x="232" y="75" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter12_d_347_368961" x="232" y="21" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter13_d_347_368961" x="232" y="37" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter14_d_347_368961" x="232" y="55" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter15_d_347_368961" x="232" y="72" width="35" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter16_d_347_368961" x="232" y="27" width="35" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter17_d_347_368961" x="232" y="43" width="35" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter18_d_347_368961" x="232" y="61" width="35" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter19_d_347_368961" x="232" y="78" width="35" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter20_d_347_368961" x="232" y="17" width="35" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter21_d_347_368961" x="232" y="33" width="35" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter22_d_347_368961" x="232" y="51" width="35" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter23_d_347_368961" x="232" y="68" width="35" height="10" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter24_d_347_368961" x="58" y="47" width="140" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
<filter id="filter25_d_347_368961" x="58" y="66" width="140" height="9" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="4"/>
<feGaussianBlur stdDeviation="2"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_347_368961"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_347_368961" result="shape"/>
</filter>
</defs>
</svg>
