document.querySelectorAll('.display_number').forEach(item => {
    item.addEventListener('click', function () {
        // Get the closest main-effect-board container to scope the operation
        const parentContainer = this.closest('.main-effect-board');

        // Remove 'targeted' class from display_number elements within the same parent container
        parentContainer.querySelectorAll('.display_number').forEach(el => el.classList.remove('targeted'));

        // Add 'targeted' class to the clicked element
        this.classList.add('targeted');

        // Hide all wheel-board elements within the same parent container
        parentContainer.querySelectorAll('.wheel-board').forEach(board => board.classList.remove('active-knob'));

        // Determine which knob should be active based on the targeted display_number child class
        const classList = this.querySelector('input').classList;
        let activeClass = '';
        if (classList.contains('rev_delay')) activeClass = 'rev_delay_knob';
        else if (classList.contains('decay')) activeClass = 'decay_knob';
        else if (classList.contains('roomsize')) activeClass = 'roomsize_knob';
        else if (classList.contains('density')) activeClass = 'density_knob';
        else if (classList.contains('lowfx3')) activeClass = 'lowfx3_knob';
        else if (classList.contains('highfx3')) activeClass = 'highfx3_knob';
        else if (classList.contains('erdelayl')) activeClass = 'erdelayl_knob';
        else if (classList.contains('erdelayr')) activeClass = 'erdelayr_knob';
        else if (classList.contains('lowcut')) activeClass = 'lowcut_knob';
        else if (classList.contains('highcut')) activeClass = 'highcut_knob';
        else if (classList.contains('erlevel')) activeClass = 'erlevel_knob';
        else if (classList.contains('level')) activeClass = 'level_knob';

        // Show the corresponding wheel-board element within the same parent container
        if (activeClass) {
            const activeWheelBoard = parentContainer.querySelector(`.wheel-board.${activeClass}`);
            if (activeWheelBoard) {
                activeWheelBoard.classList.add('active-knob');
            }
        }
    });
});

document.addEventListener('DOMContentLoaded', () => {
    document.querySelectorAll('.main-effect-board').forEach(parentContainer => {
        const wheelKnobs = parentContainer.querySelectorAll('.wheel-knob');

        wheelKnobs.forEach(wheelKnob => {
            const wheelBoard = wheelKnob.closest('.wheel-board');
            if (!wheelBoard) {
                console.error('No parent with class .wheel-board found for a wheel-knob.');
                return;
            }

            const className = [...wheelBoard.classList].find(cls => cls.endsWith('_knob'));
            if (!className) {
                // console.log('No matching class found on wheel-board.');
                return;
            }
            const knobName = className.replace('_knob', '');

            function updateKnobPosition(rotation) {
                const minRotation = -140;
                const maxRotation = 140;
                let minValue, maxValue;

                switch (knobName) {
                    case 'rev_delay':
                        minValue = 0;
                        maxValue = 200;
                        break;
                    case 'decay':
                        minValue = 0.10;
                        maxValue = 20.00;
                        break;
                    case 'roomsize':
                        minValue = 2;
                        maxValue = 100;
                        break;
                    case 'density':
                        minValue = 1;
                        maxValue = 30;
                        break;
                    case 'lowfx3':
                        minValue = 0.10;
                        maxValue = 10.00;
                        break;
                    case 'highfx3':
                        minValue = 0.10;
                        maxValue = 10.00;
                        break;
                    case 'erdelayl':
                        minValue = 0;
                        maxValue = 200;
                        break;
                    case 'erdelayr':
                        minValue = 0;
                        maxValue = 200;
                        break;
                    case 'lowcut':
                        minValue = 10;
                        maxValue = 500;
                        break;
                    case 'highcut':
                        minValue = 0.20;
                        maxValue = 20.00;
                        break;
                    case 'erlevel':
                        minValue = 0;
                        maxValue = 100;
                        break;
                    case 'level':
                        minValue = -12.00;
                        maxValue = 12.00;
                        break;
                    default:
                        return;
                }

                let value = ((rotation - minRotation) * (maxValue - minValue) / (maxRotation - minRotation)) + minValue;
                if (value > maxValue) value = maxValue;
                if (value < minValue) value = minValue;

                const targetedDisplay = parentContainer.querySelector(`.display_number.targeted .${knobName}`);
                if (targetedDisplay) {
                    if (knobName === 'decay' || knobName === 'highcut') {
                        targetedDisplay.value = Math.min(value.toFixed(2), 20.00);
                    } else if (knobName === 'lowfx3' || knobName === 'highfx3' || knobName === 'level') {
                        targetedDisplay.value = value.toFixed(2);
                    } else {
                        targetedDisplay.value = Math.round(value);
                    }
                }
            }

            function handleKnobMovement() {
                const transform = window.getComputedStyle(wheelKnob).transform;
                if (transform !== 'none') {
                    const matrixValues = transform.split('(')[1].split(')')[0].split(',');
                    const a = matrixValues[0];
                    const b = matrixValues[1];
                    const rotation = Math.round(Math.atan2(b, a) * (180 / Math.PI));

                    updateKnobPosition(rotation);
                }
            }

            // Initial update
            handleKnobMovement();

            // Observe changes to the knob's transform style
            const observer = new MutationObserver(handleKnobMovement);
            observer.observe(wheelKnob, { attributes: true, attributeFilter: ['style'] });
        });
    });
});




// with tooltip
$(document).ready(function () {
    $(".effects-dragger").each(function () {
        $(this).draggable({
            axis: "y",
            containment: $(this).parent(),
            drag: function (event, ui) {
                // Function to handle both mouse and touch events
                handleDrag($(this), ui);
            },
            touch: function (event, ui) {
                // Function to handle only touch events
                handleDrag($(this), ui);
            }
        });
    });

    // Function to handle both mouse and touch events
    function handleDrag(element, ui) {
        var channel = element.closest('.mixer-wrap').data('channel');
        var containerHeight = element.parent().height();
        var draggerHeight = element.height();
        var draggerPosition = ui.position.top;
    }
});


// screen 27 modulation numbers updates
document.addEventListener('DOMContentLoaded', function () {
    // Define min and max rotation values
    const minRotation = -140;
    const maxRotation = 140;

    // Define the range for each knob type
    const knobConfig = {
        'knob-time': { inputClass: 'time_modulation', minValue: 1.00, maxValue: 3000 },
        'knob-rate': { inputClass: 'rate_modulation', minValue: 0.05, maxValue: 10.00 },
        'knob-amb': { inputClass: 'amb_modulation', minValue: 1.0, maxValue: 10.0, step: 0.5 } // Adding step for amb
    };

    // Function to update input value based on knob rotation
    function updateModulationValue(knobType, rotation, parentContainer) {
        const config = knobConfig[knobType];
        if (!config) return;

        let value = ((rotation - minRotation) * (config.maxValue - config.minValue) / (maxRotation - minRotation)) + config.minValue;

        // Clamp value within min and max
        value = Math.max(config.minValue, Math.min(config.maxValue, value));

        // If the knob is for 'amb_modulation', round the value to the nearest 0.5
        if (knobType === 'knob-amb') {
            value = Math.round(value / config.step) * config.step;
        }

        const inputField = parentContainer.querySelector(`input.${config.inputClass}`);
        if (inputField) {
            // Set value formatting based on knob type
            if (knobType === 'knob-rate') {
                inputField.value = value.toFixed(2); // Show two decimal places for rate
            } else if (knobType === 'knob-time') {
                inputField.value = value < 100 ? value.toFixed(2) : Math.round(value); // Show decimals below 100, round above 100
            } else if (knobType === 'knob-amb') {
                inputField.value = value.toFixed(1); // Show one decimal place for amb
            }
        }
    }

    // Function to handle knob movement
    function handleModulationKnobMovement(knob, parentContainer) {
        const transform = window.getComputedStyle(knob).transform;
        if (transform !== 'none') {
            const matrixValues = transform.split('(')[1].split(')')[0].split(',');
            const a = matrixValues[0];
            const b = matrixValues[1];
            const rotation = Math.round(Math.atan2(b, a) * (180 / Math.PI));

            // Find the corresponding configuration based on the knob's class
            for (let knobType in knobConfig) {
                if (knob.classList.contains(knobType)) {
                    updateModulationValue(knobType, rotation, parentContainer);
                    break;
                }
            }
        }
    }

    // Initialize the modulation knobs for each .modulation_screen instance
    document.querySelectorAll('.modulation_screen').forEach(screen => {
        const modulationKnobs = screen.querySelectorAll('.knob-time, .knob-rate, .knob-amb');

        modulationKnobs.forEach(knob => {
            // Initial update based on default rotation
            handleModulationKnobMovement(knob, screen);

            // Observe changes to the knob's transform style
            const observer = new MutationObserver(() => handleModulationKnobMovement(knob, screen));
            observer.observe(knob, { attributes: true, attributeFilter: ['style'] });
        });
    });
});

//screen 25 and 26  
document.addEventListener('DOMContentLoaded', function () {
    // Define min and max rotation values
    const minRotation = -140;
    const maxRotation = 140;

    // Define the range for each knob type with the updated values using existing class names
    const knobConfig = {
        'time_chorus_knob': { inputClass: 'time_chorus', minValue: 1.00, maxValue: 3000 },
        'pattn_chorus_knob': {
            inputClass: 'pattn_chorus',
            minValue: -1.00,
            maxValue: 0.25,
            steps: [0.25, 0.33, 0.37, 0.50, 0.67, 0.75, 1.00, -0.25, -0.33, -0.38, -0.50, -0.67, -0.75, -1.00]
        },
        'feedhc_chorus_knob': { inputClass: 'feedhc_chorus', minValue: 1.00, maxValue: 20.00 },
        'feed_chorus_knob': { inputClass: 'feed_chorus', minValue: 0, maxValue: 100 },
        'xfeed_chorus_knob': { inputClass: 'xfeed_chorus', minValue: 0, maxValue: 100 },
        'bal_chorus_knob': { inputClass: 'bal_chorus', minValue: -100, maxValue: 100 },
        'speed_chorus_knob': { inputClass: 'speed_chorus', minValue: 0.05, maxValue: 4.00 },
        'depth_chorus_knob': { inputClass: 'depth_chorus', minValue: 0, maxValue: 100 },
        'delay_chorus_knob': { inputClass: 'delay_chorus', minValue: 0.50, maxValue: 50.00 },
        'phase_chorus_knob': { inputClass: 'phase_chorus', minValue: 0, maxValue: 180 },
        'wave_chorus_knob': { inputClass: 'wave_chorus', minValue: 0, maxValue: 100 }
    };

    // Function to update input value based on knob rotation
    function updateChorusValue(knobClass, rotation, parentContainer) {
        const config = knobConfig[knobClass];
        if (!config) return;

        let value;

        if (knobClass === 'pattn_chorus_knob') {
            // Handle 'pattn' knob using predefined steps
            const stepIndex = Math.round((rotation - minRotation) / (maxRotation - minRotation) * (config.steps.length - 1));
            value = config.steps[Math.max(0, Math.min(config.steps.length - 1, stepIndex))];
        } else {
            // Normal linear interpolation for other knobs
            value = ((rotation - minRotation) * (config.maxValue - config.minValue) / (maxRotation - minRotation)) + config.minValue;
            value = Math.max(config.minValue, Math.min(config.maxValue, value));
        }

        const inputField = parentContainer.querySelector(`input.${config.inputClass}`);
        if (inputField) {
            // Set value formatting based on specific needs
            if (knobClass === 'time_chorus_knob' && value >= 100) {
                inputField.value = Math.round(value); // Time: No decimals above 100
            } else if (knobClass === 'speed_chorus_knob' || knobClass === 'feedhc_chorus_knob') {
                inputField.value = value.toFixed(2); // Speed and FeedHC: Two decimal places
            } else if (knobClass === 'delay_chorus_knob') {
                inputField.value = value.toFixed(2); // Delay: Two decimal places
            } else if (knobClass === 'pattn_chorus_knob') {
                inputField.value = value.toFixed(2); // Pattn: Two decimal places
            } else if (knobClass === 'phase_chorus_knob') {
                inputField.value = value.toFixed(1); // Phase: One decimal place
            } else {
                inputField.value = value.toFixed(0); // Default: No decimal places
            }
        }
    }

    // Function to handle knob movement
    function handleChorusKnobMovement(knob, parentContainer) {
        const transform = window.getComputedStyle(knob).transform;
        if (transform !== 'none') {
            const matrixValues = transform.split('(')[1].split(')')[0].split(',');
            const a = matrixValues[0];
            const b = matrixValues[1];
            const rotation = Math.round(Math.atan2(b, a) * (180 / Math.PI));

            // Use the knob's class to find the corresponding config
            for (let knobClass in knobConfig) {
                if (knob.classList.contains(knobClass)) {
                    updateChorusValue(knobClass, rotation, parentContainer);
                    break;
                }
            }
        }
    }

    // Initialize the chorus knobs for each .fx_delay instance
    document.querySelectorAll('.fx_delay').forEach(parentContainer => {
        const chorusKnobs = parentContainer.querySelectorAll('.time_chorus_knob, .pattn_chorus_knob, .feedhc_chorus_knob, .feed_chorus_knob, .xfeed_chorus_knob, .bal_chorus_knob, .speed_chorus_knob, .depth_chorus_knob, .delay_chorus_knob, .phase_chorus_knob, .wave_chorus_knob');

        chorusKnobs.forEach(knob => {
            // Initial update based on default rotation
            handleChorusKnobMovement(knob, parentContainer);

            // Observe changes to the knob's transform style
            const observer = new MutationObserver(() => handleChorusKnobMovement(knob, parentContainer));
            observer.observe(knob, { attributes: true, attributeFilter: ['style'] });
        });
    });
});

// flanger one
document.addEventListener('DOMContentLoaded', function () {
    // Define min and max rotation values
    const minRotation = -140;
    const maxRotation = 140;

    // Define the range for each knob type with the updated values using flanger-related class names
    const knobConfig = {
        'time_dual_flang': { inputClass: 'time_flang', minValue: 1.00, maxValue: 3000 },
        'pattn_dual_flang': {
            inputClass: 'pattn_flang',
            minValue: -1.00,
            maxValue: 0.25,
            steps: [0.25, 0.33, 0.37, 0.50, 0.67, 0.75, 1.00, -0.25, -0.33, -0.38, -0.50, -0.67, -0.75, -1.00]
        },
        'feedhc_dual_flang': { inputClass: 'feedhc_flang', minValue: 1.00, maxValue: 20.00 },
        'feed_1_dual_flang': { inputClass: 'feed_1_flang', minValue: 0, maxValue: 100 },
        'xfeed_dual_flang': { inputClass: 'xfeed_flang', minValue: 0, maxValue: 100 },
        'bal_dual_flang': { inputClass: 'bal_flang', minValue: -100, maxValue: 100 },
        'speed_dual_flang': { inputClass: 'speed_flang', minValue: 0.05, maxValue: 4.00 },
        'depth_dual_flang': { inputClass: 'depth_flang', minValue: 0, maxValue: 100 },
        'delay_dual_flang': { inputClass: 'delay_flang', minValue: 0.50, maxValue: 50.00 },
        'phase_dual_flang': { inputClass: 'phase_flang', minValue: 0, maxValue: 180 },
        'feed_dual_flang': { inputClass: 'feed_flang', minValue: 0, maxValue: 100 }
    };

    // Function to update input value based on knob rotation
    function updateFlangerValue(knobClass, rotation, parentContainer) {
        const config = knobConfig[knobClass];
        if (!config) return;

        let value;

        if (knobClass === 'pattn_dual_flang') {
            // Handle 'pattn' knob using predefined steps
            const stepIndex = Math.round((rotation - minRotation) / (maxRotation - minRotation) * (config.steps.length - 1));
            value = config.steps[Math.max(0, Math.min(config.steps.length - 1, stepIndex))];
        } else {
            // Normal linear interpolation for other knobs
            value = ((rotation - minRotation) * (config.maxValue - config.minValue) / (maxRotation - minRotation)) + config.minValue;
            value = Math.max(config.minValue, Math.min(config.maxValue, value));
        }

        const inputField = parentContainer.querySelector(`input.${config.inputClass}`);
        if (inputField) {
            // Set value formatting based on specific needs
            if (knobClass === 'time_dual_flang' && value >= 100) {
                inputField.value = Math.round(value); // Time: No decimals above 100
            } else if (knobClass === 'speed_dual_flang' || knobClass === 'feedhc_dual_flang') {
                inputField.value = value.toFixed(2); // Speed and FeedHC: Two decimal places
            } else if (knobClass === 'delay_dual_flang') {
                inputField.value = value.toFixed(2); // Delay: Two decimal places
            } else if (knobClass === 'pattn_dual_flang') {
                inputField.value = value.toFixed(2); // Pattn: Two decimal places
            } else if (knobClass === 'phase_dual_flang') {
                inputField.value = value.toFixed(1); // Phase: One decimal place
            } else {
                inputField.value = value.toFixed(0); // Default: No decimal places
            }
        }
    }

    // Function to handle knob movement
    function handleFlangerKnobMovement(knob, parentContainer) {
        const transform = window.getComputedStyle(knob).transform;
        if (transform !== 'none') {
            const matrixValues = transform.split('(')[1].split(')')[0].split(',');
            const a = matrixValues[0];
            const b = matrixValues[1];
            const rotation = Math.round(Math.atan2(b, a) * (180 / Math.PI));

            // Use the knob's class to find the corresponding config
            for (let knobClass in knobConfig) {
                if (knob.classList.contains(knobClass)) {
                    updateFlangerValue(knobClass, rotation, parentContainer);
                    break;
                }
            }
        }
    }

    // Initialize the flanger knobs for each .fx_delay instance
    document.querySelectorAll('.fx_delay').forEach(parentContainer => {
        const flangerKnobs = parentContainer.querySelectorAll('.time_dual_flang, .pattn_dual_flang, .feedhc_dual_flang, .feed_1_dual_flang, .xfeed_dual_flang, .bal_dual_flang, .speed_dual_flang, .depth_dual_flang, .delay_dual_flang, .phase_dual_flang, .feed_dual_flang');

        flangerKnobs.forEach(knob => {
            // Initial update based on default rotation
            handleFlangerKnobMovement(knob, parentContainer);

            // Observe changes to the knob's transform style
            const observer = new MutationObserver(() => handleFlangerKnobMovement(knob, parentContainer));
            observer.observe(knob, { attributes: true, attributeFilter: ['style'] });
        });
    });
});

// screen 40 and 41 animations
document.addEventListener('DOMContentLoaded', function () {
    // Function to update active light based on dropdown button state for the first block
    function updateActiveLightFirstBlock(controlWrapper) {
        // Only target dropdown-options within the specific block
        const dropdownOptions = controlWrapper.querySelectorAll('.pick_for_drop_select .dropdown-option');

        dropdownOptions.forEach(option => {
            option.addEventListener('click', function () {
                const btnBlock = option.querySelector('.btn-block');
                let optionType = '';

                // Determine the option type based on the specific dropdown
                if (option.classList.contains('byp_drop')) {
                    optionType = 'byp_opt';
                } else if (option.classList.contains('solo_drop')) {
                    optionType = 'solo_opt';
                } else if (option.classList.contains('db_drop')) {
                    optionType = 'db48_opt';
                } else if (option.classList.contains('sbc_drop')) {
                    optionType = 'sbc_opt';
                }

                // Update active light based on the clicked btn-block
                if (optionType) {
                    const targetElement = controlWrapper.querySelector(`.single_opt_color.${optionType}`);

                    // Add or remove active_light based on btn-block active state
                    if (btnBlock.classList.contains('active-btn-background') && btnBlock.classList.contains('active-btn-shadow')) {
                        targetElement.classList.add('active_light');
                    } else {
                        targetElement.classList.remove('active_light');
                    }
                }
            });
        });
    }

    // Initialize only within the .control_spec_wrapper
    document.querySelectorAll('.control_spec_wrapper').forEach(controlWrapper => {
        updateActiveLightFirstBlock(controlWrapper);
    });
});
document.addEventListener('DOMContentLoaded', function () {
    // Function to update active light based on dropdown button state for the second block
    function updateActiveLightSecondBlock(controlWrapper) {
        // Only target dropdown-options within the specific block
        const dropdownOptions = controlWrapper.querySelectorAll('.pick_for_drop_select2 .dropdown-option');

        dropdownOptions.forEach(option => {
            option.addEventListener('click', function () {
                const btnBlock = option.querySelector('.btn-block');
                let optionType = '';

                // Determine the option type based on the specific dropdown for the second block
                if (option.classList.contains('byp_drop2')) {
                    optionType = 'byp2_opt';
                } else if (option.classList.contains('solo_drop2')) {
                    optionType = 'solo2_opt';
                } else if (option.classList.contains('db_drop2')) {
                    optionType = 'db482_opt';
                } else if (option.classList.contains('sbc_drop2')) {
                    optionType = 'sbc2_opt';
                }

                // Update active light based on the clicked btn-block
                if (optionType) {
                    const targetElement = controlWrapper.querySelector(`.single_opt_color.${optionType}`);

                    // Add or remove active_light based on btn-block active state
                    if (btnBlock.classList.contains('active-btn-background') && btnBlock.classList.contains('active-btn-shadow')) {
                        targetElement.classList.add('active_light');
                    } else {
                        targetElement.classList.remove('active_light');
                    }
                }
            });
        });
    }

    // Initialize only within the .control_spec_wrapper_b
    document.querySelectorAll('.control_spec_wrapper_b').forEach(controlWrapper => {
        updateActiveLightSecondBlock(controlWrapper);
    });
});
// xo select
document.addEventListener('DOMContentLoaded', function () {
    // Function to update active state for xover options based on dropdown selection
    function updateXoverActiveState(combinatorWrapper) {
        // Only target dropdown-options within .wrap_xover_drop
        const dropdownOptions = combinatorWrapper.querySelectorAll('.wrap_xover_drop .dropdown-option');

        dropdownOptions.forEach(option => {
            option.addEventListener('click', function () {
                // Determine the option type based on classes
                let optionType = '';

                if (option.classList.contains('high_drop')) {
                    optionType = 'high_col';
                } else if (option.classList.contains('himid_drop')) {
                    optionType = 'himid_col';
                } else if (option.classList.contains('mid_drop')) {
                    optionType = 'mid_col';
                } else if (option.classList.contains('lomid_drop')) {
                    optionType = 'lomid_col';
                } else if (option.classList.contains('low_drop')) {
                    optionType = 'low_col';
                }

                // Check if optionType is valid before proceeding
                if (optionType) {
                    // Remove active_xo and active_light from all .single_xo_color elements
                    const allXoverOptions = combinatorWrapper.querySelectorAll('.single_xo_color');
                    allXoverOptions.forEach(el => el.classList.remove('active_xo', 'active_light'));

                    // Add active_xo to the matched option
                    const targetElement = combinatorWrapper.querySelector(`.single_xo_color.${optionType}`);
                    if (targetElement) {
                        targetElement.classList.add('active_xo');

                        // Check if red_active should be applied
                        const redBtn = combinatorWrapper.querySelector('.float_lock_btn .add_lock_color_xo');
                        if (redBtn && redBtn.classList.contains('red_active')) {
                            targetElement.classList.add('active_light');
                        }
                    }

                    // Match the corresponding wheel-board to display
                    const allKnobOptions = combinatorWrapper.querySelectorAll('.wheel-board');
                    allKnobOptions.forEach(el => el.classList.remove('active_knob'));

                    const knobClass = optionType.replace('_col', '_knob_select');
                    const targetKnob = combinatorWrapper.querySelector(`.wheel-board.${knobClass}`);
                    if (targetKnob) {
                        targetKnob.classList.add('active_knob');
                    }
                }
            });
        });

        // Add event listener for the red-btn-in button
        const redBtn = combinatorWrapper.querySelector('.float_lock_btn .add_lock_color_xo');
        if (redBtn) {
            redBtn.addEventListener('click', function () {
                redBtn.classList.toggle('red_active');

                // Find the currently active .single_xo_color element
                const activeXover = combinatorWrapper.querySelector('.single_xo_color.active_xo');
                if (activeXover) {
                    if (redBtn.classList.contains('red_active')) {
                        activeXover.classList.add('active_light');
                    } else {
                        activeXover.classList.remove('active_light');
                    }
                }
            });
        }
    }

    // Initialize the function for all instances of combinator_main_animate
    document.querySelectorAll('.combinator_main_animate').forEach(combinatorWrapper => {
        updateXoverActiveState(combinatorWrapper);
    });
});

// update which child is active active_light class to be added on knobs movement
document.addEventListener('DOMContentLoaded', function () {
    // Define the rotation range for the knobs
    const minRotation = -140; // Minimum rotation angle (knob at the far left)
    const maxRotation = 140;  // Maximum rotation angle (knob at the far right)

    // Function to update lights based on knob rotation
    function updateLights(knob, lights) {
        const step = (maxRotation - minRotation) / (lights.length - 1); // Calculate step size for lights
        const transform = window.getComputedStyle(knob).transform;
        let angle = -140; // Default angle

        if (transform !== 'none') {
            const values = transform.split('(')[1].split(')')[0].split(',');
            const a = values[0];
            const b = values[1];
            angle = Math.round(Math.atan2(b, a) * (180 / Math.PI));
        }

        // Calculate the index of the active light
        let activeIndex = Math.round((angle - minRotation) / step);

        // Make sure activeIndex is within bounds and adjust for reverse order
        activeIndex = lights.length - 1 - Math.max(0, Math.min(activeIndex, lights.length - 1));

        // Update the lights
        lights.forEach((light, index) => {
            if (index === activeIndex) {
                light.classList.add('active_light');
            } else {
                light.classList.remove('active_light');
            }
        });
    }

    // Function to initialize the lights with the bottom one active by default
    function initializeDefaultLights(mainWrapper) {
        const reductionCols = mainWrapper.querySelectorAll('.single_reduction_col .col_filler');

        reductionCols.forEach(col => {
            const lights = col.querySelectorAll('.color_item_stop');
            if (lights.length > 0) {
                // Remove active_light from all lights
                lights.forEach(light => light.classList.remove('active_light'));

                // Add active_light to the last light (bottom one)
                lights[lights.length - 1].classList.add('active_light');
            }
        });
    }

    // Attach the event listener to each knob
    function attachKnobListeners(mainWrapper) {
        // Only select knobs under the .multi_select_knob parent
        const knobs = mainWrapper.querySelectorAll('.multi_select_knob .wheel-knob');

        knobs.forEach(knob => {
            // Listener to handle transformation changes
            const handleTransformation = function () {
                // Determine which set of lights to update based on the knob's class
                let lights = [];
                if (knob.classList.contains('combinator-high')) {
                    lights = mainWrapper.querySelectorAll('.high_col_stop .color_item_stop');
                } else if (knob.classList.contains('combinator-himid')) {
                    lights = mainWrapper.querySelectorAll('.himid_col_stop .color_item_stop');
                } else if (knob.classList.contains('combinator-mid')) {
                    lights = mainWrapper.querySelectorAll('.mid_col_stop .color_item_stop');
                } else if (knob.classList.contains('combinator-lomid')) {
                    lights = mainWrapper.querySelectorAll('.lomid_col_stop .color_item_stop');
                } else if (knob.classList.contains('combinator-low')) {
                    lights = mainWrapper.querySelectorAll('.low_col_stop .color_item_stop');
                }

                // Update lights based on knob position
                if (lights.length > 0) {
                    updateLights(knob, lights);
                }
            };

            // Attach an observer to detect rotation changes
            const observer = new MutationObserver(handleTransformation);
            observer.observe(knob, { attributes: true, attributeFilter: ['style'] });

            // Initialize the lights based on the knob's current rotation
            handleTransformation();
        });
    }

    // Initialize all instances within .combinator_main_animate
    document.querySelectorAll('.combinator_main_animate').forEach(mainWrapper => {
        initializeDefaultLights(mainWrapper);
        attachKnobListeners(mainWrapper);
    });
});
// progress bar change

document.addEventListener('DOMContentLoaded', function () {
    // Define the rotation range for the knob
    const minRotation = -140; // Minimum rotation angle (knob at the far left)
    const maxRotation = 140;  // Maximum rotation angle (knob at the far right)

    // Default and target width configurations
    const defaultWidths = {
        low: 13,
        lomid: 13,
        mid: 17,
        himid: 24,
        high: 33
    };

    const targetWidths = {
        low: 35,
        lomid: 23,
        mid: 18,
        himid: 14,
        high: 10
    };

    // Function to interpolate between default and target widths
    function interpolateWidth(start, end, factor) {
        return start + (end - start) * factor;
    }

    // Function to update progress bars based on knob rotation
    function updateProgressBars(knob, progressBars) {
        const transform = window.getComputedStyle(knob).transform;
        let angle = -140; // Default angle

        if (transform !== 'none') {
            const values = transform.split('(')[1].split(')')[0].split(',');
            const a = values[0];
            const b = values[1];
            angle = Math.round(Math.atan2(b, a) * (180 / Math.PI));
        }

        // Calculate the factor of knob rotation in the range [0, 1]
        const factor = (angle - minRotation) / (maxRotation - minRotation);

        // Update each progress bar width
        progressBars.low.style.width = `${interpolateWidth(defaultWidths.low, targetWidths.low, factor)}%`;
        progressBars.lomid.style.width = `${interpolateWidth(defaultWidths.lomid, targetWidths.lomid, factor)}%`;
        progressBars.mid.style.width = `${interpolateWidth(defaultWidths.mid, targetWidths.mid, factor)}%`;
        progressBars.himid.style.width = `${interpolateWidth(defaultWidths.himid, targetWidths.himid, factor)}%`;
        progressBars.high.style.width = `${interpolateWidth(defaultWidths.high, targetWidths.high, factor)}%`;
    }

    // Attach event listener to the knob
    function attachKnobListener(mainAnimateWrapper) {
        const knob = mainAnimateWrapper.querySelector('.xover_length_adjustment');
        const progressBars = {
            low: mainAnimateWrapper.querySelector('.low_progress'),
            lomid: mainAnimateWrapper.querySelector('.lomid_progress'),
            mid: mainAnimateWrapper.querySelector('.mid_progress'),
            himid: mainAnimateWrapper.querySelector('.himid_progress'),
            high: mainAnimateWrapper.querySelector('.high_progress')
        };

        // Listener to handle knob rotation changes
        const handleKnobRotation = function () {
            updateProgressBars(knob, progressBars);
        };

        // Attach an observer to detect rotation changes
        const observer = new MutationObserver(handleKnobRotation);
        observer.observe(knob, { attributes: true, attributeFilter: ['style'] });

        // Initialize the progress bars based on the knob's current rotation
        handleKnobRotation();
    }

    // Initialize all instances within .combinator_main_animate
    document.querySelectorAll('.combinator_main_animate').forEach(mainAnimateWrapper => {
        attachKnobListener(mainAnimateWrapper);
    });
});

// end screen 40 and 41
// POPUPS FOR ADDITIONAL SCREENS and Closing rules
document.addEventListener('DOMContentLoaded', () => {
    let currentEffect = null;

    // Function to open the popup and set the current effect
    const openPopup = (popupId, effectId) => {
        currentEffect = effectId;
        document.getElementById(popupId).style.display = 'block';
    };

    // Function to close the popup
    const closePopup = (popupId) => {
        document.getElementById(popupId).style.display = 'none';
    };

    // Function to switch the content based on the clicked image in the popup
    const switchContent = (screenNumber) => {
        if (!currentEffect) {
            console.error('Current effect is not set');
            return;
        }

        const parentTabContent = document.getElementById(`add-screen-wrapper_${currentEffect}`);
        if (!parentTabContent) {
            console.error('Parent tab content not found');
            return;
        }

        // Debugging: Log currentEffect and screenNumber
        console.log(`Switching content for effect: ${currentEffect}, screen: ${screenNumber}`);

        // Hide all screens dynamically by finding elements that match the pattern
        const allScreens = parentTabContent.querySelectorAll(`[class*="${currentEffect}_add-screen-"][class*="-present"]`);
        allScreens.forEach(content => {
            content.classList.remove('active_effect-screen');
            content.classList.add('inactive_effect-screen');
        });

        // Show the selected screen
        const selectedScreen = parentTabContent.querySelector(`.${currentEffect}_add-screen-${screenNumber}-present`);
        if (selectedScreen) {
            selectedScreen.classList.add('active_effect-screen');
            selectedScreen.classList.remove('inactive_effect-screen');
        } else {
            console.error(`Selected screen not found: .${currentEffect}_add-screen-${screenNumber}-present`);
        }
    };

    // Handle effect button clicks to show the popup and set the current effect
    document.querySelectorAll('.insert-btn-drop.effect-popup').forEach(button => {
        button.addEventListener('click', () => {
            const popupId = button.getAttribute('data-popup');
            const effectId = button.id.split('-')[1];
            openPopup(popupId, effectId);
        });
    });

    // Handle screen clicks within the popup to show the correct effect screen content and close the popup
    document.querySelectorAll('.add-screen').forEach(screen => {
        screen.addEventListener('click', function () {
            const screenNumber = this.getAttribute('data-screen');
            switchContent(screenNumber);

            // Fade out the popup
            const popup = this.closest('.custom-popup');
            if (popup) {
                closePopup(popup.id);
            }
        });
    });
});

// jQuery code for handling popups
$(document).ready(function () {
    let swipers = {};

    $('.insert-btn-drop').on('click', function () {
        var popupId = $(this).data('popup');
        $('#' + popupId).fadeIn(400, function () {
            if (!swipers[popupId]) {
                swipers[popupId] = new Swiper('#' + popupId + ' .swiper-container', {
                    loop: false,
                    pagination: {
                        el: '.swiper-pagination',
                        clickable: true,
                    },
                    navigation: false,
                    slidesPerView: 1,
                    spaceBetween: 10,
                });
            }
        });
    });

    $(document).on('click', function (event) {
        if ($(event.target).closest('.custom-popup-content').length === 0 && $(event.target).closest('.insert-btn-drop').length === 0) {
            $('.custom-popup').fadeOut();
        }
    });

    // Handle image click to fade out popups
    $('.add-screen').on('click', function () {
        $('.custom-popup').fadeOut();
    });
});

// additional screens animations
document.addEventListener('DOMContentLoaded', function () {
    const images = document.querySelectorAll('.switcher_track_img');

    images.forEach(image => {
        let startY = 0;
        let offsetY = 0;

        // Define the snap points
        const snapPoints = [0, 25, 45];

        image.addEventListener('mousedown', startDrag);
        image.addEventListener('touchstart', startDrag);

        function startDrag(e) {
            e.preventDefault();
            startY = e.clientY || e.touches[0].clientY;
            offsetY = image.offsetTop;

            document.addEventListener('mousemove', drag);
            document.addEventListener('touchmove', drag);
            document.addEventListener('mouseup', stopDrag);
            document.addEventListener('touchend', stopDrag);
        }

        function drag(e) {
            const clientY = e.clientY || e.touches[0].clientY;
            let newY = offsetY + (clientY - startY);
            const containerHeight = image.parentElement.clientHeight;
            const imageHeight = image.clientHeight;

            // Ensure the image stays within the bounds of the container
            if (newY < 0) newY = 0;
            if (newY > containerHeight - imageHeight) newY = containerHeight - imageHeight;

            image.style.top = `${newY}px`;
        }

        function stopDrag() {
            document.removeEventListener('mousemove', drag);
            document.removeEventListener('touchmove', drag);
            document.removeEventListener('mouseup', stopDrag);
            document.removeEventListener('touchend', stopDrag);

            // Snap to nearest snap point
            snapToClosestPoint();
        }

        function snapToClosestPoint() {
            const currentPosition = image.offsetTop;
            let closestPoint = snapPoints[0];
            let closestDistance = Math.abs(currentPosition - snapPoints[0]);

            snapPoints.forEach(point => {
                const distance = Math.abs(currentPosition - point);
                if (distance < closestDistance) {
                    closestDistance = distance;
                    closestPoint = point;
                }
            });

            // Snap the image to the closest point
            image.style.top = `${closestPoint}px`;
        }
    });
});



// Function to toggle .red_active class on all .red-btn-in elements
// document.querySelectorAll('.red-btn-in').forEach(redbuttons => {
//     redbuttons.addEventListener('click', function () {
        
//         this.classList.toggle('red_active');
//     });
// });

// draggable items reset screens 28-31
document.querySelectorAll('.reverse.reset_eq').forEach((resetButton) => {
    resetButton.addEventListener('click', function () {
        // Find the closest parent with the class 'stero_eq_and_duals'
        const parent = this.closest('.stero_eq_and_duals');

        if (parent) {
            // Select all elements with class 'effects-dragger' within this parent
            const draggers = parent.querySelectorAll('.effects-dragger');

            // Reset the position of each dragger element
            draggers.forEach((dragger) => {
                dragger.style.top = '-96px';
                dragger.style.left = '-9px';
            });
        }
    });
});

// dimensional screen 17
// Function to toggle active classes for standalone buttons
// Function to toggle button styles for standalone buttons
function toggleStandaloneButton(button) {
    button.classList.toggle('active-btn-background');
    button.classList.toggle('active-btn-shadow');
}

// Function to manage mode buttons' behavior
function handleModeButtons(clickedButton, parentContainer) {
    const offButton = parentContainer.querySelector('.mode_btn.off_active');
    const modeButtons = parentContainer.querySelectorAll('.mode_btn.mode_1, .mode_btn.mode_2, .mode_btn.mode_3, .mode_btn.mode_4');

    if (clickedButton === offButton) {
        // Toggle the OFF button
        offButton.classList.toggle('active-btn-background');
        offButton.classList.toggle('active-btn-shadow');

        // If OFF button is active, deactivate all other modes
        if (offButton.classList.contains('active-btn-background')) {
            modeButtons.forEach(button => {
                button.classList.remove('active-btn-background', 'active-btn-shadow');
            });
        }
    } else {
        // Toggle the clicked mode button
        clickedButton.classList.toggle('active-btn-background');
        clickedButton.classList.toggle('active-btn-shadow');

        // If any mode button 1-4 is active, deactivate the OFF button
        if (clickedButton.classList.contains('active-btn-background')) {
            offButton.classList.remove('active-btn-background', 'active-btn-shadow');
        }
    }
}

// Initialize buttons within each .main-mode-wrap container
document.querySelectorAll('.main-mode-wrap').forEach(parentContainer => {
    // Event listeners for standalone buttons
    parentContainer.querySelectorAll('.stereo_btn, .dry_btn, .power_btn').forEach(button => {
        button.addEventListener('click', function () {
            toggleStandaloneButton(this);
        });
    });

    // Event listeners for mode buttons
    parentContainer.querySelectorAll('.mode_btn').forEach(button => {
        button.addEventListener('click', function () {
            handleModeButtons(this, parentContainer);
        });
    });
});


// TAP screens 12 
document.querySelectorAll('.btn_dry_tap').forEach(button => {
    button.addEventListener('click', function () {
        this.classList.toggle('active-btn-background');
        this.classList.toggle('active-btn-shadow');
    });
});
// TAP screens  13
document.querySelectorAll('.btn_dry_tap.circle_btn ').forEach(button => {
    button.addEventListener('click', function () {
        this.classList.toggle('active-btn-background-tap');
    });
});


// screen 7 VINTAGE
document.addEventListener('DOMContentLoaded', function () {
    // Select all swap_vintage_control elements
    const swapControls = document.querySelectorAll('.swap_vintage_control');

    // Loop through each instance of swap_vintage_control
    swapControls.forEach(function (swapControl) {
        // Find the buttons for toggling within the current instance
        const btn1 = swapControl.querySelector('.fx-1-btn');
        const btn2 = swapControl.querySelector('.fx-2-btn');

        // Find the red and green control images within the current instance
        const redControls = swapControl.querySelectorAll('.red_control');
        const greenControls = swapControl.querySelectorAll('.green_control');

        // Function to show red and hide green controls
        function showRedControls() {
            redControls.forEach(function (redControl) {
                redControl.style.display = 'block';
            });
            greenControls.forEach(function (greenControl) {
                greenControl.style.display = 'none';
            });
        }

        // Function to show green and hide red controls
        function showGreenControls() {
            greenControls.forEach(function (greenControl) {
                greenControl.style.display = 'block';
            });
            redControls.forEach(function (redControl) {
                redControl.style.display = 'none';
            });
        }

        // Attach click event listeners to buttons within the current instance
        btn1.addEventListener('click', showRedControls);
        btn2.addEventListener('click', showGreenControls);

        // Initial state: Show red controls, hide green controls
        showRedControls();
    });
});


// dragger vintage and light up

document.addEventListener('DOMContentLoaded', function () {
    // Define the range for the draggable element
    const minPosition = -287; // Minimum position (top)
    const maxPosition = -29;  // Maximum position (bottom)

    // Function to update lights based on control position within the closest vintage_up parent
    function updateLights(control, lights) {
        const step = (maxPosition - minPosition) / lights.length;
        const currentPos = parseFloat(control.style.top);

        // Calculate the index of the active light, default to the last light if at start
        let activeIndex = lights.length - 1; // Default to the last light (bottom) active
        if (currentPos < maxPosition) {
            activeIndex = Math.floor((currentPos - minPosition) / step);
        }

        // Update the lights
        lights.forEach((light, index) => {
            if (index === activeIndex) {
                light.classList.add('active_light');
            } else {
                light.classList.remove('active_light');
            }
        });
    }

    // Function to initialize the lights with the bottom one active by default
    function initializeDefaultLights() {
        const swapControls = document.querySelectorAll('.swap_vintage_control');

        swapControls.forEach(function (swapControl) {
            const vintageBlocks = swapControl.querySelectorAll('.vintage_up');

            vintageBlocks.forEach(function (vintageBlock) {
                const redLights = vintageBlock.querySelectorAll('.red_light_vint');
                if (redLights.length > 0) {
                    redLights[redLights.length - 1].classList.add('active_light');
                }

                const greenLights = vintageBlock.querySelectorAll('.green_light_vint');
                if (greenLights.length > 0) {
                    greenLights[greenLights.length - 1].classList.add('active_light');
                }
            });
        });
    }

    // Track the currently dragged element
    let draggedElement = null;
    let startY = 0;
    let startTop = 0;

    // Function to start dragging
    function startDrag(event, control) {
        event.preventDefault(); // Prevent default behavior (e.g., scrolling)

        draggedElement = control;
        startY = event.type === 'mousedown' ? event.clientY : event.touches[0].clientY;
        startTop = parseFloat(control.style.top) || 0;
    }

    // Function to handle dragging
    function handleDrag(event) {
        if (!draggedElement) return; // Exit if no element is being dragged

        event.preventDefault(); // Prevent default behavior

        let currentY = event.type === 'mousemove' ? event.clientY : event.touches[0].clientY;
        let newTop = startTop + (currentY - startY);

        // Bound the position within the min and max limits
        newTop = Math.max(minPosition, Math.min(maxPosition, newTop));

        draggedElement.style.top = `${newTop}px`;

        // Update lights based on the dragged element
        const parentBlock = draggedElement.closest('.vintage_up');
        if (draggedElement.classList.contains('red_control')) {
            updateLights(draggedElement, parentBlock.querySelectorAll('.red_light_vint'));
        } else if (draggedElement.classList.contains('green_control')) {
            updateLights(draggedElement, parentBlock.querySelectorAll('.green_light_vint'));
        }
    }

    // Function to stop dragging
    function stopDrag() {
        draggedElement = null; // Clear the reference to the dragged element
    }

    // Initialize default lights
    initializeDefaultLights();

    // Attach mouse and touch event listeners to red and green control elements
    document.querySelectorAll('.red_control, .green_control').forEach(control => {
        control.addEventListener('mousedown', function (event) {
            startDrag(event, control);
        });
        control.addEventListener('touchstart', function (event) {
            startDrag(event, control);
        });
    });

    // Attach mousemove and touchmove event listeners to handle dragging
    document.addEventListener('mousemove', handleDrag);
    document.addEventListener('touchmove', handleDrag);

    // Attach mouseup and touchend event listeners to stop dragging
    document.addEventListener('mouseup', stopDrag);
    document.addEventListener('touchend', stopDrag);
});



