#preloader {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #858dbe;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    transition: opacity 0.2s ease-in-out;
}

#preloader-logo {
    width: 150px;
    /* Adjust size as needed */
    height: auto;
    opacity: 0;
    transition: opacity .2s ease-in-out;
}

body,
* {
    -webkit-user-select: none;
    /* Safari */
    -moz-user-select: none;
    /* Firefox */
    -ms-user-select: none;
    /* Internet Explorer/Edge */
    user-select: none;
    /* Standard syntax */
}

/* logo */
.logo-btn img {
        width: 100%;
   max-width: 130px;
}
 /* popup session */
 .popup-session {
     opacity: 0;
     visibility: hidden;
     z-index: -1;
     /* display: none; */
     position: absolute;
     top: 0;
     left: 0;
     width: 100%;
     height: 100vh;
     transition: 0.3s ease-in-out;
     display: flex;
     align-items: center;
     justify-content: center;
 }

 .popup-session.active-session {
     opacity: 1;
     visibility: visible;
     z-index: 11;
    background: #858dbe;
 }

 .x-session-start {
     position: absolute;
     z-index: 5;
     cursor: pointer;
     top: 5%;
     left: 2%;
     width: 107px;
     height: 107px;
     border-radius: 100px;
     outline: none;
     border: none;
     display: flex;
     align-items: center;
     justify-content: center;
     background: #ffffff21;
 }

 .x-session-start img {
     width: 16px;
 }
 .x-session-minmax {
     position: absolute;
     z-index: 5;
     cursor: pointer;
     bottom: 25%;
     right: 2%;
     width: 107px;
     height: 107px;
     border-radius: 100px;
     outline: none;
     border: none;
     display: flex;
     align-items: center;
     justify-content: center;
     background: #ffffff21;
 }

 .x-session-minmax img {
     width: 16px;
 }
/* main screen popup */
.arrow-pop-btn {
    position: fixed;
    bottom: 25px;
    right: 20px;
    z-index: 99999;
    background: none;
    outline: none;
    border: none;
    cursor: pointer;
    display: none;
}

.popup-selection {
    position: fixed;
    z-index: 1000;
    bottom: -152px;
    /* Initially hidden */
    left: 0;
    width: 100%;
    height: 152px;
    background: #000000;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
    transition: bottom 0.3s ease-in-out;
}

.popup-selection.show {
    bottom: 0;
}

.popup-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 100%;
    padding: 0;
    max-width: 770px;
        margin: auto;
}

.column-pop {
   display: flex;
    text-align: center;
    align-items: center;
    justify-content: center;
    color: #fff;
    border-radius: 4px;
    width: 237px;
    height: 115px;
    cursor: pointer;
}

.close-pop-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    position: absolute;
    z-index: 1000;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    color: #fff;
}

.purple-bg {
    background-color: #0D0040;
}
.light-purple-bg {
    background-color: #858DBE;
}
.chat-pop {
background-color:#f5f5f5;
width: 80px;
height: 80px;
border-radius: 100px;
cursor: pointer;
    position: absolute;
    z-index: 1000;
    top: 50%;
    right: 5%;
    transform: translateY(-50%);
}
.acc-pop {
    background-color: #f5f5f5;
        width: 80px;
        height: 80px;
        border-radius: 100px;
        cursor: pointer;
        position: absolute;
        z-index: 1000;
        top: 50%;
        left: 5%;
        transform: translateY(-50%);
}
  /* session bar */
  .popup-session-bar {
      background: #fff;
      position: fixed;
      z-index: 1001;
      bottom: -152px;
      left: 0;
      width: 100%;
      height: 72px;
      box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
      transition: bottom 0.3s ease-in-out;
  }

  .popup-session-content {
      background: rgb(20, 0, 95);
      background: linear-gradient(270deg,
              rgba(20, 0, 95, 0.4822303921568627) 0%,
              rgba(0, 66, 168, 0.4990371148459384) 100%);
      position: relative;
      width: 100%;
      height: 100%;
  }

  .inner-bar {
      display: flex;
      align-items: center;
      justify-content: space-around;
      position: relative;
      height: 100%;
      padding: 0 3%;
  }

  .inner-bar>div {
      width: 33%;
      text-align: center;
  }
.video_pop {
    width: 40%;
}
.video_pop .video_txt {
background: transparent;
width: auto;
color: #fff;
}
.dropdown,
.drop-down-wrapper,
.drop-down-wrapper-sound {
    min-width: 128px;
    text-align: left !important;
        justify-content: flex-start !important;
        padding-left: 10px;
    position: relative;
    color: #fff;
}
.drop-down-wrapper,
.drop-down-wrapper-sound {
    min-width: 138px;
}
.drop-down-wrapper-quality {
    min-width: 95px !important;
}
.dropdown.quality-dropdown {
    min-width: 100% !important;
}
.video_pop .dropdown-grp {
    position: absolute;
        left: 0;
        bottom: 50px;
        width: 312px;
        display: none;
        z-index: 1000;
        border-radius: 4px;
        background-color: #f7f7f7;
}
.dropdown-camera {
    height: 50px;
    width: 100%;
    background-color: #f7f7f7;
    color: #000;
    font-weight: bold;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 10px;
    border-radius: 4px;
}
.dropdown-camera:nth-of-type(even) {
background: #00000017;
}
.dropdown::after {
    content: ' ';
        background: url('../icons-popups/arrow-down-stroke.svg');
            position: absolute;
                background-repeat: no-repeat;
                z-index: 5;
                width: 13px;
                height: 10px;
                right: 10px;
                top: 56%;
                transform: translateY(-50%);
}
  .btn_orange_full {
      max-width: 107px;
      width: 100%;
      height: 46px;
      background: #FF9B04;
      color: #fff;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 14px;
      font-weight: bold;
      border-radius: 4px;
      cursor: pointer;
  }
  .btn_orange_full.mixing_board_on a {
    text-decoration: none;
    color: #fff;
  }
  .sharing_board_on {
    max-width: 162px;
  }
  .btn_orange_full.mixing_board_on {
max-width: 222px;
  }
.btn_orange_full.active_session {
border: 2px solid #F2EDE9;
background: #FF9B04;
position: relative;
}
.btn_orange_full.active_session::after {
  background: #72BF44;
  width: 8px;
  height: 8px;
  position: absolute;
  z-index: 2;
  top: 2px;
  right: 2px;
  border-radius: 50px;
  content: ' ';
}

  .btn_account_wrap {
      display: flex;
      justify-content: flex-end;
      gap: 50px;
      padding-right: 30px;
  }

  .btn_account_wrap a {
      color: #fff;
      text-decoration: none;
      padding-bottom: 1px;
      border-bottom: 1px solid #fff;
  }
  .btn_account_wrap a:hover {
color: #0D0040;
border-bottom: 1px solid #0D0040;
  }
.popup-session .form-box img {
max-width: 220px;
}
  .popup-session-bar.activated {
      bottom: 152px;
  }
.theme-toggle-pop {
        height: auto;
            width: auto !important;
            margin: 0 50px;
}
.session_flex {
    display: flex;
        max-width: 450px;
        width: 100%;
        gap: 15px;
}
.vol-flex {
        display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
}
.vol-flex > div {
background: #FF9B04;
width: 46px;
height: 46px;
border-radius: 4px;
display: flex;
align-items: center;
justify-content: center;
cursor: pointer;
font-size: 14px;
}
.vol-flex > .btn_video_on {
    min-width: 115px;
    justify-content: space-between !important;
    padding: 0 10px;
    color: #fff;
    /* pointer-events: auto;
    background-color: rgb(242, 237, 233);
    color: rgb(148, 148, 148); */
}
/* .btn_video_on img {
    filter: brightness(0) saturate(100%) invert(58%) sepia(0%) saturate(0%) hue-rotate(185deg) brightness(91%) contrast(85%);
} */
.sounds-active-block {
background: transparent !important;
}
       .sounds-activated {
display: none;
        }

.preview-window.selected {
    box-shadow: 0 0 0 3px #f5f5f5;
}

.toggle-modes {
    width: 45px;
    height: 19px;
    border-radius: 100px;
    background-color: #FF9B04;
    position: relative;
    cursor: pointer;
    margin: 0 10px;
}

.circle {
    width: 15px;
    height: 15px;
    border-radius: 50%;
    background-color: #fff;
    position: absolute;
    top: 2px;
    left: 2px;
    transition: transform 0.3s ease;
}

.dark-mode .circle {
    transform: translateX(26px);
}

.column-pop span {
    font-size: 12px;
}


/* home screen EDITs */

.dca-pop-screen {
    display: none;
    position: absolute;
        z-index: 990;
        top: 120px;
        left: 0;
        width: 100%;
        height: 100%;
        background: #626262;
}
.dca1-pop-screen.visible,
.dca2-pop-screen.visible,
.dca3-pop-screen.visible,
.dca4-pop-screen.visible,
.dca5-pop-screen.visible,
.dca6-pop-screen.visible,
.dca7-pop-screen.visible,
.dca8-pop-screen.visible {
    display: block;
}
.heading-dca {
    color: #fff;
    font-weight: bold;
    margin: auto;
    text-align: center;
}
.btn-block.close {
        border-radius: 4px;
            border: 3px solid #f5f5f5 !important;
            background: #2d2d2f !important;
            color: #fff !important;
            box-shadow: none !important;
            text-transform: uppercase;
            font-size: 14px;
            height: 48px;
            width: 122px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            margin: 10px auto;
}
.dca-edit-screen-flex {
    max-width: 1250px;
    margin: 20px auto 0;
    display: flex;
    flex-direction: column;
}
.row-one-dca {
    display: flex;
    justify-content: space-between;
}
.channel-screen {
    width: 120px;
        height: 71px;
        background: #1f1f21;
        color: #fff;
        display: flex;
        justify-content: center;
        align-items: flex-end;
        padding-bottom: 10px;
        margin-bottom: 20px;
        border-radius: 4px;
        font-weight: 600;
        text-decoration: none;
}
 .aux-row .channel-screen {
    background: #c8ffa5;
    color: #171100;
}

.fx-row .channel-screen {
    background: #FFC2FF;
    color: #171100;
}

.bus18-row .channel-screen,
.bus916-row .channel-screen {
    background: #b9ffff;
    color: #171100;
}
 
/* light theme adjustments */
.home.light-theme .channel-screen {
    background: #fff;
    color: #1f1f21;
}

.home.light-theme .aux-row .channel-screen {
    background: #c8ffa5;
    color: #171100;
}

.home.light-theme .fx-row .channel-screen {
    background: #FFC2FF;
    color: #171100;
}

.home.light-theme .bus18-row .channel-screen,
.home.light-theme .bus916-row .channel-screen {
    background: #b9ffff;
    color: #171100;
}
.home.light-theme .dca-pop-screen {
    background: #eeeded;
}
.home.light-theme .channel-screen {
border: none !important;
}
.home.light-theme .heading-dca {
    color: #1f1f21;
}
#channel-mtx .channel-seven .channel-screen,
#channel-mtx .channel-eight .channel-screen,
#channel-1-8 .channel-screen,
#channel-9-16 .channel-screen,
#channel-17-24 .channel-screen,
#channel-25-32 .channel-screen {
border: 3px solid #2d2d2f !important;
}

/* wheel know item */
wheel-board {
    position: relative;
    text-align: center;
    width: 134px;
    height: 122px;
}

.wheel-numbers {
    max-width: 140px;
}

.black-circle {
    position: absolute;
    top: 58%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #1e1e1e;
    width: 89px;
    height: 89px;
    border-radius: 100px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.wheel-knob {
    width: 57px;
    height: 57px;
    transform: rotate(-140deg);
}

.insert-container {
    padding-right: 20px;
}

#animated {
    position: absolute;
}

/* dropdowns */

.source-detail,
.gate-detail,
.key-source-item,
.button-drop_wrapper,
.effect-insert-block-row {
    position: relative;
}

.dropdown-detail {
    border-radius: 4px;
    border: 3px solid #2d2d2f;
    color: #f7f7f7;
    width: 127px;
    height: 51px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    position: relative;
}

.dropdown-menu {
position: absolute;
    right: -244px;
    top: 0;
    width: 222px;
    max-height: 262px;
        overflow-y: auto;
            overflow-x: visible;
        border: 5px solid #2D2D2F;
        border-radius: 0;
        background-color: #414141;
    display: none;
    z-index: 1000;
}
.dropdown-option:nth-of-type(even) {
background-color: #383838;
}


.dropdown-arrow {
    display: none;
        /* Initially hidden */
z-index: 1001;
    position: absolute;
        right: -27px;
        top: 13px;
        width: 12px;
        height: auto;
}
.dropdown-arrow img {
   width: 100%;
}
.dropdown-option {
    padding: 10px;
    color: #f7f7f7;
    cursor: pointer;
    text-align: center;
    background-color: #414141;
        text-align: left;
            padding-left: 25px;
            font-size: 16px;
            font-weight: 600;
}
.source-detail .text-detail {
    position: absolute;
        color: #fff;
        text-align: center;
        font-size: 14px;
        font-weight: 600;
        z-index: 3;
        top: -40px;
        left: 50%;
        transform: translate(-50%, 0);
}
.key-source-item .dropdown-menu {
right: 50px;
    top: -260px;
    height: 220px !important;
    width: 222px;
    max-height: 262px;
}
.gate-detail .dropdown-menu {
    right: -138px;
        top: -65px;
    height: 220px !important;
    width: 222px;
    max-height: 262px;
}
.key-source-item .dropdown-arrow {
    z-index: 1001;
    position: absolute;
    transform: rotate(-90deg);
    right: 154px;
    top: -55px;
    width: 12px;
    height: auto;
}

.gate-detail .dropdown-arrow {
    z-index: 1001;
        position: absolute;
        transform: rotate(0deg);
            right: 79px;
            top: 13px;
        width: 12px;
        height: auto;
}

.key-source-item .dropdown-menu {
    right: -15px;
    top: -215px;
}
.key-source-item .dropdown-arrow {
right: 89px;
    top: -11px;
}
     .button-drop_wrapper .dropdown-arrow {
   
        top: 27px;
        left: 50%;
        transform: rotate(90deg) translateX(50%);

     }
  .button-drop_wrapper .dropdown-menu {
        right: 50%;
            top: 40px;
            transform: translate(50%, 10px);
  }
/* effects dropdowns */
.insert-btn-drop.dropdown-detail {
    border-radius: 4px;
    border: 3px solid #2d2d2f;
    color: #f7f7f7;
    width: 121px;
    height: 31px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    text-transform: uppercase;
}
.effect-insert-block-row .dropdown-menu{
    right: auto;
        left: 132px;
      
            max-height: 282px;
}
.effect-insert-block-row .dropdown-arrow {
        right: 0;
            top: 8px;
            width: 12px;
            height: auto;
            left: 125px;
}
.effect-insert-block-row .dropdown-option {
padding: 6px 10px;
text-transform: uppercase;
}

/* end */

.progress-path {
    transition: stroke-dasharray 0.3s;
}

.wheel-knob {
    width: 57px;
    height: 57px;
    transform-origin: center;
    cursor: pointer;
}
.progress-circle {
    position: absolute;
    width: 93%;
    height: 100%;
}

.progress-path {
    transition: stroke-dasharray 0.3s;
}

/* tab links to detail pages */


.screen-channels-item {
    text-decoration: none;
}



  .minimized .x-session-start,
  .minimized .x-session-minmax {
      width: 40px !important;
      height: 40px !important;
  }
   .minimized .x-session-minmax {
    bottom: 5%;
   }
   .popup-session.minimized .form-box img {
    max-width: 100px;
   }
  #popup-sendkey .form-box {
background: #0D0040;
width: 510px;
height: 380px;
border-radius: 10px;
    padding: 30px;
        text-align: center;
            display: flex;
                align-items: center;
                justify-content: center;
  }


  /* popup form */
.popup-session input[type="text"]  {
    margin-bottom: 40px;
        background: transparent !important;
        border: none;
        border-bottom: 1px solid #fff;
        padding: 10px 10px 10px 0;
        outline: none;
        color: #fff;
        font-family: "Open Sans", sans-serif;
        max-width: 373px;
        width: 100%;
        height: 40px;
        font-size: 16px;
}

.popup-session button[type="submit"] {
    background-color: #ff9b04;
    color: #fff;
    font-size: 16px;
    font-weight: 600;
    max-width: 373px;
    width: 100%;
    height: 62px;
    margin: auto;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 8px;
    outline: none;
    border: none;
    font-family: "Open Sans", sans-serif;
    cursor: pointer;
}

.popup-session h2 {
    text-align: center;
    color: #fff;
    text-transform: capitalize;
    font-size: 40px;
    font-weight: 500;
    margin-bottom: 20px;
}
.popup-session p {
    color: #fff;
    margin-bottom: 30px !important;
}
.popup-session form div {
    width: 100%;
    height: 30px;
    margin-bottom: 20px;
}

.popup-session .login-form input[type="checkbox"] {
    margin-right: 10px;
}

.popup-session ::placeholder {
    color: #fff;
    opacity: 1;
    /* For older browsers */
    font-family: "Open Sans", sans-serif;
    font-size: 16px;
}

.popup-session :-ms-input-placeholder {
    /* Internet Explorer 10-11 */
    color: #fff;
}

.popup-session ::-ms-input-placeholder {
    /* Microsoft Edge */
    color: #fff;
}

.popup-session .form-box p {
    margin: 10px 0;
    text-align: center;
}

.popup-session .form-box p a {
    color: #ffd601;
    font-weight: bold;
    font-size: 14px;
    text-decoration: none;
}
/* sharing screens */
.sharing-screens {
    display: none;
    /* Initially hidden */
} 
.sharing-img {
    display: block;
    /* Initially visible */
}
.sharing-screens {
    padding: 35px 0;
background: #0D0040;
border-radius: 10px;
min-height: 540px;
min-width: 510px;
width: 100%;
position: relative;
}
.sharing-screens .close-sharing-screens {
        position: absolute;
            z-index: 5;
            cursor: pointer;
            top: 40px;
                right: 20px;
                width: 25px;
                height: 25px;
            border-radius: 100px;
            outline: none;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
}
.sharing-screens h2 {
    font-size: 24px;
    padding-left: 35px;
    margin-bottom: 0;
    padding-bottom: 30px;
    border-bottom: 1px solid #fff;
    text-align: left;
    margin-top: 0;
}
.sharing-selection {
        display: flex;
            flex-wrap: wrap;
            max-width: 450px;
            gap: 10px;
            margin: 40px auto 60px;
}
#start-sharing {
    font-size: 16px;
        margin: 30px auto 10px;
}
.screen-single {
    width: 100%;
    max-width: 219px;
    color: #fff;
   
    cursor: pointer;
}
.screen-single img {
border: 3px solid transparent;
}

.screen-single.screen-selected img {
    border: 3px solid #00A651;
    border-radius: 10px;
}
.popup-session .screen-single img {
    max-width: 219px;
}
 .addons #user {
     display: none !important;
 }

/* volume bar */
/* Main volume display container */
.volume-display {
    position: absolute;
    bottom: 25%;
    left: 50%;
    transform: translateX(-50%);
    width: 425px;
    text-align: center;
    z-index: 1000;
    padding: 10px;
    border-radius: 8px;
    display: flex;
    gap: 20px;
    align-items: center;
}

/* Volume icon */
.volume-icon {
    margin-bottom: 10px;
}

/* Progress container */
.progress-container {
    position: relative;
        /* Ensure relative positioning for the percentage text */
    width: 280px;
    height: 10px;
    background: #FFFFFF;
    border-radius: 5px;
    overflow: hidden;
    margin-bottom: 5px;
}

/* Progress bar */
.progress-bar {

    height: 100%;
    background: #FF9B04;
    transition: width 0.3s ease;
}

/* Percentage text */
.percentage-text {
    position: absolute;
    top: -20px;
    color: #000;
    background: #fff;
    padding: 5px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    left: 50px;
    /* Start at 50px for 0% */
    transform: translateX(-50%);
    /* Center the text horizontally */
    transition: left 0.3s ease;
    /* Smooth movement */
}
.percentage-text::before {
        content: ' ';
            width: 8px;
            height: 8px;
            background: #fff;
            position: absolute;
            transform: rotate(135deg);
            left: 42%;
            bottom: -4px;
}
#vol-mute,
#vol-min,
#vol-plus {
    pointer-events: none;
}
/* height fixes */

.page-wrapper {
    max-height: none !important;
    padding: 12px 20px 5px !important;
}

.screen-channels-wrapper {
    margin-top: 20px !important;
}

.channel-screen {
    margin-bottom: 15px !important;
}

.needle-screen {
    margin-bottom: 20px !important;
}

.main-board-buttons.mute-solo {
    margin-top: 20px !important;
}

.btn-block.sidebar-item.lock-mute {
    margin: 20px auto 5px !important;
}

.main-item.draggers {
    margin-bottom: 15px !important;
}

.sidebar-item.show-solos {
    margin: 10px auto !important;
}
.sidebar-item.mixing {
    margin: 40px auto !important;
}
@media only screen and (max-width: 1366px) {
    .main-item.draggers {
        margin-bottom: 10px !important;
        margin-top: 20px !important;
    }
                .main-item.draggers {
                    margin-bottom: 5px !important;
                    margin-top: 15px !important;
                }
                                .sidebar-item.controls {
                                    margin: 7px auto !important;
                                }
}

/* Responsiveness */
@media only screen and (max-width: 1365px) {
    .page-wrapper {
        max-height: none !important;
        padding: 12px 8px 5px !important;
    }
    .btn_account_wrap {
        width: 280px !important;
    }
         .btn_account_wrap a,
                 .video_pop .video_txt  {
            font-size: 12px;
         }
         .inner-bar {
            padding: 0 2%;
         }
                 .inner-bar>div {
                     width: 36%;
                 }

.arrow-pop-btn {
    right: 10px;
}
    .container {
        gap: 10px!important;
    }
        .screen-channels-wrapper {
            max-width: 100% !important;
        }
        .sidebar-item.addons {
                gap: 15px !important;
        }
       
  .btn-block.sidebar-item.faders p {
                    font-size: 11px !important;
   }
   .sidebar-item.mute-group {
        gap: 5px !important;
   }
   .main-board-buttons.mute-solo {
    gap: 10px !important;
   }
   .theme-toggle-pop {
            flex-direction: row;
                margin: 0 20px;
                gap: 5px !important;
                left: -4% !important;
   }
   .chat-pop {
    right: 7% !important;
   }
   #app-window,
   #video-window,
   #screen-share {
       width: 210px !important;
       height: 105px !important;
       position: relative !important;
   }
  #app-window img {
width: 100% !important;
    height: 100% !important;
    object-fit: cover !important;
   }
         
   /* inner pages */
#rpreset-tab-content .processing-flex {
    gap: 10px !important;
}
   .config-tab-wrapper:not(#rpreset-tab-content) .tab-content-hidden {
       padding: 5px 5px 30px 5px !important;
   }
   .config-tab-wrapper:not(#rpreset-tab-content) .processing-flex {
       max-width: 100% !important;
   }
   .config-tab-wrapper:not(#rpreset-tab-content) .single-list {
       width: 16% !important;
       min-width: 170px !important;
   }
.config-tab-wrapper:not(#rpreset-tab-content) .single-list.last-block {
    margin-left: 25px !important;
}
#analog-tab-content .single-list.last-block {
min-width: 220px !important;
padding: 0 10px !important;
}
.home.routing .config-tab-wrapper:not(#rpreset-tab-content) .single-list.extended-card-col {
        min-width: 345px !important;
}
.home.routing .single-list.extended-card-col .inside-border-single img {
    width: 100% !important;
    max-width: 320px !important;
}
.home.meters-dark .tab-content-hidden,
.home.meters-light .tab-content-hidden  {
padding: 5px 10px 30px 10px !important;
}
.tab-content-hidden .signals-17-32 {
    padding-left: 15px !important;
}
.tab-content-hidden .signals-1-16 {
    padding-left: 1px !important;
}
.main-title-1-32::before,
.main-title-1-32:after {
display: none !important;
}
.home.meters-dark .tab-content-hidden .below-main,
.home.meters-light .tab-content-hidden .below-main {
padding: 0 !important;
}
.home.scene .below-main {
    padding: 10px 0!important;
}
.current-ticket img {
    max-width: 210px !important;
}


/* effects */

.home.effects .tab-content-hidden {
    padding: 5px 2px 30px 2px !important;
}
.heading-board-effect .filled-column {
    font-size: 26px !important;
}
.screw-bottom-right {
    bottom: 1px!important;
    right: 1px !important;
}
.screw-bottom-left {
    bottom: 1px !important;
    left: 1px!important;
}
.screw-top-left {
    top: 1px!important;
    left: 1px!important;
}
.screw-top-right {
    top: 1px!important;
    right: 1px!important;
}
.knobs_wrap.drag_wrap_design {
    width: 75% !important;
}
.double-row_temp_with_title {
    gap: 20px!important;
    padding-left: 10px!important;
}
.fx3-inner-1-content {
        padding: 0 15px !important;
}
.col-1_fx3.inputoutput {
    gap: 10px !important;
}
.display_number input {
    font-size: 40px !important;
}
.display_wrap {
    width: 215px !important;
}
.board_img {
    max-width: 150px !important;
}
.tap_wrappers .title_wrap {
    width: 12% !important;
    padding-left: 10px !important;
}
.tap_wrappers .knobs_wrap {
    width: 86% !important;
}
.tap_wrappers.tap_four .title_wrap {
        width: 18% !important;

}
.title_wrap {
    font-size: 26px !important;
}
.fx_delay .double-row_temp {
    padding-left: 20px !important;
}
.fx_delay .labels_top_count_bot,
.full_row_count,
.fx_delay .screen_number_module,
.bottom_text_row {
    width: 100% !important;
    max-width: 99% !important;
}
.fx_delay .bottom-row_flex {
    width: 99% !important;
 }
 .fx_delay .title_wrap.sm_text {
     font-size: 14px !important;
 }
 .fx_delay .count_single_wrap>div,
 .fx_delay .count_single_wrap input {
     font-size: 28px !important;
 }
 .dragger-effect-board.bottom_eq_drag_wrap {
     gap: 5px !important;
 }
 .vse_xtec .vse_electronic_inner {
     padding: 0 20px !important;
 }
 .fx6-att-col {
     width: 16% !important;
 }
 .vse_xtec .vse-limit-col {
     width: 77% !important;
     padding-left: 68px !important;
 }
 .a-fx6 {
    font-size: 26px !important;
}
.vse_xtec.vse_xtec_eq5 .vse-limit-col {
    width: 84% !important;
}
.vse_xtec .boxed.wheel-box.hi_freq_wrap .wheel-numbers {
    margin-left: -2px;
}
.combinator_stereo .float_title.title_wrap {
    font-size: 12px !important;
}
.indicator_wrap {
    width: 280px !important;
    height: 156px !important;
}
.indicator-img {
    width: 240px !important;
    height: 107px !important;
}
.vse_electronic.vse_compress .vse_electronic_inner {
    padding: 0 17px 0 15px !important;
}
.vse_electronic.vse_compress .vse-electronic-col {
    display: flex;
    width: 34% !important;
}
.effect-inner-row.main-effect-board.vse_electronic.vse_compress .vse_electronic_inner {
  
    gap: 10px !important;
}
.vse_electronic.vse_compress .vse-limit-col {
    width: 70% !important;
}
.vse_electronic .left_wall,
.vse_electronic .right_wall {
        display: none !important;
}
.vse_electronic .title-box {
    font-size: 26px !important;
}
.effect-inner-row.main-effect-board.one_row_setup.ab_double .title_wrap {
    font-size: 18px !important;

}
.one_row_setup.ab_double.enhancer-x1 .knobs_wrap {
    width: 90% !important;
}
.one_row_setup .double-row_temp_with_title .top-row_flex>.effects_double_down {
    width: 14.1% !important;
}
.one_row_setup.ab_double .double-row_temp_with_title .top-row_flex>.effects_double_down {
    width: 27.1% !important;
}
.one_row_setup.ab_double .double-row_temp_with_title {
    justify-content: flex-end;
}
.one_row_setup .double-row_temp_with_title .top-row_flex>.effects_double_down {
    width: 12.1% !important;
}
.left_wall,
.right_wall {
 display: none !important;
}
.one_row_setup .knobs_wrap {
    width: 88% !important;
}
.effect-inner-row.main-effect-board.one_row_setup .top-row_flex {
    gap: 4px !important;
}
        .one_row_setup.solo_modes .double-row_temp_with_title .top-row_flex>.effects_double_down {
            width: 14.1% !important;
        }
                .effect-inner-row.main-effect-board.one_row_setup.ab_double .top-row_flex {
                    justify-content: flex-end !important;
                }
                                .title_wrap_ex {
                                    font-size: 22px !important;
                                    margin: 0 13px !important;
                                }
          .effect-inner-row.main-effect-board.one_row_setup.ab_double .title_wrap {
         top: 1% !important;
      left: 12px !important;
     }
   .solo_modes .red_btn_text:first-of-type {
       font-size: 18px !important;
     }

     /* details */

         .left-config-tab {
             width: 78% !important;
         }
.right-config-tab {
    width: 21% !important;
}
.insert-container {
    padding-right: 5px !important;
    padding-left: 5px !important;
}

.tab-content-hidden {
    padding: 5px 10px 30px 5px;
}
.screen-set.column_one_left {
    padding-left: 2px !important;
    gap: 15px !important;
}
.gate-right-col .wheel-container {
    width: 95% !important;
}
.dyn-bottom-flex {
    gap: 20px !important;
}
.inner-wrapper_eq {
    gap: 5px !important;
}
.sends-tab-wrapper.tab-content-hidden {
    padding: 5px 5px 30px 10px !important;
}
.flex-two-btns>div span {
        text-align: center;
}
.single-list.preset-first {
    padding-left: 10px !important;
}
.eq-board-wrapper {
    width: 863px !important;
}
.movement-board {
    width: 800px !important;
}
.select-drag-btns-eq {
    gap: 35px !important;
}
.control-board-btns {
    display: flex;
    gap: 10px !important;
}
.eq-tab-wrapper.tab-content-hidden {
padding: 5px 5px 30px 0px !important;
}
.bottom-btns-eq {
    gap: 20px !important;
}
.flex-two-btns>div {
    font-size: 12px !important;
}
.dca-edit-screen-flex {
    max-width: 1120px !important;
}
.modes-set.column_one_left {
    gap: 20px !important;
}
}
