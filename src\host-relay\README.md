# VSE Host Relay Application

The VSE Host Relay is a lightweight desktop application that runs on the host's computer to bridge OSC messages between the VSE web server and the local X32 mixer.

## Features

- **WebSocket Connection**: Connects to the VSE web server for real-time communication
- **X32 Auto-Discovery**: Automatically finds X32 mixers on the local network
- **OSC Message Relay**: Bridges OSC commands from web clients to the local X32
- **Simple GUI**: Easy-to-use interface for monitoring connection status
- **Cross-Platform**: Runs on Windows, macOS, and Linux
- **Executable Generation**: Can be built into standalone executables

## Requirements

### For Running from Source
- Python 3.7 or higher
- Required packages (see requirements.txt):
  - websocket-client
  - xair-api
  - python-osc

### For Building Executables
- PyInstaller (automatically installed by build script)
- Platform-specific tools for installers (optional):
  - Windows: NSIS for installer creation
  - macOS: hdiutil (built-in) for DMG creation
  - Linux: tar (built-in) for package creation

## Installation

### Option 1: Download Pre-built Executable
1. Download the appropriate executable for your platform from the releases
2. Extract the archive (if applicable)
3. Run the executable

### Option 2: Run from Source
1. Install Python 3.7+
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the application:
   ```bash
   python vse_host_relay.py
   ```

### Option 3: Build Your Own Executable
1. Install Python 3.7+
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
3. Run the build script:
   ```bash
   python build_executable.py
   ```
4. Find the executable in the `dist/` folder

## Usage

### Initial Setup
1. Launch the VSE Host Relay application
2. Configure the connection settings:
   - **Server URL**: Enter your VSE server URL (e.g., `https://your-vse-server.com`)
   - **X32 IP**: Enter your X32 mixer IP address or click "Auto Discover"
   - **Session Key**: Enter the 6-digit session key from the web interface

### Auto-Discovery
1. Ensure your X32 mixer is connected to the same network as your computer
2. Click the "Auto Discover" button
3. The application will scan the network and automatically detect X32 mixers

### Connecting
1. Click the "Connect" button to establish connections to both the VSE server and X32 mixer
2. Monitor the connection status in the "Connection Status" section
3. Watch the activity log for real-time information about OSC commands

### Operation
- Once connected, the relay will automatically forward OSC commands from web clients to your X32
- All activity is logged in the "Activity Log" section
- Connection status is displayed with color-coded indicators:
  - **Green**: Connected
  - **Red**: Disconnected
  - **Gray**: No active session

## Configuration

### Server URL Formats
- HTTP: `http://your-server.com`
- HTTPS: `https://your-server.com`
- WebSocket: `ws://your-server.com`
- Secure WebSocket: `wss://your-server.com`

The application will automatically convert HTTP(S) URLs to WebSocket format.

### X32 Network Configuration
- Ensure your X32 is connected to the same network as your computer
- The X32 should be configured to accept OSC messages on port 10023 (default)
- Check your network firewall settings if auto-discovery fails

### Session Management
- The session key is provided by the VSE web interface when creating a session
- Multiple clients can connect to the same session through this relay
- The relay acts as the "host" in the session

## Troubleshooting

### Connection Issues
1. **VSE Server Connection Failed**:
   - Verify the server URL is correct
   - Check your internet connection
   - Ensure the VSE server is running and accessible

2. **X32 Connection Failed**:
   - Verify the X32 IP address is correct
   - Ensure the X32 is powered on and connected to the network
   - Try the auto-discovery feature
   - Check network firewall settings

3. **Session Join Failed**:
   - Verify the session key is correct and active
   - Ensure the VSE server connection is established first

### Auto-Discovery Issues
- Ensure your computer and X32 are on the same network subnet
- Check that no firewall is blocking network scanning
- Try manually entering the X32 IP address if auto-discovery fails

### Performance Issues
- Close unnecessary applications to free up system resources
- Ensure stable network connectivity
- Check the activity log for error messages

## Logging

The application creates detailed logs in:
- **GUI Log**: Real-time activity log in the application window
- **File Log**: `vse_host_relay.log` in the application directory

Log levels include:
- **INFO**: General information and status updates
- **SUCCESS**: Successful operations
- **WARNING**: Non-critical issues
- **ERROR**: Critical errors that may affect functionality
- **DEBUG**: Detailed debugging information

## Building Executables

The `build_executable.py` script can create platform-specific executables:

### Windows
- Creates `.exe` executable
- Optional NSIS installer (if NSIS is installed)
- Includes version information and icon

### macOS
- Creates `.app` application bundle
- Optional `.dmg` disk image
- Code signing support (requires developer certificate)

### Linux
- Creates standalone binary
- Optional `.tar.gz` package
- AppImage support (future enhancement)

## Architecture

The VSE Host Relay acts as a bridge between three components:

1. **VSE Web Server**: Receives commands from web clients via WebSocket
2. **Host Relay**: This application - processes and forwards commands
3. **X32 Mixer**: Receives OSC commands via UDP

```
Web Client → VSE Server → Host Relay → X32 Mixer
    ↑                                      ↓
    ←────── Status Updates ←────────────────
```

## Security Considerations

- The relay only accepts commands from authenticated sessions
- All communication with the VSE server is encrypted (WSS)
- Local network communication with X32 is unencrypted (OSC standard)
- Session keys provide access control to mixer functions

## Support

For technical support or bug reports:
1. Check the activity log for error messages
2. Verify all connection requirements are met
3. Consult the troubleshooting section
4. Contact VSE Suite support with log files if needed

## Version History

- **v1.0.0**: Initial release with basic relay functionality
  - WebSocket connection to VSE server
  - X32 auto-discovery and connection
  - OSC command forwarding
  - GUI status monitoring
  - Cross-platform executable generation
