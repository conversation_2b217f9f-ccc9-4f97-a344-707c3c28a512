   html,
   * {
       box-sizing: border-box;
   }

   body,
   a,
   p,
   div {
       font-family: "Open Sans", sans-serif;
   }

   body {
       background: #626262;
       margin: 0;
       padding: 0;
   }

   .page-wrapper {
       padding: 25px 20px;
       max-height: 100vh;
       max-width: 100%;
   }

   .container {
       display: flex;
       gap: 25px;
       flex-wrap: nowrap;
   }

   .left-sidebar {
       width: 10%;
       text-align: center;
   }

   .main {
       width: 90%;
   }

   /* buttons */

   .btn-block {
       border-radius: 4px;
       border: 3px solid #f5f5f5;
       background: #2d2d2f;
       color: #fff;
       text-transform: uppercase;
       font-size: 14px;
       height: 38px;
       width: 42px;
       display: flex;
       align-items: center;
       justify-content: center;
       cursor: pointer;
   }

   .btn-block p,
   .left-sidebar p {
       color: #fff;
       font-weight: 600;
       text-transform: uppercase;
       font-size: 14px;
   }

   /* main part header */
   .main-item.navbar {
       display: flex;
       justify-content: space-around;
       align-items: center;
   }

   .main-item.navbar a {
       display: flex;
       flex-direction: column;
       gap: 10px;
       justify-content: center;
       align-items: center;
       text-decoration: none;
       text-transform: uppercase;
       color: #fff;
       font-size: 14px;
       font-weight: bold;
   }

   .navbar-item-img {
       background: #000;
       height: 40px;
       width: 40px;
       border-radius: 100px;
       display: flex;
       align-items: center;
       justify-content: center;
       box-shadow: 0 0 0 3px #00000014;
   }

   .navbar-item-img.active_item {
       background: #fff;
       box-shadow: none;
   }

   .navbar-item-img.active_item img {
       filter: invert(1);
   }

   .navbar-item-img.homed img {
       margin-bottom: 2px;
       margin-left: 2px;
   }

   .scenes img {
       margin-top: 7px;
       margin-right: 7px;
   }

   .meters img {
       margin-bottom: 7px;
       margin-left: 5px;
       width: 32px;
   }

   /* sidebar items */
   .sidebar-item.controls {
       width: 100%;
       max-width: 130px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       margin: 15px auto;
       border: 3px solid #f5f5f5;
       border-radius: 4px;
       gap: 0 !important;
   }

   .sidebar-item.controls .btn-block {
       border: none;
       width: 32%;
       border-radius: 0;
   }

   .sidebar-item.controls .btn-block:nth-of-type(2) {
       border-left: 3px solid #f5f5f5;
       border-right: 3px solid #f5f5f5;
       border-radius: 0 !important;
       width: 36%;
       margin: 0;
   }

   /* siebar - 4 buttons */
   .sidebar-item.addons {
       width: 100%;
       max-width: 134px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       flex-wrap: wrap;
       margin: 20px auto;
       gap: 20px;
   }

   .sidebar-item.addons .btn-block {
       width: 42%;
       max-width: 56px;
       max-height: 36px;
   }

   /* fader */
   .activated-faders {
       width: 100%;
       max-width: 130px;
       min-height: 151px;
       margin: auto;
       background: #f5f5f5;
       box-shadow: 0 0 9.2px 1px #f5f5f5;
       display: flex;
       align-items: center;
       justify-content: center;
       flex-direction: column;
       gap: 10px;
       display: none;
   }

   .activated-faders img {
       width: 100%;
       max-width: 60%;
   }

   .btn-block.sidebar-item.faders {
       width: 100%;
       max-width: 134px;
       height: 46px;
       display: flex;
       align-items: center;
       margin: 20px auto;
   }

   .btn-block.sidebar-item.faders p {
       font-size: 12px;
   }

   /* 1-2 when fader activated */
   .send-faders-active-options {
       width: 100%;
       max-width: 120px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       display: none;
   }

   .send-faders-active {
       width: 46%;
       max-width: 55px;
       height: 46px;
       margin-bottom: 20px;
   }

   /* X Y auto mixing */
   .sidebar-item.mixing {
       width: 100%;
       max-width: 134px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       flex-wrap: wrap;
       margin: 45px auto;
   }

   .sidebar-item.mixing p {
       width: 100%;
   }

   .btn-block.mixing-item {
       width: 46%;
       max-width: 55px;
       height: 46px;
   }

   /* mute groups */
   .sidebar-item.mute-group {
       width: 100%;
       max-width: 134px;
       display: flex;
       align-items: center;
       justify-content: space-between;
       flex-wrap: wrap;
       margin: 20px auto;
       gap: 15px;
   }

   .sidebar-item.mute-group .btn-block {
       width: 46%;
       max-width: 55px;
       height: 55px;
   }

   .sidebar-item.mute-group p {
       width: 100%;
       margin-bottom: 0;
   }

   .sidebar-item.mute-group .last-mute {
       width: 100%;
       max-width: 100%;
   }

   /* solos  */
   .sidebar-item.show-solos {
       width: 100%;
       max-width: 134px;
       display: flex;
       align-items: center;
       justify-content: center;
       flex-direction: column;
       margin: 20px auto;
   }

   .sidebar-item.show-solos p {
       width: 100%;
   }

   .show-solos-item-wrap {
       max-width: 134px;
       width: 100%;
       display: flex;
       border: 3px solid #f5f5f5;
       border-radius: 4px;
   }

   .show-solos-item {
       border: none;
       width: 50%;
       border-radius: 0;
       background: #2d2d2f;
       color: #fff;
       text-transform: uppercase;
       font-size: 14px;
       height: 38px;
       cursor: pointer;
       display: flex;
       justify-content: center;
       align-items: center;
   }

   .show-solos-item .btn-block:last-of-type {
       border-left: 3px solid #f5f5f5;
       border-radius: 0 !important;
       margin: 0;
   }

   /* lock mutes big */
   .btn-block.sidebar-item.lock-mute {
       width: 100%;
       max-width: 134px;
       height: 46px;
       display: flex;
       align-items: center;
       flex-wrap: wrap;
       margin: 20px auto;
   }

   .btn-block.sidebar-item.lock-mute p {
       margin: 0;
   }

   /* MAIN */
   /* board equalizer */
   .screen-channels-wrapper {
       margin-top: 30px;
       display: flex;
       justify-content: space-between;
       max-width: 97%;
   }

   .screen-channels-item {
       max-width: 96px;
       text-align: center;
       display: flex;
       flex-direction: column;
       justify-content: center;
       gap: 0;
       border-radius: 4px;
       overflow: hidden;
       box-shadow: 0 0 0 3px #00000014;
       cursor: pointer;
   }

   .equalizer-screens {
       display: flex;
       justify-content: center;
       align-items: flex-end;
   }

   .bottom-screen-info {
       background: #000;
       color: #fff;
       height: 32px;
       font-size: 12px;
       display: flex;
       justify-content: center;
       align-items: center;
   }

   .equalizer-board {
       position: relative;
       height: 138px;
       background: linear-gradient(rgba(206, 24, 30, 1) 0%,
               rgba(236, 140, 14, 1) 18%,
               rgba(248, 215, 6, 1) 38%,
               rgba(136, 195, 59, 1) 100%);
       overflow: hidden;
       z-index: -2;
   }

   .tab-channel-active {
       border: 3px solid #fff;
       box-shadow: none;
       max-width: 102px;
   }

   .equalizer-board img {
       width: 100%;
   }

   /* mixer */
   .main-item.draggers {
       margin-top: 25px;
       margin-bottom: 20px;
       display: flex;
   }

   .mixer-wrap {
       width: 12.5%;
       min-width: 120px;
   }

   .mixer-board {
       max-width: 88px;
   }

   .mixer-container {
       max-width: 120px;
       display: flex;
       gap: 5px;
       align-items: flex-end;
   }

   .mixer-container .inner-container {
       max-width: 88px;
       position: relative;
   }

   .mixer-container .inner-container .tooltip {
       position: absolute;
       z-index: 3;
       top: -12px !important;
       left: 16%;
       background: url(assets/icons/Union.svg);
       background-repeat: no-repeat;
       color: #fff;
       font-size: 12px;
       background-size: cover;
       width: 85px;
       height: 33px;
       display: flex;
       justify-content: center;
       align-items: center;
       padding-bottom: 5px;
       font-weight: bold;
   }

   .number-mixer {
       color: #fff;
       font-size: 20px;
       line-height: 1;
   }

   .volume-board {
       max-width: 24px;
       width: 100%;
       height: 319px;
       border-radius: 10px;
       margin-bottom: 20px;
       background: linear-gradient(rgba(206, 24, 30, 1) 0%,
               rgba(236, 140, 14, 1) 18%,
               rgba(248, 215, 6, 1) 38%,
               rgba(136, 195, 59, 1) 100%);
       position: relative;
       overflow: hidden;
       z-index: -2;
   }

   .volume-board img {
       max-width: 24px;
       width: 100%;
   }

   .mixer-dragger {
       position: absolute;
       z-index: 2;
       bottom: -5px;
       left: 26px;
       max-width: 59px;
   }

   /* mixer channels */
   .channel-screen {
       width: 120px;
       height: 71px;
       background: #1f1f21;
       color: #fff;
       display: flex;
       justify-content: center;
       align-items: flex-end;
       padding-bottom: 10px;
       margin-bottom: 20px;
       border-radius: 4px;
       font-weight: 600;
       text-decoration: none;
   }

   /* mixer mute buttons */
   .main-board-buttons.mute-solo {
       max-width: 120px;
       display: flex;
       margin-top: 30px;
       justify-content: center;
       gap: 20px;
   }

   .main-board-buttons.mute-solo .mute-solo-item {
       width: 56px;
       height: 40px;
   }

   /* needle board and needle */

   .needle-screen {
       max-width: 120px;
       margin-bottom: 30px;
       position: relative;
       overflow: hidden;
       /* max-height: 55px;
        cursor: pointer;
        transition: max-height 0.4s ease-out; */
       max-height: 55px;
       /* Initial height */
       cursor: pointer;
       transition: 0.3s ease-in-out;
       border-radius: 4px;
   }

   .needle-screen:hover {
       max-height: 81px;
       overflow: visible;
       transition: height 0.3s ease;
   }

   .needle-path {
       width: 100%;
   }

   .needle-dragger {
       position: absolute;
       z-index: 1;
       top: 0;
       left: 46%;
   }

   /* ANIMATIONS */
   .btn-activated {
       background: #f5f5f5;
   }

   .overlay {
       position: absolute;
       border-radius: 0;
       top: 0;
       left: 0;
       width: 100%;
       height: 100%;
       background: #2a2a2a;
       /* Adjust opacity as needed */
       z-index: -1;
   }

   /* btn animation */
   .active-btn-shadow {
       box-shadow: 0 0 9.2px 1px #f5f5f5;
   }

   .active-btn-background {
       background: #f5f5f5;
       color: #222;
       font-weight: 600;
   }

   .btn-block.active-btn-background p {
       color: #222;
       font-weight: 600;
   }

   .btn-block.active-btn-background img {
       filter: invert(1);
   }

   /* send on faders */
   .send-faders-active-options.show-flex,
   .activated-faders.show-flex {
       display: flex;
   }

   .needle-screen.hide-if-faders,
   .sidebar-item.mixing.hide-if-faders {
       display: none;
   }

   /* TABS Switch channels */

   .main-item.draggers {
       display: none;
   }

   .main-item.draggers.tab-active {
       display: flex;
   }

   .screen-channels-item.tab-active {
       box-shadow: 0 0 0 3px #fff;
   }

   .btn-block.edit-dca-item {
       width: 100%;
       max-width: 120px;
       height: 44px;
       margin-bottom: 15px;
   }

   .edit-dca-wrap {
       height: 55px;
       margin-bottom: 30px;
   }

   /* tab content colors change */
   #channel-aux .channel-screen,
   #channel-fx .channel-screen {
       background: #c8ffa5;
       color: #171100;
   }

   #channel-bus18 .channel-screen,
   #channel-bus916 .channel-screen {
       background: #b9ffff;
       color: #171100;
   }

   #channel-mtx .channel-screen,
   #channel-dca .channel-screen {
       background: #ffc2ff;
       color: #171100;
   }

   #channel-mtx .mixer-wrap:nth-of-type(8) .channel-screen,
   #channel-mtx .mixer-wrap:nth-of-type(7) .channel-screen {
       background: #f2ede9;
       color: #171100;
   }

   #channel-mtx .mixer-wrap .needle-screen {
       visibility: hidden;
   }

   #channel-mtx .mixer-wrap:last-of-type .needle-screen {
       visibility: visible;
   }

   #channel-dca .mixer-wrap .needle-screen {
       display: none;
   }

   /*  TABS END  */
   /* MUTE disable */
   .muted.disabled,
   .no-faders-tab.disabled {
       pointer-events: none;
       opacity: 0.6;
   }

   /*  MUTE Disable end */

   /* SCENE PAGE */

   .below-main {
       background-color: #5a5a5a;
       padding: 10px 6px;
       position: relative;
   }

   .top-scene-part {
       background-color: #3f3f3f;
       display: flex;
   }

   .top-scene-part .show-name-bar {
       width: 40%;
       padding: 10px 12px;
   }

   .top-scene-part .drag-to-bar {
       width: 60%;
       display: flex;
       align-items: center;
   }

   .new-ticket,
   .current-ticket {
       width: 37%;
       display: flex;
       align-items: center;
   }

   .current-ticket img {
       max-width: 239px;
   }

   .vertical-letters {
       writing-mode: vertical-rl;
       transform: scale(-1);
       color: #fff;
       font-weight: 600;
       font-size: 13px;
   }

   .current-ticket {
       gap: 10px;
   }

   .drag-ticket {
       width: 26%;
       margin: 5px;
       padding: 5px;
       background: url(/assets/icons-scene/dashes-scene-ticket.svg);
       background-size: contain;
       background-position: center;
       background-repeat: no-repeat;
       height: 84px;
       display: flex;
       justify-content: center;
       align-items: center;
       color: #fff;
       font-weight: 600;
       gap: 10px;
   }

   /* names and buttons */
   .show-name-bar {
       display: flex;
       gap: 4%;
       border-right: 1px solid #ffffff1a;
       justify-content: center;
       align-items: center;
   }

   .show-name-bar .name-buttons {
       display: flex;
       width: 85%;
       flex-direction: column;
       gap: 10px;
   }

   .name-scene {
       display: flex;
       justify-content: flex-start;
       align-items: center;
       gap: 30px;
   }

   .reverse-button {
       width: 10%;
   }

   .reverse {
       max-width: 53px;
       height: 53px;
       border-radius: 4px;
       border: 3px solid #383838;
       display: flex;
       align-items: center;
       justify-content: center;
       cursor: pointer;
   }

   .reverse img {
       width: 50%;
   }

   .show-name {
       font-size: 18px;
       color: #fff;
       font-weight: 600;
       padding-left: 5px;
   }

   .scene-name {
       font-size: 14px;
       font-weight: 600;
       color: #fff;
       padding: 10px 15px;
       background-color: #383838;
       border-radius: 4px;
       width: 100%;
       max-width: 280px;
   }

   .buttons-scene {
       display: flex;
       gap: 15px;
   }

   .buttons-scene .btn-block,
   .btn-block-scene {
       border-radius: 4px;
       border: 3px solid #f5f5f5;
       background: #2d2d2f;
       color: #fff;
       text-transform: uppercase;
       font-size: 14px;
       height: 38px;
       width: 42px;
       display: flex;
       align-items: center;
       justify-content: center;
       cursor: pointer;
       max-width: 132px;
       width: 100%;
       height: 34px;
   }

   .active-btn-background {
       background: #f5f5f5;
       color: #222;
   }

   /* accordion part */
   .accordion-row {
       display: flex;
       height: 63px;
   }

   .numbered {
       width: 5%;
       padding-left: 15px;
       display: flex;
       align-items: center;
   }

   .named {
       width: 20%;
       padding-left: 15px;
       display: flex;
       align-items: center;
       border-right: 1px solid #515050;
       border-left: 1px solid #515050;
       border-top: 1px solid #515050;
   }

   .noted {
       width: 35%;
       padding-left: 15px;
       display: flex;
       align-items: center;
       border-right: 1px solid #515050;
       border-top: 1px solid #515050;
   }

   .pins {
       width: 40%;
       padding-left: 15px;
       display: flex;
       border-top: 1px solid #515050;
   }

   .accordion-row>div {
       color: #fff;
       font-weight: 600;
       font-size: 18px;
   }

   .named,
   .noted {
       outline: none;
   }

   .heading-row .named,
   .heading-row .noted,
   .heading-row .pins {
       border: none !important;
   }

   .accordion-row .pin-item {
       font-size: 10px;
       writing-mode: vertical-rl;
       transform: scale(-1);
       width: 12.4%;
       display: flex;
       align-items: center;
       justify-content: center;
       height: 63px;
   }

   .accordion-item-nav .numbered {
       cursor: pointer;
   }

   .pinned {
       background: #3f3f3f;
       height: 16px;
       width: 16px;
       border-radius: 100px;
       cursor: pointer;
   }

   .pinned-active {
       background: #f2ede9;
   }

   .accordion-item-content {
       max-height: 0;
       overflow: hidden;
       transition: max-height 0.3s ease-out;
       /* Adjust timing and easing as needed */
       background-color: #3f3f3f;
   }

   .accordion-item-content.activate-edit {
       max-height: 125px;
       /* Adjust max-height as needed */
   }

   .buttons-scene.accordion-opened {
       max-width: 400px;
       flex-wrap: wrap;
       padding: 20px 0 20px 5%;
   }

   .activate-edit .named,
   .activate-edit .noted {
       background: #3f3f3f;
   }

   /* test scroll bar for scene elements */

   /* Hide the scrollbar for WebKit browsers */
   /* Note: Adjust for Firefox using `scrollbar-width: none;` */
   .bottom-scene-part {
       height: 500px;
       overflow-y: scroll;
       position: relative;
   }

   .bottom-scene-part::-webkit-scrollbar {
       width: 5px;
   }

   /* Optional: Style the scrollbar track */
   .bottom-scene-part::-webkit-scrollbar-track {
       /* Set the track color to match the background */
       background: #f0f0f0;
       /* Change this to your background color */
   }

   /* Optional: Style the scrollbar thumb */

   .bottom-scene-part::-webkit-scrollbar-thumb {
       /* Make the thumb almost invisible */
       background: rgba(0, 0, 0, 0.3);
       /* Adjust opacity as needed */
       /* Set a minimum size for the thumb */
       min-height: 20px;
   }

   /* popups */
   .param-safe-wrapper,
   .chain-safe-wrapper {
       display: none;
       position: absolute;
       top: 115px;
       left: 0;
       width: 100%;
       height: 100%;
       z-index: 5;
       background: #5a5a5a;
   }

   .chain-safe-wrapper .param-safe-input {
       width: 46%;
   }

   .chain-safe-wrapper .param-safe-input_items_three_col {
       gap: 30px;
   }

   .visible_block {
       display: block;
   }

   .param-safe-flex {
       display: flex;
       width: 100%;
       max-width: 1290px;
       margin: 50px auto 30px;
       gap: 80px;
   }

   .param-safe-input {
       width: 55%;
   }

   .param-safe-buses {
       width: 17%;
   }

   .param-safe-console {
       width: 17%;
   }

   .param-safe-input_title {
       color: #fff;
       font-weight: bold;
       text-align: center;
   }

   .param-safe-input_items_three_col {
       display: flex;
       gap: 70px;
       justify-content: space-around;
   }

   .param-safe-input_items {
       display: flex;
       margin-top: 20px;
       flex-direction: column;
       gap: 20px;
   }

   .single_param_item {
       display: flex;
       align-items: center;
       gap: 20px;
       color: #fff;
       font-weight: 16px;
       font-weight: bold;
   }

   .active-btn-background {
       background: #f5f5f5 !important;
       color: #222 !important;
   }