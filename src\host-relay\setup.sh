#!/bin/bash
# VSE Host Relay Setup Script for macOS/Linux
# This script will set up and run the VSE Host Relay application

echo "VSE Host Relay Setup"
echo "===================="

# Check if Python 3 is installed
if ! command -v python3 &> /dev/null; then
    echo "Error: Python 3 is not installed"
    echo "Please install Python 3.7+ from your package manager or https://python.org"
    exit 1
fi

# Check Python version
python3 -c "import sys; exit(0 if sys.version_info >= (3,7) else 1)"
if [ $? -ne 0 ]; then
    echo "Error: Python 3.7 or higher is required"
    python3 --version
    exit 1
fi

echo "Python 3 detected, starting setup..."
python3 setup.py
