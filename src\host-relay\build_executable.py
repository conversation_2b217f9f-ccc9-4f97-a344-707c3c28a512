#!/usr/bin/env python3
"""
Build script for creating VSE Host Relay executables

Supports building for:
- Windows (.exe)
- macOS (.app)
- Linux (binary)

Uses PyInstaller for cross-platform executable generation.
"""

import os
import sys
import subprocess
import shutil
import platform
from pathlib import Path


class ExecutableBuilder:
    def __init__(self):
        self.system = platform.system().lower()
        self.script_dir = Path(__file__).parent
        self.app_name = "VSE Host Relay"
        self.script_file = "vse_host_relay.py"
        self.version = "1.0.0"
        
        # Output directories
        self.dist_dir = self.script_dir / "dist"
        self.build_dir = self.script_dir / "build"
        
        print(f"Building for {platform.system()} ({platform.machine()})")

    def check_dependencies(self):
        """Check if PyInstaller is installed"""
        try:
            import PyInstaller
            print(f"✓ PyInstaller {PyInstaller.__version__} found")
            return True
        except ImportError:
            print("✗ PyInstaller not found. Installing...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
                print("✓ PyInstaller installed successfully")
                return True
            except subprocess.CalledProcessError:
                print("✗ Failed to install PyInstaller")
                return False

    def create_spec_file(self):
        """Create PyInstaller spec file for customization"""
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['{self.script_file}'],
    pathex=[],
    binaries=[],
    datas=[],
    hiddenimports=[
        'websocket',
        'xair_api',
        'python_osc',
        'tkinter',
        'tkinter.ttk',
        'tkinter.scrolledtext',
        'queue',
        'threading',
        'json',
        'socket',
        'logging',
        'datetime'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.app_name.replace(" ", "_")}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console={'True' if self.system == 'linux' else 'False'},
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    version='version_info.txt' if self.system == 'windows' else None,
    icon='icon.ico' if self.system == 'windows' else 'icon.icns' if self.system == 'darwin' else None,
)

{'app = BUNDLE(exe, name="' + self.app_name + '.app", icon="icon.icns", bundle_identifier="com.vse.hostrelay")' if self.system == 'darwin' else ''}
'''
        
        spec_file = self.script_dir / f"{self.app_name.replace(' ', '_')}.spec"
        with open(spec_file, 'w') as f:
            f.write(spec_content)
        
        print(f"✓ Created spec file: {spec_file}")
        return spec_file

    def create_version_info(self):
        """Create version info file for Windows executable"""
        if self.system != 'windows':
            return
            
        version_info = f'''# UTF-8
#
# Version info for VSE Host Relay
#

VSVersionInfo(
  ffi=FixedFileInfo(
    filevers=({self.version.replace('.', ', ')}, 0),
    prodvers=({self.version.replace('.', ', ')}, 0),
    mask=0x3f,
    flags=0x0,
    OS=0x40004,
    fileType=0x1,
    subtype=0x0,
    date=(0, 0)
  ),
  kids=[
    StringFileInfo(
      [
      StringTable(
        u'040904B0',
        [StringStruct(u'CompanyName', u'VSE Suite'),
        StringStruct(u'FileDescription', u'VSE Host Relay Application'),
        StringStruct(u'FileVersion', u'{self.version}'),
        StringStruct(u'InternalName', u'vse_host_relay'),
        StringStruct(u'LegalCopyright', u'Copyright (C) 2024 VSE Suite'),
        StringStruct(u'OriginalFilename', u'VSE_Host_Relay.exe'),
        StringStruct(u'ProductName', u'VSE Host Relay'),
        StringStruct(u'ProductVersion', u'{self.version}')])
      ]), 
    VarFileInfo([VarStruct(u'Translation', [1033, 1200])])
  ]
)
'''
        
        version_file = self.script_dir / "version_info.txt"
        with open(version_file, 'w') as f:
            f.write(version_info)
        
        print(f"✓ Created version info: {version_file}")

    def build_executable(self):
        """Build the executable using PyInstaller"""
        print("Building executable...")
        
        # Clean previous builds
        if self.dist_dir.exists():
            shutil.rmtree(self.dist_dir)
        if self.build_dir.exists():
            shutil.rmtree(self.build_dir)
        
        # Create spec file
        spec_file = self.create_spec_file()
        
        # Create version info for Windows
        self.create_version_info()
        
        # Build command
        cmd = [
            sys.executable, "-m", "PyInstaller",
            "--clean",
            "--noconfirm",
            str(spec_file)
        ]
        
        try:
            # Run PyInstaller
            result = subprocess.run(cmd, cwd=self.script_dir, capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✓ Executable built successfully!")
                self.post_build_actions()
            else:
                print("✗ Build failed!")
                print("STDOUT:", result.stdout)
                print("STDERR:", result.stderr)
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"✗ Build failed with error: {e}")
            return False
        
        return True

    def post_build_actions(self):
        """Perform post-build actions"""
        print("Performing post-build actions...")
        
        # Find the built executable
        if self.system == 'darwin':
            app_path = self.dist_dir / f"{self.app_name}.app"
            if app_path.exists():
                print(f"✓ macOS app bundle created: {app_path}")
        else:
            exe_name = f"{self.app_name.replace(' ', '_')}"
            if self.system == 'windows':
                exe_name += ".exe"
            
            exe_path = self.dist_dir / exe_name
            if exe_path.exists():
                print(f"✓ Executable created: {exe_path}")
                
                # Make executable on Linux/Unix
                if self.system in ['linux', 'darwin']:
                    os.chmod(exe_path, 0o755)
        
        # Copy additional files
        self.copy_additional_files()
        
        # Create installer/package if requested
        self.create_installer()

    def copy_additional_files(self):
        """Copy additional files to distribution"""
        files_to_copy = [
            "README.md",
            "requirements.txt"
        ]
        
        for file_name in files_to_copy:
            src_file = self.script_dir / file_name
            if src_file.exists():
                dst_file = self.dist_dir / file_name
                shutil.copy2(src_file, dst_file)
                print(f"✓ Copied {file_name}")

    def create_installer(self):
        """Create platform-specific installer"""
        print("Creating installer...")
        
        if self.system == 'windows':
            self.create_windows_installer()
        elif self.system == 'darwin':
            self.create_macos_dmg()
        elif self.system == 'linux':
            self.create_linux_package()

    def create_windows_installer(self):
        """Create Windows installer using NSIS (if available)"""
        try:
            # Check if NSIS is available
            subprocess.run(["makensis", "/VERSION"], capture_output=True, check=True)
            
            # Create NSIS script
            nsis_script = f'''
!define APP_NAME "{self.app_name}"
!define APP_VERSION "{self.version}"
!define APP_EXE "{self.app_name.replace(' ', '_')}.exe"

OutFile "VSE_Host_Relay_Setup_v{self.version}.exe"
InstallDir "$PROGRAMFILES\\VSE Suite\\Host Relay"

Page directory
Page instfiles

Section "Install"
    SetOutPath $INSTDIR
    File "dist\\${{APP_EXE}}"
    File "dist\\README.md"
    File "dist\\requirements.txt"
    
    CreateDirectory "$SMPROGRAMS\\VSE Suite"
    CreateShortCut "$SMPROGRAMS\\VSE Suite\\${{APP_NAME}}.lnk" "$INSTDIR\\${{APP_EXE}}"
    CreateShortCut "$DESKTOP\\${{APP_NAME}}.lnk" "$INSTDIR\\${{APP_EXE}}"
    
    WriteUninstaller "$INSTDIR\\uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "$INSTDIR\\${{APP_EXE}}"
    Delete "$INSTDIR\\README.md"
    Delete "$INSTDIR\\requirements.txt"
    Delete "$INSTDIR\\uninstall.exe"
    RMDir "$INSTDIR"
    
    Delete "$SMPROGRAMS\\VSE Suite\\${{APP_NAME}}.lnk"
    Delete "$DESKTOP\\${{APP_NAME}}.lnk"
    RMDir "$SMPROGRAMS\\VSE Suite"
SectionEnd
'''
            
            nsis_file = self.script_dir / "installer.nsi"
            with open(nsis_file, 'w') as f:
                f.write(nsis_script)
            
            # Build installer
            subprocess.run(["makensis", str(nsis_file)], check=True)
            print("✓ Windows installer created")
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("! NSIS not found - skipping Windows installer creation")
            print("  You can manually create an installer or use the executable directly")

    def create_macos_dmg(self):
        """Create macOS DMG package"""
        try:
            app_path = self.dist_dir / f"{self.app_name}.app"
            dmg_name = f"VSE_Host_Relay_v{self.version}.dmg"
            
            # Create DMG using hdiutil
            cmd = [
                "hdiutil", "create",
                "-volname", self.app_name,
                "-srcfolder", str(app_path),
                "-ov", "-format", "UDZO",
                str(self.dist_dir / dmg_name)
            ]
            
            subprocess.run(cmd, check=True)
            print(f"✓ macOS DMG created: {dmg_name}")
            
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("! Failed to create DMG - using app bundle directly")

    def create_linux_package(self):
        """Create Linux package (AppImage or tar.gz)"""
        try:
            # Create tar.gz package
            import tarfile
            
            exe_name = self.app_name.replace(' ', '_')
            package_name = f"VSE_Host_Relay_v{self.version}_linux.tar.gz"
            package_path = self.dist_dir / package_name
            
            with tarfile.open(package_path, 'w:gz') as tar:
                tar.add(self.dist_dir / exe_name, arcname=exe_name)
                
                # Add additional files
                for file_name in ["README.md", "requirements.txt"]:
                    file_path = self.dist_dir / file_name
                    if file_path.exists():
                        tar.add(file_path, arcname=file_name)
            
            print(f"✓ Linux package created: {package_name}")
            
        except Exception as e:
            print(f"! Failed to create Linux package: {e}")

    def run(self):
        """Main build process"""
        print(f"VSE Host Relay Executable Builder v{self.version}")
        print("=" * 50)
        
        if not self.check_dependencies():
            return False
        
        if not self.build_executable():
            return False
        
        print("\n" + "=" * 50)
        print("Build completed successfully!")
        print(f"Output directory: {self.dist_dir}")
        
        return True


def main():
    builder = ExecutableBuilder()
    success = builder.run()
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
