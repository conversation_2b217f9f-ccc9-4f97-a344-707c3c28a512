// EQ Control Integration
document.addEventListener('DOMContentLoaded', function() {
  const visualizer = window.eqVisualizer;
  if (!visualizer) {
    console.error('EQ Visualizer not initialized!');
    return;
  }
  
  let activeBandIndex = 0;
  const modeButton = document.getElementById('channel-one-eq-lshv');
  
  
  const stateCycle = [
    { type: 'lowshelf', mode: null, label: 'LSHV' },
    { type: 'highpass', mode: null, label: 'LCUT' },
    { type: 'peak', mode: 'PEQ', label: 'PEQ' },
    { type: 'peak', mode: 'VEQ', label: 'VEQ' },
    { type: 'highshelf', mode: null, label: 'HShv' },
    { type: 'lowpass', mode: null, label: 'HCut' },
  ];
  
  function getCurrentStateIndex(band) {
    return stateCycle.findIndex(state => 
      state.type === band.type && 
      (state.mode === band.mode || (!state.mode && !band.mode))
    );
  }
  
  const knobMap = {
    'wheel-ch1-gain-4': function(angle) {
      if (document.getElementById('wheel-ch1-gain-4').classList.contains('disabled')) return;
      const gain = (angle + 140) / 280 * 30 - 15;
      visualizer.updateBand(activeBandIndex, { gain });
      console.log(`Band ${activeBandIndex} gain set to ${gain.toFixed(1)}dB`);
    },
    'wheel-ch1-freq': function(angle) {
      const normalizedValue = (angle + 140) / 280;
      const minLog = Math.log10(20);
      const maxLog = Math.log10(20000);
      const freq = Math.pow(10, minLog + normalizedValue * (maxLog - minLog));
      visualizer.updateBand(activeBandIndex, { freq });
      const filterMarker = document.querySelector(`.filter-marker-${activeBandIndex + 1}`);
      if (filterMarker && document.querySelector('.movement-board')) {
        const movementBoard = document.querySelector('.movement-board');
        const normalizedX = (Math.log10(freq) - minLog) / (maxLog - minLog);
        filterMarker.style.left = (normalizedX * movementBoard.clientWidth - 10) + 'px';
      }
      console.log(`Band ${activeBandIndex} frequency set to ${freq.toFixed(1)}Hz`);
    },
    'wheel-ch1-width': function(angle) {
      if (document.getElementById('wheel-ch1-width').classList.contains('disabled')) return;
      const normalizedValue = (angle + 140) / 280;
      const q = 0.1 * Math.pow(100, normalizedValue);
      visualizer.updateBand(activeBandIndex, { Q: q });
      console.log(`Band ${activeBandIndex} width set to Q=${q.toFixed(2)}`);
    }
  };
  
  function connectKnobsToEQ() {
    const wheelKnobs = document.querySelectorAll('.wheel-knob');
    wheelKnobs.forEach(knob => {
      const knobId = knob.id;
      if (knobMap[knobId]) {
        const observer = new MutationObserver(mutations => {
          mutations.forEach(mutation => {
            if (mutation.attributeName === 'style') {
              const angle = parseFloat(knob.style.transform.replace('rotate(', '').replace('deg)', ''));
              if (!isNaN(angle)) knobMap[knobId](angle);
            }
          });
        });
        observer.observe(knob, { attributes: true, attributeFilter: ['style'] });
      }
    });
  }
  
  // Update the band selector mapping
  function connectBandSelectors() {
    window.activeBandIndex = activeBandIndex;
    const bandButtons = {
      'channel-one-eq-low': 0,
      'channel-one-eq-low2': 1,
      'channel-one-eq-lomid': 2,
      'channel-one-eq-himid': 3,
      'channel-one-eq-high2': 4,
      'channel-one-eq-high': 5,
    };
    
    for (const [buttonId, bandIndex] of Object.entries(bandButtons)) {
      const button = document.getElementById(buttonId);
      if (button) {
        button.addEventListener('click', function() {
          Object.keys(bandButtons).forEach(id => document.getElementById(id)?.classList.remove('active-band'));
          this.classList.add('active-band');
          activeBandIndex = bandIndex;
          window.activeBandIndex = bandIndex;
          console.log(`Switched to band ${bandIndex} (${visualizer.bands[bandIndex].name})`);
          updateKnobsFromBand(bandIndex);
          updateModeButton();
          updateControlVisibility();
        });
      }
    }
  }
  
  function updateKnobsFromBand(bandIndex) {
    const band = visualizer.bands[bandIndex];
    if (!band) return;
    
    const gainKnob = document.getElementById('wheel-ch1-gain-4');
    if (gainKnob && !gainKnob.classList.contains('disabled')) {
      const gainAngle = (band.gain + 15) / 30 * 280 - 140;
      gainKnob.style.transform = `rotate(${gainAngle}deg)`;
      updateProgressPath(gainAngle, gainKnob);
    }
    
    const freqKnob = document.getElementById('wheel-ch1-freq');
    if (freqKnob) {
      const minLog = Math.log10(20);
      const maxLog = Math.log10(20000);
      const normalizedValue = (Math.log10(band.freq) - minLog) / (maxLog - minLog);
      const freqAngle = normalizedValue * 280 - 140;
      freqKnob.style.transform = `rotate(${freqAngle}deg)`;
      updateProgressPath(freqAngle, freqKnob);
    }
    
    const widthKnob = document.getElementById('wheel-ch1-width');
    if (widthKnob) {
      const normalizedValue = Math.log10(band.Q / 0.1) / Math.log10(100);
      const widthAngle = normalizedValue * 280 - 140;
      widthKnob.style.transform = `rotate(${widthAngle}deg)`;
      updateProgressPath(widthAngle, widthKnob);
    }
  }
  
  window.updateKnobsFromBand = updateKnobsFromBand;
  
  function updateProgressPath(angle, knob) {
    if (!knob) return;
    const progressPath = knob.closest('.black-circle')?.querySelector('.progress-path');
    if (progressPath) {
      const percentage = (angle + 140) / 280;
      const pathLength = 208.4;
      progressPath.style.strokeDashoffset = pathLength - (percentage * pathLength);
    }
  }
  
  function connectEQButtons() {
    const resetButton = document.getElementById('channel-one-eq-reset');
    if (resetButton) {
      resetButton.addEventListener('click', function() {
        console.log("Reset button clicked, resetting all EQ settings");
        
        // Reset all bands to original values
        for (let i = 0; i < visualizer.bands.length; i++) {
          const originalBand = {
            type: visualizer.bands[i].originalType,
            mode: visualizer.bands[i].originalMode,
            freq: visualizer.bands[i].originalFreq,
            Q: visualizer.bands[i].originalQ,
            gain: visualizer.bands[i].originalGain
          };
          visualizer.updateBand(i, originalBand);
        }
        
        // Reset any global filters (like Lo CUT if implemented)
        if (visualizer.highpassFilter) {
          visualizer.highpassFilter.enabled = false;
          visualizer.highpassFilter.freq = 80; // Reset to default value
          visualizer.highpassFilter.Q = 0.707;
          
          // If there's a Lo CUT button, update its visual state
          const locutButton = document.getElementById('channel-one-eq-locut');
          if (locutButton) {
            locutButton.classList.remove('active');
            locutButton.style.backgroundColor = '#000000';
          }
          
          // If there's a Lo CUT marker/region, update them
          const lowcutMarker = document.querySelector('.lowcut-marker');
          const lowCutRegion = document.querySelector('.low-cut-region');
          if (lowcutMarker) {
            lowcutMarker.style.borderLeft = '2px dotted white';
            lowcutMarker.style.zIndex = '4';
          }
          if (lowCutRegion) {
            lowCutRegion.style.display = 'none';
          }
        }
        
        // Update UI to reflect reset state
        updateKnobsFromBand(activeBandIndex);
        updateModeButton();
        updateControlVisibility();
        
        // Force redraw
        visualizer.draw();
      });
    }
  }
  
  function updateModeButton() {
    const band = visualizer.bands[activeBandIndex];
    if (!band || !modeButton) return;
    
    const currentIndex = getCurrentStateIndex(band);
    if (currentIndex !== -1) {
      modeButton.textContent = stateCycle[currentIndex].label;
    } else {
      modeButton.textContent = 'Unknown';
    }
    
    modeButton.onclick = function() {
      let nextIndex;
      if (currentIndex === -1) {
        nextIndex = 0;
      } else {
        nextIndex = (currentIndex + 1) % stateCycle.length;
      }
      const nextState = stateCycle[nextIndex];
      visualizer.updateBand(activeBandIndex, { type: nextState.type, mode: nextState.mode });
      updateModeButton();
      updateControlVisibility();
      updateKnobsFromBand(activeBandIndex);
    };
  }
    
  function updateControlVisibility() {
    const band = visualizer.bands[activeBandIndex];
    const gainKnob = document.getElementById('wheel-ch1-gain-4');
    const widthKnob = document.getElementById('wheel-ch1-width');
    const freqKnob = document.getElementById('wheel-ch1-freq');
    
    // Get parent elements for each knob
    const gainParent = gainKnob?.closest('.wheel-item');
    const widthParent = widthKnob?.closest('.wheel-item');
    const freqParent = freqKnob?.closest('.wheel-item');
    
    // Get associated images
    const gainImage = document.querySelector('.gain-knob-img');
    const widthImage = document.querySelector('.width-knob-img');
    const freqImage = document.querySelector('.freq-knob-img');
    
    if (!band || !gainKnob || !widthKnob) return;
    
    // For highpass/lowpass filter types (no gain control)
    if (band.type === 'highpass' || band.type === 'lowpass') {
      // Disable the knob
      gainKnob.classList.add('disabled');
      
      // Hide the parent container
      if (gainParent) {
        gainParent.classList.add('hidden');
        
        // Hide the associated image
        if (gainImage) gainImage.classList.add('hidden');
      }
    } else {
      // Enable the knob
      gainKnob.classList.remove('disabled');
      
      // Show the parent container
      if (gainParent) {
        gainParent.classList.remove('hidden');
        
        // Show the associated image
        if (gainImage) gainImage.classList.remove('hidden');
      }
    }
    
    // For non-peak filters or shelf filters, hide width control
    if (band.type === 'highpass' || band.type === 'lowpass' || 
        band.type === 'lowshelf' || band.type === 'highshelf' || 
        band.mode === null) {
      // Disable the knob
      widthKnob.classList.add('disabled');
      
      // Hide the parent container
      if (widthParent) {
        widthParent.classList.add('hidden');
        
        // Hide the associated image
        if (widthImage) widthImage.classList.add('hidden');
      }
    } else {
      // Enable the knob
      widthKnob.classList.remove('disabled');
      
      // Show the parent container
      if (widthParent) {
        widthParent.classList.remove('hidden');
        
        // Show the associated image
        if (widthImage) widthImage.classList.remove('hidden');
      }
    }
    
    // Log the state for debugging
    console.log(`Control visibility updated for band ${activeBandIndex}:`, {
      type: band.type,
      mode: band.mode,
      gainHidden: gainParent?.classList.contains('hidden'),
      widthHidden: widthParent?.classList.contains('hidden')
    });
  }
  
  window.updateModeButton = updateModeButton;
  window.updateControlVisibility = updateControlVisibility;
  
  // Updated initializeEqUI function with extra image handling
  
  function initializeEqUI() {
    // First, connect all event handlers
    connectKnobsToEQ();
    connectBandSelectors();
    connectEQButtons();
    
    // Then trigger a click on the default band (LOW)
    const firstBandButton = document.getElementById('channel-one-eq-low');
    if (firstBandButton) {
      firstBandButton.click();
    } else {
      // Fallback if button doesn't exist
      activeBandIndex = 0;
      window.activeBandIndex = 0;
      updateKnobsFromBand(0);
      updateModeButton();
      updateControlVisibility();
    }
    
    // Ensure the width knob is properly hidden on initial load if needed
    // This is a failsafe in case the click handler didn't work correctly
    setTimeout(() => {
      const band = visualizer.bands[activeBandIndex];
      const widthKnob = document.getElementById('wheel-ch1-width');
      const widthImage = document.querySelector('.width-knob-img');
      const widthParent = widthKnob?.closest('.wheel-item');
      
      if (widthKnob && band && 
          (band.type === 'lowshelf' || band.type === 'highshelf')) {
        // Disable the knob
        widthKnob.classList.add('disabled');
        
        // Hide the parent container
        if (widthParent) {
          widthParent.classList.add('hidden');
        }
        
        // Hide the associated image
        if (widthImage) {
          widthImage.classList.add('hidden');
        }
        
        console.log("Ensuring width knob is hidden for shelf filter on initial load");
      }
    }, 200);
  }
  
  initializeEqUI();
  
  document.querySelectorAll('.wrapper_lines').forEach(line => {
    line.addEventListener('mousedown', function(e) {
      const bounds = this.closest('.movement-board').getBoundingClientRect();
      const x = e.clientX - bounds.left;
      const y = e.clientY - bounds.top;
      const minLog = Math.log10(20);
      const maxLog = Math.log10(20000);
      const freq = Math.pow(10, minLog + (x / bounds.width) * (maxLog - minLog));
      const gain = (1 - (y / bounds.height)) * 30 - 15;
      visualizer.updateBand(activeBandIndex, { freq, gain });
      updateKnobsFromBand(activeBandIndex);
    });
  });
});