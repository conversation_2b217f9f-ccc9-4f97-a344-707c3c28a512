output "ecs_cluster_name" {
  description = "The name of the ECS cluster"
  value       = aws_ecs_cluster.vse_cluster.name
}

output "ecs_service_name" {
  description = "The name of the ECS service"
  value       = aws_ecs_service.vse_service.name
}

# Outputs
output "alb_dns_name" {
  description = "The DNS name of the ALB"
  value       = aws_lb.vse_alb.dns_name
}

output "route53_nameservers" {
  description = "Nameservers for the Route 53 hosted zone - UPDATE YOUR DOMAIN REGISTRAR"
  value       = aws_route53_zone.vse_zone.name_servers
}

output "certificate_info" {
  description = "Let's Encrypt certificate info"
  value       = "Certificates managed automatically by Traefik + Let's Encrypt"
}

output "traefik_dashboard" {
  description = "Traefik dashboard URL (for debugging)"
  value       = "http://${aws_lb.vse_alb.dns_name}:8080"
}

output "domain_name" {
  description = "The domain name for the application"
  value       = "https://vseaudiobeta.com"
}

output "internet_access" {
  description = "Internet access method"
  value       = "NAT Gateway enabled for outbound internet access"
}