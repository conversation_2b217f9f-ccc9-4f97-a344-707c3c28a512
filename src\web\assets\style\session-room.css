    body,
    a,
    p,
    div,
    h2 {
        font-family: "Open Sans", sans-serif;
    }

    h2 {
        font-size: 28px;
        font-weight: 500;
    }

    html,
    body,
    * {
        padding: 0;
        margin: 0;
        box-sizing: border-box;
    }

    .main-wrapper {
        height: 100vh;
    }

    .body-wrapper {
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #858dbe;
    }

    .main-wrapper .header {
        background: rgb(255, 155, 4);
        background: linear-gradient(270deg,
                rgba(255, 155, 4, 1) 0%,
                rgba(255, 214, 1, 1) 14%,
                rgba(187, 180, 45, 1) 23%,
                rgba(111, 142, 95, 1) 33%,
                rgba(0, 87, 168, 1) 47%,
                rgba(56, 6, 250, 1) 63%,
                rgba(13, 0, 64, 1) 90%);
        height: 58px;
        width: 100%;
    }

    .form-box {
        max-width: 480px;
        width: 100%;
        height: auto;
        min-height: 360px;
        padding: 30px 0;
        background: #0d0040;
        border-radius: 10px;
        color: #fff;
        display: flex;
        align-items: center;
        flex-direction: column;
    }

    .form-box img {
        margin: 12px;
    }

    form {
        margin-top: 30px;
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        max-width: 373px;
        width: 100%;
    }

    input {
        margin-bottom: 40px;
        background: transparent !important;
        border: none;
        border-bottom: 1px solid #fff;
        padding: 10px 10px 10px 0;
        outline: none;
        color: #fff;
        font-family: "Open Sans", sans-serif;
        max-width: 373px;
        width: 100%;
        height: 40px;
        font-size: 16px;
    }

    .login-form input:focus {
        background-color: transparent;
        outline: none;
        /* Removes the default focus outline */
    }

    .login-form input[type="email"]:not(:placeholder-shown),
    .login-form input[type="password"]:not(:placeholder-shown) {
        background-color: transparent;
    }

    button[type="submit"] {
        background-color: #ff9b04;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        max-width: 373px;
        width: 100%;
        height: 62px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        outline: none;
        border: none;
        font-family: "Open Sans", sans-serif;
        cursor: pointer;
    }

    .form-box h2 {
        text-align: left;
        width: 373px;
        text-transform: uppercase;
        font-size: 24px;
    }

    form div {
        width: 100%;
        height: 30px;
        margin-bottom: 20px;
    }

    .login-form input[type="checkbox"] {
        margin-right: 10px;
    }

    ::placeholder {
        color: #fff;
        opacity: 1;
        /* For older browsers */
        font-family: "Open Sans", sans-serif;
        font-size: 16px;
    }

    :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #fff;
    }

    ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #fff;
    }

    .form-box p {
        margin: 10px 0;
        text-align: center;
    }

    .form-box p a {
        color: #ffd601;
        font-weight: bold;
        font-size: 14px;
        text-decoration: none;
    }

    /* imgages */
    .img-left {
        margin-right: 120px;
    }

    .img-right {
        margin-left: 120px;
        margin-bottom: 20px;
    }

    /* popup session */
    .popup-session {
        opacity: 0;
        visibility: hidden;
        z-index: -1;
        /* display: none; */
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(100vh - 200px);
        transition: 0.3s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .popup-session.active-session {
        opacity: 1;
        visibility: visible;
        z-index: 3;
    }

    .x-session-start {
        position: absolute;
        z-index: 5;
        cursor: pointer;
        top: 5%;
        left: 2%;
        width: 107px;
        height: 107px;
        border-radius: 100px;
        outline: none;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffffff21;
    }

    .x-session-start img {
        width: 16px;
    }

    /* session bar */
    .popup-session-bar {
        background: #fff;
        position: fixed;
        z-index: 999;
        bottom: -152px;
        left: 0;
        width: 100%;
        height: 72px;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        transition: bottom 0.3s ease-in-out;
    }

    .popup-session-content {
        background: rgb(20, 0, 95);
        background: linear-gradient(270deg,
                rgba(20, 0, 95, 0.4822303921568627) 0%,
                rgba(0, 66, 168, 0.4990371148459384) 100%);
        position: relative;
        width: 100%;
        height: 100%;
    }

    .inner-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        height: 100%;
    }

    .inner-bar>div {
        width: 33%;
        text-align: center;
    }

    .btn_orange_full {
        max-width: 190px;
        height: 46px;
        background: #ff9b04;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        border-radius: 4px;
        cursor: pointer;
    }

    .btn_account_wrap {
        display: flex;
        justify-content: flex-end;
        gap: 50px;
        padding-right: 30px;
    }

    .btn_account_wrap a {
        color: #fff;
        text-decoration: none;
        padding-bottom: 1px;
        border-bottom: 1px solid #fff;
    }

    .popup-session-bar.activated {
        bottom: 152px;
    }

    .verification-form {
        display: flex;
        flex-direction: column;
        align-items: center;
        margin: auto;
        gap: 50px;
    }

    .verification-code {
        display: flex;
        gap: 10px;
        justify-content: center;
        margin-bottom: 20px;
    }

    .code-input {
        width: 40px;
        height: 50px;
        text-align: center;
        font-size: 24px;
        border: 1px solid #ccc;
        border-radius: 5px;
    }

    .code-input:focus {
        outline: 1px solid #ff9b04;
    }

    .session-title {
        text-align: center !important;
        font-size: 40px !important;
        text-transform: capitalize !important;
        margin-bottom: 20px;
    }

    .btn_orange_full {
        max-width: 107px;
        width: 100%;
        height: 46px;
        background: #666796;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        border-radius: 4px;
        cursor: pointer;
    }

    .btn_orange_full.active_session {
        border: 2px solid #f2ede9;
        background: #ff9b04;
    }