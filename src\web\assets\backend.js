
let screenStream;
let videoTrack;
let animationFrameId;

const canvas = document.getElementById('popup-session');
const ctx = canvas.getContext('2d');
const startButton = document.getElementById('screen');
const stopButton = document.getElementById('stopScreenSharing');

// Function to start screen sharing
async function startScreenSharing() {
    // Add click event listener to the video button
    try {
        screenStream = await navigator.mediaDevices.getDisplayMedia({ video: true });
        videoTrack = screenStream.getVideoTracks()[0];

        // Attach the stream to a video element for rendering on the canvas
        const videoElement = document.createElement('video');
        videoElement.srcObject = screenStream;
        videoElement.play();

        // Render the screen content on the canvas
        const renderFrame = () => {
            ctx.drawImage(videoElement, 0, 0, canvas.width, canvas.height);
            animationFrameId = requestAnimationFrame(renderFrame);
        };

        renderFrame();

        // Enable stop button
        stopButton.disabled = false;
        startButton.disabled = true;

        videoTrack.onended = () => {
            stopScreenSharing();
        };
    } catch (error) {
        console.error('Error starting screen sharing:', error);
    }
}

// Function to stop screen sharing
function stopScreenSharing() {
    if (screenStream) {
        screenStream.getTracks().forEach((track) => track.stop());
        cancelAnimationFrame(animationFrameId);
        ctx.clearRect(0, 0, canvas.width, canvas.height);
    }

    // Reset buttons
    stopButton.disabled = true;
    startButton.disabled = false;
}

// Attach event listeners
//startButton.addEventListener('click', startScreenSharing);
//stopButton.addEventListener('click', stopScreenSharing);