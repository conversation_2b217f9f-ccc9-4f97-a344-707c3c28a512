/**
 * Audio EQ filter math library
 * Ported from Python implementation in sample.py
 */

const EQMath = {
  // Calculate frequency points (logarithmic scale)
  createFrequencyArray: function(count = 2048) {
    const freqs = [];
    for (let i = 0; i < count; i++) {
      // Logarithmic scale from 20Hz to 20kHz
      const t = i / (count - 1);
      freqs.push(20 * Math.pow(1000, t));
    }
    return freqs;
  },

  // Bell/Peak filter calculation
  bellFilter: function(frequencies, f0, Q, gainDb, fs = 48000) {
    const A = Math.pow(10, gainDb / 40);
    const w0 = 2 * Math.PI * f0 / fs;
    const alpha = Math.sin(w0) / (2 * Q);

    const b0 = 1 + alpha * A;
    const b1 = -2 * Math.cos(w0);
    const b2 = 1 - alpha * A;
    const a0 = 1 + alpha / A;
    const a1 = -2 * Math.cos(w0);
    const a2 = 1 - alpha / A;

    const b = [b0 / a0, b1 / a0, b2 / a0];
    const a = [1, a1 / a0, a2 / a0];

    return this.calculateFrequencyResponse(b, a, frequencies, fs);
  },

  // Low shelf filter calculation
  lowShelfFilter: function(frequencies, f0, Q, gainDb, fs = 48000) {
    const A = Math.pow(10, gainDb / 40);
    const w0 = 2 * Math.PI * f0 / fs;
    const alpha = Math.sin(w0) / (2 * Q);
    const cosW0 = Math.cos(w0);
    const sqrtA = Math.sqrt(A);

    const b0 = A * ((A + 1) - (A - 1) * cosW0 + 2 * sqrtA * alpha);
    const b1 = 2 * A * ((A - 1) - (A + 1) * cosW0);
    const b2 = A * ((A + 1) - (A - 1) * cosW0 - 2 * sqrtA * alpha);
    const a0 = (A + 1) + (A - 1) * cosW0 + 2 * sqrtA * alpha;
    const a1 = -2 * ((A - 1) + (A + 1) * cosW0);
    const a2 = (A + 1) + (A - 1) * cosW0 - 2 * sqrtA * alpha;

    const b = [b0 / a0, b1 / a0, b2 / a0];
    const a = [1, a1 / a0, a2 / a0];

    return this.calculateFrequencyResponse(b, a, frequencies, fs);
  },

  // High shelf filter calculation
  highShelfFilter: function(frequencies, f0, Q, gainDb, fs = 48000) {
    const A = Math.pow(10, gainDb / 40);
    const w0 = 2 * Math.PI * f0 / fs;
    const alpha = Math.sin(w0) / (2 * Q);
    const cosW0 = Math.cos(w0);
    const sqrtA = Math.sqrt(A);

    const b0 = A * ((A + 1) + (A - 1) * cosW0 + 2 * sqrtA * alpha);
    const b1 = -2 * A * ((A - 1) + (A + 1) * cosW0);
    const b2 = A * ((A + 1) + (A - 1) * cosW0 - 2 * sqrtA * alpha);
    const a0 = (A + 1) - (A - 1) * cosW0 + 2 * sqrtA * alpha;
    const a1 = 2 * ((A - 1) - (A + 1) * cosW0);
    const a2 = (A + 1) - (A - 1) * cosW0 - 2 * sqrtA * alpha;

    const b = [b0 / a0, b1 / a0, b2 / a0];
    const a = [1, a1 / a0, a2 / a0];

    return this.calculateFrequencyResponse(b, a, frequencies, fs);
  },

  // Low-pass filter
  lowPassFilter: function(frequencies, f0, Q, fs = 48000) {
    const w0 = 2 * Math.PI * f0 / fs;
    const alpha = Math.sin(w0) / (2 * Q);
    const cosW0 = Math.cos(w0);

    const b0 = (1 - cosW0) / 2;
    const b1 = 1 - cosW0;
    const b2 = (1 - cosW0) / 2;
    const a0 = 1 + alpha;
    const a1 = -2 * cosW0;
    const a2 = 1 - alpha;

    const b = [b0 / a0, b1 / a0, b2 / a0];
    const a = [1, a1 / a0, a2 / a0];

    return this.calculateFrequencyResponse(b, a, frequencies, fs);
  },

  // High-pass filter
  highPassFilter: function(frequencies, f0, Q, fs = 48000) {
    const w0 = 2 * Math.PI * f0 / fs;
    const alpha = Math.sin(w0) / (2 * Q);
    const cosW0 = Math.cos(w0);

    const b0 = (1 + cosW0) / 2;
    const b1 = -(1 + cosW0);
    const b2 = (1 + cosW0) / 2;
    const a0 = 1 + alpha;
    const a1 = -2 * cosW0;
    const a2 = 1 - alpha;

    const b = [b0 / a0, b1 / a0, b2 / a0];
    const a = [1, a1 / a0, a2 / a0];

    return this.calculateFrequencyResponse(b, a, frequencies, fs);
  },

  // Calculate frequency response for a filter
  calculateFrequencyResponse: function(b, a, frequencies, fs = 48000) {
    const response = [];
    
    for (let i = 0; i < frequencies.length; i++) {
      const f = frequencies[i];
      const w = 2 * Math.PI * f / fs;
      
      // Calculate complex response using Euler's formula
      let numerator = {real: 0, imag: 0};
      let denominator = {real: 0, imag: 0};
      
      for (let j = 0; j < b.length; j++) {
        numerator.real += b[j] * Math.cos(-j * w);
        numerator.imag += b[j] * Math.sin(-j * w);
      }
      
      for (let j = 0; j < a.length; j++) {
        denominator.real += a[j] * Math.cos(-j * w);
        denominator.imag += a[j] * Math.sin(-j * w);
      }
      
      // Complex division
      const magnitude = this.complexMagnitude(
        this.complexDivide(numerator, denominator)
      );
      
      response.push(magnitude);
    }
    
    return response;
  },
  
  // Helper: Complex division
  complexDivide: function(a, b) {
    const denom = b.real * b.real + b.imag * b.imag;
    return {
      real: (a.real * b.real + a.imag * b.imag) / denom,
      imag: (a.imag * b.real - a.real * b.imag) / denom
    };
  },
  
  // Helper: Complex magnitude
  complexMagnitude: function(c) {
    return Math.sqrt(c.real * c.real + c.imag * c.imag);
  },
  
  // Convert magnitude to dB
  magnitudeToDb: function(magnitude) {
    return 20 * Math.log10(magnitude);
  },
  
  // Calculate combined EQ response
  calculateCombinedResponse: function(frequencies, filters) {
    const combinedResponse = new Array(frequencies.length).fill(1);
    
    // Multiply all filter responses
    for (const filter of filters) {
      for (let i = 0; i < frequencies.length; i++) {
        combinedResponse[i] *= filter[i];
      }
    }
    
    // Convert to dB
    const dbResponse = combinedResponse.map(mag => this.magnitudeToDb(mag));
    return dbResponse;
  }
};

// Export for use in other modules
window.EQMath = EQMath;