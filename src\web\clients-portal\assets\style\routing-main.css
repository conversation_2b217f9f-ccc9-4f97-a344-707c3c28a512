     html,
     * {
         box-sizing: border-box;
     }

     body,
     a,
     p,
     div {
         font-family: "Open Sans", sans-serif;
     }

     body {
         background: #626262;
         margin: 0;
         padding: 0;
     }

     .page-wrapper {
         padding: 25px 20px;
         max-height: 100vh;
         max-width: 100%;
     }

     .container {
         display: flex;
         gap: 25px;
         flex-wrap: nowrap;
     }

     .left-sidebar {
         width: 10%;
         text-align: center;
     }

     .main {
         width: 90%;
     }

     /* buttons */

     .btn-block {
         border-radius: 4px;
         border: 3px solid #f5f5f5;
         background: #2d2d2f;
         color: #fff;
         text-transform: uppercase;
         font-size: 14px;
         height: 38px;
         width: 42px;
         display: flex;
         align-items: center;
         justify-content: center;
         cursor: pointer;
     }

     .btn-block p,
     .left-sidebar p {
         color: #fff;
         font-weight: 600;
         text-transform: uppercase;
         font-size: 14px;
     }

     /* main part header */
     .main-item.navbar {
         display: flex;
         justify-content: space-around;
         align-items: center;
     }

     .main-item.navbar a {
         display: flex;
         flex-direction: column;
         gap: 10px;
         justify-content: center;
         align-items: center;
         text-decoration: none;
         text-transform: uppercase;
         color: #fff;
         font-size: 14px;
         font-weight: bold;
     }

     .navbar-item-img {
         background: #000;
         height: 40px;
         width: 40px;
         border-radius: 100px;
         display: flex;
         align-items: center;
         justify-content: center;
         box-shadow: 0 0 0 3px #00000014;
     }

     .navbar-item-img.active_item {
         background: #fff;
         box-shadow: none;
     }

     .navbar-item-img.active_item img {
         filter: invert(1);
     }

     .navbar-item-img.homed img {
         margin-bottom: 2px;
         margin-left: 2px;
     }

     .scenes img {
         margin-top: 7px;
         margin-right: 7px;
     }

     .meters img {
         margin-bottom: 7px;
         margin-left: 5px;
         width: 32px;
     }

     /* sidebar items */
     .sidebar-item.controls {
         width: 100%;
         max-width: 130px;
         display: flex;
         align-items: center;
         justify-content: space-between;
         margin: 15px auto;
         border: 3px solid #f5f5f5;
         border-radius: 4px;
         gap: 0 !important;
     }

     .sidebar-item.controls .btn-block {
         border: none;
         width: 32%;
         border-radius: 0;
     }

     .sidebar-item.controls .btn-block:nth-of-type(2) {
         border-left: 3px solid #f5f5f5;
         border-right: 3px solid #f5f5f5;
         border-radius: 0 !important;
         width: 36%;
         margin: 0;
     }

     /* siebar - 4 buttons */
     .sidebar-item.addons {
         width: 100%;
         max-width: 134px;
         display: flex;
         align-items: center;
         justify-content: space-between;
         flex-wrap: wrap;
         margin: 20px auto;
         gap: 20px;
     }

     .sidebar-item.addons .btn-block {
         width: 42%;
         max-width: 56px;
         max-height: 36px;
     }

     /* fader */
     .activated-faders {
         width: 100%;
         max-width: 130px;
         min-height: 151px;
         margin: auto;
         background: #f5f5f5;
         box-shadow: 0 0 9.2px 1px #f5f5f5;
         display: flex;
         align-items: center;
         justify-content: center;
         flex-direction: column;
         gap: 10px;
         display: none;
     }

     .activated-faders img {
         width: 100%;
         max-width: 60%;
     }

     .btn-block.sidebar-item.faders {
         width: 100%;
         max-width: 134px;
         height: 46px;
         display: flex;
         align-items: center;
         margin: 20px auto;
     }

     .btn-block.sidebar-item.faders p {
         font-size: 12px;
     }

     /* 1-2 when fader activated */
     .send-faders-active-options {
         width: 100%;
         max-width: 120px;
         display: flex;
         align-items: center;
         justify-content: space-between;
         display: none;
     }

     .send-faders-active {
         width: 46%;
         max-width: 55px;
         height: 46px;
         margin-bottom: 20px;
     }

     /* X Y auto mixing */
     .sidebar-item.mixing {
         width: 100%;
         max-width: 134px;
         display: flex;
         align-items: center;
         justify-content: space-between;
         flex-wrap: wrap;
         margin: 45px auto;
     }

     .sidebar-item.mixing p {
         width: 100%;
     }

     .btn-block.mixing-item {
         width: 46%;
         max-width: 55px;
         height: 46px;
     }

     /* mute groups */
     .sidebar-item.mute-group {
         width: 100%;
         max-width: 134px;
         display: flex;
         align-items: center;
         justify-content: space-between;
         flex-wrap: wrap;
         margin: 20px auto;
         gap: 15px;
     }

     .sidebar-item.mute-group .btn-block {
         width: 46%;
         max-width: 55px;
         height: 55px;
     }

     .sidebar-item.mute-group p {
         width: 100%;
         margin-bottom: 0;
     }

     .sidebar-item.mute-group .last-mute {
         width: 100%;
         max-width: 100%;
     }

     /* solos  */
     .sidebar-item.show-solos {
         width: 100%;
         max-width: 134px;
         display: flex;
         align-items: center;
         justify-content: center;
         flex-direction: column;
         margin: 20px auto;
     }

     .sidebar-item.show-solos p {
         width: 100%;
     }

     .show-solos-item-wrap {
         max-width: 134px;
         width: 100%;
         display: flex;
         border: 3px solid #f5f5f5;
         border-radius: 4px;
     }

     .show-solos-item {
         border: none;
         width: 50%;
         border-radius: 0;
         background: #2d2d2f;
         color: #fff;
         text-transform: uppercase;
         font-size: 14px;
         height: 38px;
         cursor: pointer;
         display: flex;
         justify-content: center;
         align-items: center;
     }

     .show-solos-item .btn-block:last-of-type {
         border-left: 3px solid #f5f5f5;
         border-radius: 0 !important;
         margin: 0;
     }

     /* lock mutes big */
     .btn-block.sidebar-item.lock-mute {
         width: 100%;
         max-width: 134px;
         height: 46px;
         display: flex;
         align-items: center;
         flex-wrap: wrap;
         margin: 20px auto;
     }

     .btn-block.sidebar-item.lock-mute p {
         margin: 0;
     }

     /* MAIN */
     /* board equalizer */
     .screen-channels-wrapper {
         margin-top: 30px;
         display: flex;
         justify-content: space-between;
         max-width: 97%;
     }

     .screen-channels-item {
         max-width: 96px;
         text-align: center;
         display: flex;
         flex-direction: column;
         justify-content: center;
         gap: 0;
         border-radius: 4px;
         overflow: hidden;
         box-shadow: 0 0 0 3px #00000014;
         cursor: pointer;
     }

     .equalizer-screens {
         display: flex;
         justify-content: center;
         align-items: flex-end;
     }

     .bottom-screen-info {
         background: #000;
         color: #fff;
         height: 32px;
         font-size: 12px;
         display: flex;
         justify-content: center;
         align-items: center;
     }

     .equalizer-board {
         position: relative;
         height: 138px;
         background: linear-gradient(rgba(206, 24, 30, 1) 0%,
                 rgba(236, 140, 14, 1) 18%,
                 rgba(248, 215, 6, 1) 38%,
                 rgba(136, 195, 59, 1) 100%);
         overflow: hidden;
         z-index: -2;
     }

     .tab-channel-active {
         border: 3px solid #fff;
         box-shadow: none;
         max-width: 102px;
     }

     .equalizer-board img {
         width: 100%;
     }

     /* mixer */
     .main-item.draggers {
         margin-top: 25px;
         margin-bottom: 20px;
         display: flex;
     }

     .mixer-wrap {
         width: 12.5%;
         min-width: 120px;
     }

     .mixer-board {
         max-width: 88px;
     }

     .mixer-container {
         max-width: 120px;
         display: flex;
         gap: 5px;
         align-items: flex-end;
     }

     .mixer-container .inner-container {
         max-width: 88px;
         position: relative;
     }

     .mixer-container .inner-container .tooltip {
         position: absolute;
         z-index: 3;
         top: -12px !important;
         left: 16%;
         background: url(assets/icons/Union.svg);
         background-repeat: no-repeat;
         color: #fff;
         font-size: 12px;
         background-size: cover;
         width: 85px;
         height: 33px;
         display: flex;
         justify-content: center;
         align-items: center;
         padding-bottom: 5px;
         font-weight: bold;
     }

     .number-mixer {
         color: #fff;
         font-size: 20px;
         line-height: 1;
     }

     .volume-board {
         max-width: 24px;
         width: 100%;
         height: 319px;
         border-radius: 10px;
         margin-bottom: 20px;
         background: linear-gradient(rgba(206, 24, 30, 1) 0%,
                 rgba(236, 140, 14, 1) 18%,
                 rgba(248, 215, 6, 1) 38%,
                 rgba(136, 195, 59, 1) 100%);
         position: relative;
         overflow: hidden;
         z-index: -2;
     }

     .volume-board img {
         max-width: 24px;
         width: 100%;
     }

     .mixer-dragger {
         position: absolute;
         z-index: 2;
         bottom: -5px;
         left: 26px;
         max-width: 59px;
     }

     /* mixer channels */
     .channel-screen {
         width: 120px;
         height: 71px;
         background: #1f1f21;
         color: #fff;
         display: flex;
         justify-content: center;
         align-items: flex-end;
         padding-bottom: 10px;
         margin-bottom: 20px;
         border-radius: 4px;
         font-weight: 600;
         text-decoration: none;
     }

     /* mixer mute buttons */
     .main-board-buttons.mute-solo {
         max-width: 120px;
         display: flex;
         margin-top: 30px;
         justify-content: center;
         gap: 20px;
     }

     .main-board-buttons.mute-solo .mute-solo-item {
         width: 56px;
         height: 40px;
     }

     /* needle board and needle */

     .needle-screen {
         max-width: 120px;
         margin-bottom: 30px;
         position: relative;
         overflow: hidden;
         /* max-height: 55px;
        cursor: pointer;
        transition: max-height 0.4s ease-out; */
         max-height: 55px;
         /* Initial height */
         cursor: pointer;
         transition: 0.3s ease-in-out;
         border-radius: 4px;
     }

     .needle-screen:hover {
         max-height: 81px;
         overflow: visible;
         transition: height 0.3s ease;
     }

     .needle-path {
         width: 100%;
     }

     .needle-dragger {
         position: absolute;
         z-index: 1;
         top: 0;
         left: 46%;
     }

     /* ANIMATIONS */
     .btn-activated {
         background: #f5f5f5;
     }

     .overlay {
         position: absolute;
         border-radius: 0;
         top: 0;
         left: 0;
         width: 100%;
         height: 100%;
         background: #2a2a2a;
         /* Adjust opacity as needed */
         z-index: -1;
     }

     /* btn animation */
     .active-btn-shadow {
         box-shadow: 0 0 9.2px 1px #f5f5f5;
     }

     .active-btn-background {
         background: #f5f5f5;
         color: #222;
         font-weight: 600;
     }

     .btn-block.active-btn-background p {
         color: #222;
         font-weight: 600;
     }

     .btn-block.active-btn-background img {
         filter: invert(1);
     }

     /* send on faders */
     .send-faders-active-options.show-flex,
     .activated-faders.show-flex {
         display: flex;
     }

     .needle-screen.hide-if-faders,
     .sidebar-item.mixing.hide-if-faders {
         display: none;
     }

     /* TABS Switch channels */

     .main-item.draggers {
         display: none;
     }

     .main-item.draggers.tab-active {
         display: flex;
     }

     .screen-channels-item.tab-active {
         box-shadow: 0 0 0 3px #fff;
     }

     .btn-block.edit-dca-item {
         width: 100%;
         max-width: 120px;
         height: 44px;
         margin-bottom: 15px;
     }

     .edit-dca-wrap {
         height: 55px;
         margin-bottom: 30px;
     }

     /* tab content colors change */
     #channel-aux .channel-screen,
     #channel-fx .channel-screen {
         background: #c8ffa5;
         color: #171100;
     }

     #channel-bus18 .channel-screen,
     #channel-bus916 .channel-screen {
         background: #b9ffff;
         color: #171100;
     }

     #channel-mtx .channel-screen,
     #channel-dca .channel-screen {
         background: #ffc2ff;
         color: #171100;
     }

     #channel-mtx .mixer-wrap:nth-of-type(8) .channel-screen,
     #channel-mtx .mixer-wrap:nth-of-type(7) .channel-screen {
         background: #f2ede9;
         color: #171100;
     }

     #channel-mtx .mixer-wrap .needle-screen {
         visibility: hidden;
     }

     #channel-mtx .mixer-wrap:last-of-type .needle-screen {
         visibility: visible;
     }

     #channel-dca .mixer-wrap .needle-screen {
         display: none;
     }

     /*  TABS END  */
     /* MUTE disable */
     .muted.disabled,
     .no-faders-tab.disabled {
         pointer-events: none;
         opacity: 0.6;
     }

     /*  MUTE Disable end */

     /* Routing PAGE */
     .below-main {
         background-color: #626262;
         padding: 10px 6px;
     }

     .tab-content-hidden {
         display: none;
     }

     .tab-content-hidden.active-detail-tab {
         display: block;
     }

     .detail-tabs-navigation {
         width: 100%;
         display: flex;
         margin-top: 25px;
         gap: 0.5%;
     }

     .detail-tabs-navigation .details-tab-nav {
         width: 14.2%;
         background: #383838;
         color: #fff;
         text-align: center;
         height: 49px;
         border-radius: 4px 4px 0 0;
         display: flex;
         justify-content: center;
         align-items: center;
         cursor: pointer;
     }

     .details-tab-nav.active-detail-tab {
         background: #5a5a5a;
     }

     .tab-content-hidden {
         background: #5a5a5a;
         width: 100%;
         min-height: 671px;
         padding: 5px 30px 30px 20px;
     }

     /* routing in tab content */
     .home-tab-title {
         width: 80%;
         text-align: center;
         padding: 50px 0 30px;
     }

     .home-tab-title,
     .heading-list,
     .scrolled {
         color: #fff;
         font-weight: 600;
         font-size: 16px;
     }

     .processing-flex {
         max-width: 98%;
         margin: 10px auto;
         display: flex;
     }

     .single-list {
         width: 16.5%;
         min-width: 213px;
     }

     .single-item {
         height: 32px;
         display: flex;
         align-items: center;
         padding-left: 15px;
         cursor: pointer;
     }

     .single-item:nth-last-of-type(odd) {
         background: #414141;
     }

     .single-item:nth-last-of-type(even) {
         background: #383838;
     }

     .scroll-items {
         height: 512px;
         overflow-y: scroll;
         background-color: #2d2d2f;
     }

     .scroll-items::-webkit-scrollbar {
         display: none;
     }

     .heading-list {
         text-align: center;
         padding: 20px 1px;
     }

     .single-list.last-block {
         padding: 0 20px;
     }

     .connected-devices {
         display: flex;
         flex-direction: column;
         justify-content: center;
         align-items: center;
     }

     .connected-devices>div {
         width: 195px;
         height: 195px;
         background-color: #2d2d2f;
         display: flex;
         align-items: center;
         justify-content: center;
         flex-direction: column;
         margin-bottom: 35px;
         border-radius: 12px;
         border: 4px solid #f7f7f7;
         gap: 15px;
     }

     .connected-devices>div>div {
         color: #fff;
         font-weight: 600;
         font-size: 16px;
     }

     .recplay-block {
         display: flex;
         align-items: center;
         justify-content: center;
         gap: 25px;
     }

     .recplay-block>div {
         width: 52px;
         height: 51px;
     }

     /* PRESET Style */
     #rpreset-tab-content {
         padding-top: 40px;
     }

     .single-list.preset-first {
         width: 18%;
     }

     .single-list.preset-second {
         width: 60%;
         padding-top: 62px;
     }

     .single-list.preset-third {
         width: 18%;
         padding-top: 62px;
     }

     .single-list.preset-first .heading-list {
         text-align: left;
     }

     .single-list.preset-third .btn-block {
         width: 118px;
         height: 51px;
     }

     .single-list.preset-third .single_preset_item {
         display: flex;
         justify-content: center;
         align-items: center;
         gap: 30px;
         flex-direction: column;
     }

     .single-list.preset-first .single_preset_item {
         display: flex;
         align-items: center;
         margin-bottom: 15px;
         gap: 20px;
         color: #fff;
         font-weight: 16px;
         font-weight: bold;
     }

     .single-list.preset-first .btn-block {
         width: 51px;
         height: 51px;
     }

     /* Analog output */
     .settings {
         background-color: #383838;
     }

     /* animate */
     .scrolled.scrolled-active {
         background: #f7f7f7;
         color: #383838;
     }

     /* analog tab  */
     .scroll-items.settings {
         display: flex;
         flex-direction: column;
         align-items: center;
         justify-content: space-between;
         padding: 40px 0;
     }

     .btn-block.settings-off {
         width: 120px;
         height: 71px;
     }

     .btn-block.settings-phase {
         width: 155px;
         height: 51px;
     }

     .box-wheel-delay {
         display: flex;
         justify-content: flex-start;
         flex-direction: column;
         align-items: center;
         height: 350px;
         gap: 50px;
     }

     .inside-border-single {
         height: 100%;
         max-height: 514px;
         border: 4px solid #f7f7f7;
         border-radius: 12px;
         background: #2d2d2f;
     }

     .btn-block.delayed {
         width: 155px;
         height: 51px;
         margin: 0 auto;
     }

     .delay-bottom-block {
         height: 156px;
         display: flex;
         align-items: flex-end;
         justify-content: center;
         padding-bottom: 40px;
     }

     .delay-three {
         width: 155px;
         height: 128px;
         border-radius: 6px;
         border: 4px solid #f7f7f7;
         margin-top: 20px;
         display: flex;
         flex-direction: column;
         align-items: center;
         justify-content: center;
         color: #fff;
         gap: 10px;
     }

     .delay-three>div {
         display: flex;
         justify-content: space-between;
         width: 100%;
         max-width: 80%;
     }

     /* analog tab wheel */
     .wheel-item {
         width: 23%;
         margin-bottom: 30px;
         display: flex;
         flex-direction: column;
         justify-content: center;
         align-items: center;
     }

     .wheel-board {
         position: relative;
         text-align: center;
         width: 134px;
         height: 122px;
     }

     .wheel-numbers {
         max-width: 140px;
     }

     .black-circle {
         position: absolute;
         top: 58%;
         left: 50%;
         transform: translate(-50%, -50%);
         background: #1e1e1e;
         width: 89px;
         height: 89px;
         border-radius: 100px;
         display: flex;
         align-items: center;
         justify-content: center;
     }

     .wheel-knob {
         width: 57px;
         height: 57px;
         transform: rotate(-140deg);
     }

     .insert-container {
         padding-right: 20px;
     }

     #animated {
         position: absolute;
     }

     .single-list .wheel-item {
         width: 145px;
         margin-bottom: 30px;
         display: flex;
         flex-direction: column;
         justify-content: center;
         align-items: center;
     }

     .single-list .wheel-board {
         position: relative;
         text-align: center;
         width: 145px;
         height: 122px;
     }

     .single-list .wheel-numbers {
         max-width: 145px;
         width: 100%;
     }

     .single-list .black-circle {
         position: absolute;
         top: 58%;
         left: 49%;
         transform: translate(-50%, -50%);
         background: #1e1e1e;
         width: 85px;
         height: 85px;
         border-radius: 100px;
         display: flex;
         align-items: center;
         justify-content: center;
     }

     /* card */
     .single-list.extended-card-col {
         width: 32%;
         margin-left: 30px;
     }

     .single-list.extended-card-col .inside-border-single {
         display: flex;
         align-items: flex-start;
         justify-content: center;
         padding-top: 20px;
     }

     .single-list.extended-card-col .inside-border-single img {
         width: 100%;
         max-width: 369px;
     }