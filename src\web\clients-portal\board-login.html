<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Virtual Sound Client Board</title>
    <link rel="shortcut icon" href="assets/fav/fav-vse.png" />
    <link rel="stylesheet" href="assets/style/main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
      rel="stylesheet"
    />
    <!-- Include jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include jQuery UI via CDN -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Include touch-punch library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    <style>
      body,
      a,
      p,
      div,
      h2 {
        font-family: "Open Sans", sans-serif;
      }
      h2 {
        font-size: 28px;
        font-weight: 500;
      }
      html,
      body,
      * {
        padding: 0;
        margin: 0;
        box-sizing: border-box;
      }
      .main-wrapper {
        height: 100vh;
      }
      .body-wrapper {
        height: 100vh;
        display: flex;
        justify-content: center;
        align-items: center;
        background: #858dbe;
      }
      .main-wrapper .header {
        background: rgb(255, 155, 4);
        background: linear-gradient(
          270deg,
          rgba(255, 155, 4, 1) 0%,
          rgba(255, 214, 1, 1) 14%,
          rgba(187, 180, 45, 1) 23%,
          rgba(111, 142, 95, 1) 33%,
          rgba(0, 87, 168, 1) 47%,
          rgba(56, 6, 250, 1) 63%,
          rgba(13, 0, 64, 1) 90%
        );
        height: 58px;
        width: 100%;
      }
      .form-box {
        max-width: 480px;
        width: 100%;
        height: auto;
        min-height: 470px;
        padding: 30px 0;
        background: #0d0040;
        border-radius: 10px;
        color: #fff;
        display: flex;
        align-items: center;
        flex-direction: column;
      }
      .form-box img {
        margin: 12px;
      }
      form {
        margin-top: 30px;
        margin-bottom: 30px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        max-width: 373px;
        width: 100%;
      }
      input {
        margin-bottom: 40px;
        background: transparent !important;
        border: none;
        border-bottom: 1px solid #fff;
        padding: 10px 10px 10px 0;
        outline: none;
        color: #fff;
        font-family: "Open Sans", sans-serif;
        max-width: 373px;
        width: 100%;
        height: 40px;
        font-size: 16px;
      }
      .login-form input:focus {
        background-color: transparent;
        outline: none; /* Removes the default focus outline */
      }

      .login-form input[type="email"]:not(:placeholder-shown),
      .login-form input[type="password"]:not(:placeholder-shown) {
        background-color: transparent;
      }
      button[type="submit"] {
        background-color: #ff9b04;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        max-width: 373px;
        width: 100%;
        height: 62px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        outline: none;
        border: none;
        font-family: "Open Sans", sans-serif;
        cursor: pointer;
      }
      .form-box h2 {
        text-align: left;
        width: 373px;
        text-transform: uppercase;
        font-size: 24px;
      }
      form div {
        width: 100%;
        height: 30px;
        margin-bottom: 20px;
      }
      .login-form input[type="checkbox"] {
        margin-right: 10px;
      }
      ::placeholder {
        color: #fff;
        opacity: 1; /* For older browsers */
        font-family: "Open Sans", sans-serif;
        font-size: 16px;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #fff;
      }
      ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #fff;
      }

      .form-box p {
        margin: 10px 0;
        text-align: center;
      }
      .form-box p a {
        color: #ffd601;
        font-weight: bold;
        font-size: 14px;
        text-decoration: none;
      }
      /* imgages */
      .img-left {
        margin-right: 120px;
      }
      .img-right {
        margin-left: 120px;
        margin-bottom: 20px;
      }

      /* popup session */
      .popup-session {
        opacity: 0;
        visibility: hidden;
        z-index: -1;
        /* display: none; */
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: calc(100vh - 200px);
        transition: 0.3s ease-in-out;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .popup-session.active-session {
        opacity: 1;
        visibility: visible;
        z-index: 3;
      }
      .x-session-start {
        position: absolute;
        z-index: 5;
        cursor: pointer;
        top: 5%;
        left: 2%;
        width: 107px;
        height: 107px;
        border-radius: 100px;
        outline: none;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        background: #ffffff21;
      }
      .x-session-start img {
        width: 16px;
      }
      /* session bar */
      .popup-session-bar {
        background: #fff;
        position: fixed;
        z-index: 999;
        bottom: -152px;
        left: 0;
        width: 100%;
        height: 72px;
        box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
        transition: bottom 0.3s ease-in-out;
      }
      .popup-session-content {
        background: rgb(20, 0, 95);
        background: linear-gradient(
          270deg,
          rgba(20, 0, 95, 0.4822303921568627) 0%,
          rgba(0, 66, 168, 0.4990371148459384) 100%
        );
        position: relative;
        width: 100%;
        height: 100%;
      }
      .inner-bar {
        display: flex;
        align-items: center;
        justify-content: space-between;
        position: relative;
        height: 100%;
      }
      .inner-bar > div {
        width: 33%;
        text-align: center;
      }
      .btn_orange_full {
        max-width: 190px;
        height: 46px;
        background: #ff9b04;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: bold;
        border-radius: 4px;
        cursor: pointer;
      }
      .btn_account_wrap {
        display: flex;
        justify-content: flex-end;
        gap: 50px;
        padding-right: 30px;
      }
      .btn_account_wrap a {
        color: #fff;
        text-decoration: none;
        padding-bottom: 1px;
        border-bottom: 1px solid #fff;
      }
      .popup-session-bar.activated {
        bottom: 152px;
      }
    </style>
  </head>
  <body>
    <div class="main-wrapper login">
      <div class="body-wrapper">
        <div style="margin-bottom: 10%">
          <img
            src="assets/icons-popups/VSE-logo-light-big.svg"
            alt="logo-btn"
          />
        </div>
      </div>
    </div>

    <!--  popup -->
    <div id="popup-selection" class="popup-selection show">
      <div class="popup-content">
        <!-- <div id="profile-window" class="column-pop acc-pop">
          <img src="assets/icons-popups/account-swap.svg" alt="acc" />
        </div> -->
        <div
          id="app-window"
          class="column-pop preview-pop preview-window light-purple-bg selected"
        >
          <img src="assets/icons-popups/tune-screen.svg" alt="preview" />
        </div>
        <div id="video-window" class="column-pop purple-bg preview-window">
          <img src="assets/icons-popups/video.svg" alt="video" />
        </div>
        <div
          id="screen-share"
          class="column-pop light-purple-bg preview-window"
        >
          <img src="assets/icons-popups/screen.svg" alt="screen" />
        </div>
        <div id="chat-window" class="column-pop chat-pop">
          <img src="assets/icons-popups/chat.svg" alt="chat" />
        </div>
        <!-- <button id="close-pop-btn" class="close-pop-btn">
          <img src="assets/icons-popups/x-close.svg" alt="xlose" />
        </button> -->
      </div>
    </div>
    <!-- end popup -->

    <!-- popup login session -->
    <div id="popup-session" class="popup-session">
      <button id="x-session-start" class="x-session-start">
        <img src="assets/icons-popups/x-close.svg" alt="xlose" />
      </button>
      <div class="form-box">
        <!-- <div class="logo-btn">
          <img src="assets/icons-logo/dark-logo.svg" alt="logo" />
        </div>
        <img src="assets/icons-login/pulse-line.svg" alt="line" /> -->
        <h2>Mixing Board</h2>
        <form action="/">
          <input
            type="text"
            id="email"
            name="email"
            placeholder="IP Address*"
            required
            autocomplete="off"
          />

          <input
            type="text"
            id="password"
            name="password"
            placeholder="Status/Subnet Mask*"
            required
            autocomplete="off"
          />
          <input
            type="text"
            id="email"
            name="email"
            placeholder="Gateway*"
            required
            autocomplete="off"
          />

          <input
            type="text"
            id="password"
            name="Session key*"
            placeholder="Status/Subnet Mask*"
            required
            autocomplete="off"
          />
          <!-- <div>
            <input type="checkbox" id="agree" name="agree" required />
            <label for="agree">I agree to the terms and conditions</label>
          </div> -->
          <button type="submit">Connect</button>
        </form>
        <!-- <p>
          Don’t have an account<br />
          <a class="signup" href="#">Connect</a>
        </p> -->
        <!-- <p><a class="pass-reset" href="#">Forgot Password?</a></p> -->
      </div>
    </div>
    <!-- popup login end -->
    <!-- session bar  -->
    <div id="popup-session-bar" class="popup-session-bar activated">
      <div class="popup-session-content">
        <div class="inner-bar">
          <div></div>
          <div id="connect-board-id" class="btn_orange_full">
            Connect Mixing board
          </div>
          <div class="btn_account_wrap">
            <a href="#">Send Session Key</a>
            <a href="#">My Account</a>
          </div>
        </div>
      </div>
    </div>
    <!-- end session bar  -->

    <script>
      // MAIN HOME Screen popup
      document.addEventListener("DOMContentLoaded", () => {
        const infoButton = document.getElementById("info");
        const popup = document.getElementById("popup-selection");
        const closeButton = document.getElementById("close-pop-btn");
        const mainLogoButton = document.getElementById("main-logo-btn");

        const classesToToggle = ["active-btn-background", "active-btn-shadow"];

        function updateButtonClasses() {
          if (popup.classList.contains("show")) {
            infoButton.classList.add(...classesToToggle);
          } else {
            infoButton.classList.remove(...classesToToggle);
          }
        }

        closeButton.addEventListener("click", () => {
          popup.classList.remove("show");
          updateButtonClasses();
        });

        mainLogoButton.addEventListener("click", () => {
          popup.classList.toggle("show");
          updateButtonClasses();
        });

        infoButton.addEventListener("click", () => {
          popup.classList.toggle("show");
          updateButtonClasses();
        });
      });

      //   close session start
      // Add event listener to the button with ID 'connect-board-id'
      document
        .getElementById("connect-board-id")
        .addEventListener("click", function () {
          document
            .querySelector(".popup-session")
            .classList.add("active-session");
        });

      // Function to remove 'active-session' class
      function removeActiveSession() {
        document
          .querySelector(".popup-session")
          .classList.remove("active-session");
      }

      // Add event listener for the close (X) button or any element with class 'x-session-start'
      document.querySelectorAll(".x-session-start").forEach((element) => {
        element.addEventListener("click", removeActiveSession);
      });

      // Optionally, if you have a specific close button for "X" functionality, use this selector to attach the event
      document
        .querySelector(".close-button-selector")
        .addEventListener("click", removeActiveSession);
    </script>
  </body>
</html>
