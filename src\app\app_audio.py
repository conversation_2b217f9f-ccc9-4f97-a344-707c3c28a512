from flask import Flask, render_template, jsonify, request
import random
from OpenSSL import crypto
import secrets

# ------------------------------
# Generate a self-signed SSL certificate and key with SAN extension
# ------------------------------
key = crypto.PKey()
key.generate_key(crypto.TYPE_RSA, 2048)

cert = crypto.X509()
cert.get_subject().C = "US"
cert.get_subject().ST = "MyState"
cert.get_subject().L = "MyCity"
cert.get_subject().O = "MyCompany"
cert.get_subject().OU = "MyOrganizationalUnit"
cert.get_subject().CN = "127.0.0.1"  # or use 'localhost'
cert.set_serial_number(1)
cert.gmtime_adj_notBefore(0)
cert.gmtime_adj_notAfter(10 * 365 * 24 * 60 * 60)


san = b"DNS:localhost, IP:127.0.0.1"
cert.add_extensions([crypto.X509Extension(b"subjectAltName", False, san)])
cert.set_issuer(cert.get_subject())
cert.set_pubkey(key)
cert.sign(key, "sha256")

cert_file = "local.crt"
key_file = "local.key"

with open(cert_file, "wt") as f:
    f.write(crypto.dump_certificate(crypto.FILETYPE_PEM, cert).decode("utf-8"))
with open(key_file, "wt") as f:
    f.write(crypto.dump_privatekey(crypto.FILETYPE_PEM, key).decode("utf-8"))

# ------------------------------
# Initialize Flask App
# ------------------------------
app = Flask(__name__, template_folder="templates")
app.secret_key = "your-secret-key"

# ------------------------------
# Active Session and Helper Function
# ------------------------------
active_sessions = {}  # Each session key maps to a list of signaling messages


def generate_session_key():
    return str(secrets.randbelow(900000) + 100000)  # 6-digit numeric key


# ------------------------------
# Signaling Routes for WebRTC
# ------------------------------
@app.route("/create-session", methods=["POST"])
def create_session():
    session_key = generate_session_key()
    active_sessions[session_key] = []  # initialize message queue
    print("Generated session key (via /create-session):", session_key)
    return jsonify({"session_key": session_key})


@app.route("/validate-session", methods=["POST"])
def validate_session():
    data = request.get_json()
    session_key = data.get("session_key")
    if not session_key or not session_key.isdigit() or len(session_key) != 6:
        return jsonify({"valid": False, "message": "Invalid session key format"}), 400
    if session_key in active_sessions:
        return jsonify({"valid": True})
    active_sessions[session_key] = []
    return jsonify({"valid": True, "message": "Session key restored"})


@app.route("/join-session", methods=["POST"])
def join_session():
    data = request.get_json()
    session_key = data.get("session_key")
    if not session_key or not session_key.isdigit() or len(session_key) != 6:
        return jsonify({"error": "Invalid session key format"}), 400
    if session_key not in active_sessions:
        return jsonify({"error": "Session key does not exist"}), 404
    user_id = str(random.randint(10000000, 99999999))  # 8-digit numeric user ID
    # Optionally, you can store the user_id in the session's data
    print(f"User {user_id} joined session {session_key}")
    return jsonify(
        {"message": "Joined session", "session_key": session_key, "user_id": user_id}
    )


@app.route("/offer", methods=["POST"])
def offer():
    data = request.get_json()
    session_key = data.get("session_key")
    offer = data.get("offer")
    if session_key not in active_sessions:
        return jsonify({"error": "Session key does not exist"}), 404
    active_sessions[session_key].append({"type": "offer", "data": offer})
    return jsonify({"message": "Offer received successfully"})


@app.route("/answer", methods=["POST"])
def answer():
    data = request.get_json()
    session_key = data.get("session_key")
    answer = data.get("answer")
    if session_key not in active_sessions:
        return jsonify({"error": "Session key does not exist"}), 404
    active_sessions[session_key].append({"type": "answer", "data": answer})
    return jsonify({"message": "Answer received successfully"})


@app.route("/candidate", methods=["POST"])
def candidate():
    data = request.get_json()
    session_key = data.get("session_key")
    candidate = data.get("candidate")
    if session_key not in active_sessions:
        return jsonify({"error": "Session key does not exist"}), 404
    active_sessions[session_key].append({"type": "candidate", "data": candidate})
    return jsonify({"message": "Candidate received successfully"})


@app.route("/get-messages", methods=["POST"])
def get_messages():
    data = request.get_json()
    session_key = data.get("session_key")
    if session_key not in active_sessions:
        return jsonify({"error": "Session key does not exist"}), 404
    # Return any pending signaling messages then clear them
    messages = active_sessions[session_key]
    active_sessions[session_key] = []
    return jsonify({"messages": messages})


# ------------------------------
# Main Application Route (No Authentication)
# ------------------------------
@app.route("/")
def home():
    return render_template("index2.html")


@app.errorhandler(404)
def not_found(error):
    return "Page not found", 404


@app.route("/health")
def health_check():
    return jsonify({"status": "healthy", "message": "Application is running"}), 200


# ------------------------------
# Run the Application
# ------------------------------
if __name__ == "__main__":
    context = (cert_file, key_file)
    app.run(host="0.0.0.0", debug=True, ssl_context=context)
