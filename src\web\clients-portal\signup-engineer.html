<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Virtual Sound Client Login</title>
    <link rel="shortcut icon" href="assets/fav/fav-vse.png" />
    <link rel="stylesheet" href="assets/style/main.css" />
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link
      href="https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap"
      rel="stylesheet"
    />
    <!-- Include jQuery via CDN -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <!-- Include jQuery UI via CDN -->
    <script src="https://code.jquery.com/ui/1.12.1/jquery-ui.min.js"></script>
    <!-- Include touch-punch library -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jqueryui-touch-punch/0.2.3/jquery.ui.touch-punch.min.js"></script>
    <style>
      body,
      a,
      p,
      div,
      h2 {
        font-family: "Open Sans", sans-serif;
      }
      h2 {
        font-size: 40px;
        font-weight: 500;
        margin-bottom: 50px;
      }
      html,
      body,
      * {
        padding: 0;
        margin: 0;
        box-sizing: border-box;
      }
      .main-wrapper {
        height: auto;
        min-height: 100vh;
      }
      .body-wrapper {
        height: auto;
        min-height: 100vh;
        padding: 100px 20px;
        display: flex;
        justify-content: center;
        align-items: center;
        background: rgb(20, 0, 95);
        background: linear-gradient(
          270deg,
          rgba(20, 0, 95, 0.5018382352941176) 0%,
          rgba(0, 66, 168, 0.5018382352941176) 100%
        );
      }
      .main-wrapper .header {
        background: rgb(255, 155, 4);
        background: linear-gradient(
          270deg,
          rgba(255, 155, 4, 1) 0%,
          rgba(255, 214, 1, 1) 14%,
          rgba(187, 180, 45, 1) 23%,
          rgba(111, 142, 95, 1) 33%,
          rgba(0, 87, 168, 1) 47%,
          rgba(56, 6, 250, 1) 63%,
          rgba(13, 0, 64, 1) 90%
        );
        height: 58px;
        width: 100%;
      }
      .form-box {
        max-width: 550px;
        width: 100%;
        height: auto;
        min-height: 550px;
        padding: 30px 0;
        background: #0d0040;
        border-radius: 10px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: flex-sq;
        flex-direction: column;
      }
      .form-box img {
        margin: 12px;
      }
      form {
        margin-top: 30px;
        margin-bottom: 30px;
        display: flex;
        justify-content: center;
        flex-direction: column;
        max-width: 335px;
        width: 100%;
      }
      input[type="email"],
      input[type="password"],
      input[type="text"] {
        margin-bottom: 40px;
        background: transparent !important;
        border: none;
        border-bottom: 1px solid #fff;
        padding: 10px 10px 10px 0;
        outline: none;
        color: #fff;
        font-family: "Open Sans", sans-serif;
        max-width: 335px;
        width: 100%;
        height: 40px;
        font-size: 16px;
      }
      .login-form input[type="email"]:focus,
      .login-form input[type="password"]:focus {
        background-color: transparent;
        outline: none; /* Removes the default focus outline */
      }

      .login-form input[type="email"]:not(:placeholder-shown),
      .login-form input[type="password"]:not(:placeholder-shown) {
        background-color: transparent;
      }
      button[type="submit"] {
        background-color: #ff9b04;
        color: #fff;
        font-size: 16px;
        font-weight: 600;
        max-width: 335px;
        width: 100%;
        height: 62px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 8px;
        outline: none;
        border: none;
        font-family: "Open Sans", sans-serif;
        cursor: pointer;
      }
      form div {
        width: 100%;
        height: 30px;
        margin-bottom: 20px;
      }
      .login-form input[type="checkbox"] {
        margin-right: 10px;
      }
      ::placeholder {
        color: #fff;
        opacity: 1; /* For older browsers */
        font-family: "Open Sans", sans-serif;
        font-size: 12px;
      }
      :-ms-input-placeholder {
        /* Internet Explorer 10-11 */
        color: #fff;
        font-size: 12px;
      }
      ::-ms-input-placeholder {
        /* Microsoft Edge */
        color: #fff;
        font-size: 12px;
      }

      .form-box p {
        margin: 10px 0;
        text-align: center;
        font-size: 12px;
      }
      .checkbox-agree {
        margin-bottom: 50px;
      }
      .checkbox-agree label {
        font-weight: bold;
        font-size: 12px;
        text-decoration: none;
        color: #fff;
      }
      .checkbox-agree label a,
      .form-box p a {
        color: #ff9b04;
        font-weight: bold;
        font-size: 12px;
        text-decoration: none;
      }
      .form-box p .signup {
        display: inline-block;
        font-size: 14px;
        margin-top: 30px;
        text-decoration: underline !important;
      }
      .logo-btn img {
        max-width: 57px;
        margin-bottom: 30px;
      }
      .pass-lost {
        margin-bottom: 20px;
        display: inline-block;
        text-decoration: underline !important;
      }
    </style>
  </head>
  <body>
    <div class="main-wrapper login">
      <div class="header"></div>
      <div class="body-wrapper">
        <!-- <div class="img-left">
          <img src="assets/icons-login/lights-bkg-left.svg" alt="img" />
        </div> -->
        <div class="form-box">
          <div class="logo-btn">
            <img src="assets/icons-logo/dark-logo-notext.svg" alt="logo" />
          </div>
          <!-- <img src="assets/icons-login/pulse-line.svg" alt="line" /> -->
          <h2>Create Account</h2>
          <form action="/login-engineer.html">
            <input
              type="text"
              id="text"
              name="text"
              placeholder="First Name*"
              autocomplete="off"
            />
            <input
              type="text"
              id="text"
              name="text"
              placeholder="Last Name*"
              autocomplete="off"
            />
            <input
              type="text"
              id="text"
              name="text"
              placeholder="City*"
              autocomplete="off"
            />
            <input
              type="email"
              id="email"
              name="email"
              placeholder="Username (Email Address)*"
              required
              autocomplete="off"
            />

            <input
              type="password"
              id="password"
              name="password"
              placeholder="Create New Password*"
              required
              autocomplete="off"
            />
            <input
              type="password"
              id="password"
              name="password"
              placeholder="Confirm New Password*"
              required
              autocomplete="off"
            />
            <div class="checkbox-agree">
              <input type="checkbox" id="agree" name="agree" required />
              <label for="agree">
                By signing in, I agree to VSE’s
                <a href="#" class="yellow-txt">Privacy Statement </a>and
                <a href="#" class="yellow-txt">Terms of Service</a></label
              >
            </div>

            <button type="submit">Create Client Account</button>
          </form>
        </div>
        <!-- <div class="img-right">
          <img src="assets/icons-login/lights-bkg-right.svg" alt="img" />
        </div> -->
      </div>
    </div>
  </body>
</html>
