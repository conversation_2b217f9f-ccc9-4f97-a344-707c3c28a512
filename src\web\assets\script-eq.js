// script-eq.js
document.addEventListener('DOMContentLoaded', function() {
  const movementBoard = document.querySelector('.movement-board');
  
  if (movementBoard) {
    const eqCanvas = document.createElement('canvas');
    eqCanvas.id = 'eq-canvas';
    eqCanvas.width = movementBoard.clientWidth;
    eqCanvas.height = movementBoard.clientHeight;
    eqCanvas.style.position = 'absolute';
    eqCanvas.style.top = '0';
    eqCanvas.style.left = '0';
    eqCanvas.style.pointerEvents = 'none';
    
    const filterColors = ['#ff5500', '#ff8800', '#ffaa00', '#00aaff', '#00ccff', '#55ff00'];
    const filterLabels = ['1', 'L2', '2', '3', 'H2', '4'];
    
    for (let i = 0; i < 6; i++) {
      const filterMarker = document.createElement('div');
      filterMarker.className = `filter-marker filter-marker-${i+1}`;
      filterMarker.style.position = 'absolute';
      filterMarker.style.top = '0';
      filterMarker.style.height = '100%';
      filterMarker.style.width = '20px';
      filterMarker.style.zIndex = '3';
      filterMarker.style.cursor = 'ew-resize';
      filterMarker.setAttribute('data-band-index', i);
      
      const markerLabel = document.createElement('div');
      markerLabel.className = 'marker-label';
      markerLabel.textContent = filterLabels[i];
      markerLabel.style.width = '20px';
      markerLabel.style.height = '20px';
      markerLabel.style.borderRadius = '50%';
      markerLabel.style.backgroundColor = filterColors[i];
      markerLabel.style.color = '#fff';
      markerLabel.style.textAlign = 'center';
      markerLabel.style.fontWeight = 'bold';
      markerLabel.style.lineHeight = '20px';
      markerLabel.style.position = 'absolute';
      markerLabel.style.top = '0';
      markerLabel.style.left = '0';
      
      const dottedLine = document.createElement('div');
      dottedLine.className = 'dotted-line';
      dottedLine.style.height = '100%';
      dottedLine.style.width = '0';
      dottedLine.style.borderLeft = `2px dashed ${filterColors[i]}`;
      dottedLine.style.position = 'absolute';
      dottedLine.style.top = '20px';
      dottedLine.style.left = '10px';
      
      filterMarker.appendChild(markerLabel);
      filterMarker.appendChild(dottedLine);
      movementBoard.appendChild(filterMarker);
      
      function createClickHandler(bandIndex) {
        return function(e) {
          if (!window.isDragging) {
            const bandMap = {
              0: 'channel-one-eq-low',
              1: 'channel-one-eq-low2',
              2: 'channel-one-eq-lomid',
              3: 'channel-one-eq-himid',
              4: 'channel-one-eq-high2',
              5: 'channel-one-eq-high'
            };
            const button = document.getElementById(bandMap[bandIndex]);
            if (button) {
              button.click();
              document.querySelectorAll('.filter-marker').forEach(marker => marker.classList.remove('active-marker'));
              filterMarker.classList.add('active-marker');
            }
          }
        };
      }
      
      markerLabel.addEventListener('click', createClickHandler(i));
      dottedLine.addEventListener('click', createClickHandler(i));
      
      filterMarker.addEventListener('mousedown', function(e) {
        e.preventDefault();
        window.isDragging = false;
        const bandIndex = parseInt(this.getAttribute('data-band-index'));
        const initialX = e.clientX;
        const initialLeft = parseInt(window.getComputedStyle(this).left) || 0;
        const boardWidth = movementBoard.clientWidth;
        
        function moveHandler(moveEvent) {
          window.isDragging = true;
          const dx = moveEvent.clientX - initialX;
          let newLeft = Math.max(0, Math.min(boardWidth - 20, initialLeft + dx));
          filterMarker.style.left = newLeft + 'px';
          const normalizedX = (newLeft + 10) / boardWidth;
          const minLog = Math.log10(20);
          const maxLog = Math.log10(20000);
          const freq = Math.pow(10, minLog + normalizedX * (maxLog - minLog));
          
          if (window.eqVisualizer) {
            window.eqVisualizer.updateBand(bandIndex, { freq });
            if (bandIndex === window.activeBandIndex) {
              const freqKnob = document.getElementById('wheel-ch1-freq');
              if (freqKnob) {
                const normalizedValue = (Math.log10(freq) - minLog) / (maxLog - minLog);
                const freqAngle = normalizedValue * 280 - 140;
                freqKnob.style.transform = `rotate(${freqAngle}deg)`;
                const progressPath = freqKnob.closest('.black-circle')?.querySelector('.progress-path');
                if (progressPath) {
                  const percentage = (freqAngle + 140) / 280;
                  const pathLength = 208.4;
                  progressPath.style.strokeDashoffset = pathLength - (percentage * pathLength);
                }
              }
            }
          }
        }
        
        function upHandler() {
          document.removeEventListener('mousemove', moveHandler);
          document.removeEventListener('mouseup', upHandler);
          setTimeout(() => window.isDragging = false, 100);
        }
        
        document.addEventListener('mousemove', moveHandler);
        document.addEventListener('mouseup', upHandler);
      });
    }
    
    movementBoard.appendChild(eqCanvas);
    const visualizer = new EQVisualizer('#eq-canvas');
    window.eqVisualizer = visualizer;
    
    setTimeout(() => {
      const markers = document.querySelectorAll('.filter-marker');
      markers.forEach((marker, index) => {
        if (visualizer && visualizer.bands[index]) {
          const freq = visualizer.bands[index].freq;
          const minLog = Math.log10(20);
          const maxLog = Math.log10(20000);
          const normalizedX = (Math.log10(freq) - minLog) / (maxLog - minLog);
          marker.style.left = (normalizedX * movementBoard.clientWidth - 10) + 'px';
        }
      });
      document.getElementById('channel-one-eq-low')?.click();
    }, 100);
    
    window.addEventListener('resize', function() {
      if (visualizer) {
        visualizer.resize(movementBoard.clientWidth, movementBoard.clientHeight);
        const markers = document.querySelectorAll('.filter-marker');
        markers.forEach((marker, index) => {
          if (visualizer.bands[index]) {
            const freq = visualizer.bands[index].freq;
            const minLog = Math.log10(20);
            const maxLog = Math.log10(20000);
            const normalizedX = (Math.log10(freq) - minLog) / (maxLog - minLog);
            marker.style.left = (normalizedX * movementBoard.clientWidth - 10) + 'px';
          }
        });
        // Update low-cut marker position
        const lowcutMarker = document.querySelector('.lowcut-marker');
        if (lowcutMarker && visualizer.lowCut) {
          const normalizedX = (Math.log10(visualizer.lowCut.freq) - Math.log10(20)) / (Math.log10(20000) - Math.log10(20));
          lowcutMarker.style.left = (normalizedX * movementBoard.clientWidth) + 'px';
          const lowCutRegion = document.querySelector('.low-cut-region');
          if (lowCutRegion && visualizer.lowCut.enabled) {
            lowCutRegion.style.width = `${parseInt(lowcutMarker.style.left)}px`;
          }
        }
      }
    });
    
    const rtaButton = document.getElementById('channel-one-eq-rta');
    const eqButton = document.getElementById('channel-one-eq-eq');
    const locutButton = document.getElementById('channel-one-eq-locut');
    const defaultButtonBgColor = '#000000';
    const activeButtonBgColor = '#FF9500';
    let rtaActive = false;
    let eqActive = true;
    let lowcutActive = false;
    
    if (rtaButton) {
      rtaButton.addEventListener('click', function() {
        rtaActive = !rtaActive;
        eqCanvas.style.backgroundColor = rtaActive ? '#142a4c' : 'transparent';
        rtaButton.style.backgroundColor = rtaActive ? activeButtonBgColor : defaultButtonBgColor;
        rtaButton.style.color = rtaActive ? 'white' : '';
      });
    }
    
    if (eqButton) {
      if (eqActive) {
        eqButton.style.backgroundColor = activeButtonBgColor;
        eqButton.style.color = 'white';
        if (visualizer.setFillColor) {
          visualizer.setFillColor('#ffbf80');
          visualizer.draw();
        }
      }
      
      eqButton.addEventListener('click', function() {
        eqActive = !eqActive;
        if (eqActive) {
          visualizer.setFillColor('#ffbf80');
          eqButton.style.backgroundColor = activeButtonBgColor;
          eqButton.style.color = 'white';
        } else {
          visualizer.setFillColor('#808080');
          eqButton.style.backgroundColor = defaultButtonBgColor;
          eqButton.style.color = '';
        }
        visualizer.draw();
      });
    }

    // Low-cut marker and region
    if (!document.querySelector('.lowcut-marker')) {
      const lowcutMarker = document.createElement('div');
      lowcutMarker.className = 'lowcut-marker';
      lowcutMarker.style.position = 'absolute';
      lowcutMarker.style.top = '0';
      lowcutMarker.style.height = '100%';
      lowcutMarker.style.width = '0';
      lowcutMarker.style.borderLeft = '2px dotted white';
      lowcutMarker.style.zIndex = '4';
      lowcutMarker.style.cursor = 'ew-resize';
      
      // Start at the extreme left (20Hz)
      lowcutMarker.style.left = '0px';
      
      // Initially hidden until Lo CUT is activated
      lowcutMarker.style.display = 'none';
      
      movementBoard.appendChild(lowcutMarker);
      
      const lowCutRegion = document.createElement('div');
      lowCutRegion.className = 'low-cut-region';
      lowCutRegion.style.position = 'absolute';
      lowCutRegion.style.top = '0';
      lowCutRegion.style.left = '0';
      lowCutRegion.style.height = '100%';
      lowCutRegion.style.backgroundColor = 'rgba(255, 85, 0, 0.05)';
      lowCutRegion.style.display = 'none';
      lowCutRegion.style.pointerEvents = 'none';
      lowCutRegion.style.zIndex = '2';
      movementBoard.appendChild(lowCutRegion);
      
      // Dragging functionality for low-cut marker
      lowcutMarker.addEventListener('mousedown', function(e) {
        e.preventDefault();
        const initialX = e.clientX;
        const initialLeft = parseInt(window.getComputedStyle(this).left) || 0;
        const boardWidth = movementBoard.clientWidth;
        
        function moveHandler(moveEvent) {
          const dx = moveEvent.clientX - initialX;
          let newLeft = Math.max(0, Math.min(boardWidth, initialLeft + dx));
          lowcutMarker.style.left = newLeft + 'px';
          const normalizedX = newLeft / boardWidth;
          const minLog = Math.log10(20);
          const maxLog = Math.log10(20000);
          const freq = Math.pow(10, minLog + normalizedX * (maxLog - minLog));
          
          if (visualizer) {
            visualizer.updateLowCut(freq);
            const lowCutRegion = document.querySelector('.low-cut-region');
            if (lowCutRegion && visualizer.lowCut.enabled) {
              lowCutRegion.style.width = `${newLeft}px`;
            }
          }
        }
        
        function upHandler() {
          document.removeEventListener('mousemove', moveHandler);
          document.removeEventListener('mouseup', upHandler);
        }
        
        document.addEventListener('mousemove', moveHandler);
        document.addEventListener('mouseup', upHandler);
      });
    }
    
    // Low-cut button handler
    if (locutButton) {
      locutButton.addEventListener('click', function() {
        lowcutActive = !lowcutActive;
        
        this.classList.toggle('active', lowcutActive);
        this.style.backgroundColor = lowcutActive ? activeButtonBgColor : defaultButtonBgColor;
        this.style.color = lowcutActive ? 'white' : '';
        
        const lowcutMarker = document.querySelector('.lowcut-marker');
        const lowCutRegion = document.querySelector('.low-cut-region');
        
          if (lowcutActive) {
            // When activating Lo CUT for the first time
            if (visualizer) {
              const initialFreq = 0;
              visualizer.updateLowCut(initialFreq);
              visualizer.toggleLowCut(true);
              
              const newPosition = 0; 
              
              if (lowcutMarker) {
                lowcutMarker.style.display = 'block';
                lowcutMarker.style.borderLeft = '1.5px solid orange';
                lowcutMarker.style.zIndex = '5';
                lowcutMarker.style.left = newPosition + 'px';
              }
              
              if (lowCutRegion) {
                lowCutRegion.style.display = 'block';
                lowCutRegion.style.width = `${newPosition}px`;
              }
            }
          }
        else {
          // When deactivating Lo CUT
          if (visualizer) {
            visualizer.toggleLowCut(false);
          }
          
          if (lowcutMarker) {
            lowcutMarker.style.display = 'none';
            lowcutMarker.style.borderLeft = '2px dotted white';
            lowcutMarker.style.zIndex = '4';
          }
          
          if (lowCutRegion) {
            lowCutRegion.style.display = 'none';
          }
        }
        
        // Force a redraw after a brief delay to ensure all updates are applied
        setTimeout(() => {
          visualizer.draw();
        }, 10);
        
        console.log('Lo CUT toggled:', lowcutActive, 
                    'Frequency:', visualizer.lowCut ? visualizer.lowCut.freq : 'N/A',
                    'Button background:', this.style.backgroundColor,
                    'Marker visible:', lowcutMarker?.style.display);
      });
      
      // Initialize button state
      locutButton.classList.toggle('active', lowcutActive);
      locutButton.style.backgroundColor = lowcutActive ? activeButtonBgColor : defaultButtonBgColor;
      locutButton.style.color = lowcutActive ? 'white' : '';
    }
    
    const resetButton = document.getElementById('channel-one-eq-reset');
    if (resetButton) {
      resetButton.addEventListener('click', function() {
        if (visualizer) {
          visualizer.reset();
          
          if (rtaActive) {
            rtaActive = false;
            eqCanvas.style.backgroundColor = 'transparent';
            rtaButton.style.backgroundColor = defaultButtonBgColor;
            rtaButton.style.color = '';
          }
          
          if (!eqActive) {
            eqActive = true;
            eqButton.style.backgroundColor = activeButtonBgColor;
            eqButton.style.color = 'white';
            visualizer.setFillColor('#ffbf80');
          }

          if (lowcutActive) {
            lowcutActive = false;
            locutButton.classList.remove('active');
            locutButton.style.backgroundColor = defaultButtonBgColor;
            locutButton.style.color = '';
            const lowcutMarker = document.querySelector('.lowcut-marker');
            const lowCutRegion = document.querySelector('.low-cut-region');
            if (lowcutMarker) {
              lowcutMarker.style.display = 'none';
              lowcutMarker.style.borderLeft = '2px dotted white';
              lowcutMarker.style.zIndex = '4';
              lowcutMarker.style.left = '0px'; // Reset to extreme left (20Hz)
            }
            if (lowCutRegion) {
              lowCutRegion.style.display = 'none';
            }
            if (visualizer && visualizer.lowCut) {
              visualizer.lowCut.freq = 20; // Reset to 20Hz
            }
          }
          
          setTimeout(() => {
            const markers = document.querySelectorAll('.filter-marker');
            markers.forEach((marker, index) => {
              if (visualizer.bands[index]) {
                const freq = visualizer.bands[index].freq;
                const minLog = Math.log10(20);
                const maxLog = Math.log10(20000);
                const normalizedX = (Math.log10(freq) - minLog) / (maxLog - minLog);
                marker.style.left = (normalizedX * movementBoard.clientWidth - 10) + 'px';
              }
            });
            window.updateKnobsFromBand?.(window.activeBandIndex || 0);
            window.updateModeButton?.();
            window.updateControlVisibility?.();
          }, 0);
        }
      });
    }
    
    const style = document.createElement('style');
    style.textContent = `
      .filter-marker { pointer-events: auto; }
      .marker-label { box-shadow: 0 0 5px rgba(0, 0, 0, 0.5); cursor: pointer; }
      .dotted-line { pointer-events: auto; cursor: pointer; }
      .filter-marker.active-marker .marker-label { box-shadow: 0 0 8px rgba(255, 255, 255, 0.8); transform: scale(1.1); }
      .detail-btn-active { background-color: #FF9500 !important; color: white !important; }
      .wheel-knob.disabled { opacity: 0.5; pointer-events: none; }
      .hidden { display: none; }
    `;
    document.head.appendChild(style);
  }
});
// Create marker overlays for vertical gain adjustment
setTimeout(() => {
  // Make sure visualizer is initialized
  if (!window.eqVisualizer) return;
  
  // Function to create and position marker overlays
  function createMarkerOverlays() {
    // Remove any existing overlays first
    document.querySelectorAll('.marker-overlay').forEach(overlay => overlay.remove());
    
    const visualizer = window.eqVisualizer;
    const response = visualizer.calculateResponse();
    const movementBoard = document.querySelector('.movement-board');
    
    // Create overlays for each enabled band
    visualizer.bands.forEach((band, index) => {
      if (!band.enabled) return;
      
      // Calculate position based on frequency and response
      const x = visualizer.freqToX(band.freq);
      let closestIndex = 0;
      let minDistance = Number.MAX_VALUE;
      
      for (let i = 0; i < visualizer.frequencies.length; i++) {
        const freqX = visualizer.freqToX(visualizer.frequencies[i]);
        const distance = Math.abs(freqX - x);
        if (distance < minDistance) {
          minDistance = distance;
          closestIndex = i;
        }
      }
      
      const y = visualizer.dbToY(response[closestIndex]);
      
      // Create overlay element
      const overlay = document.createElement('div');
      overlay.className = `marker-overlay marker-overlay-${index + 1}`;
      overlay.setAttribute('data-band-index', index);
      overlay.style.position = 'absolute';
      overlay.style.width = '20px';
      overlay.style.height = '20px';
      overlay.style.borderRadius = '50%';
      overlay.style.left = `${x - 10}px`;
      overlay.style.top = `${y - 10}px`;
      overlay.style.cursor = 'ns-resize';
      overlay.style.zIndex = '10';
      
      // Add visual indicator (optional)
      overlay.style.border = '2px solid transparent';
      overlay.style.backgroundColor = 'rgba(255, 149, 0, 0.2)';
      overlay.style.transition = 'background-color 0.2s, transform 0.1s';
      
      // Hover effect
      overlay.addEventListener('mouseenter', function() {
        this.style.backgroundColor = 'rgba(255, 149, 0, 0.4)';
        this.style.transform = 'scale(1.2)';
      });
      
      overlay.addEventListener('mouseleave', function() {
        this.style.backgroundColor = 'rgba(255, 149, 0, 0.2)';
        this.style.transform = 'scale(1)';
      });
      
      // Add vertical drag handler for gain adjustment
      overlay.addEventListener('mousedown', function(e) {
        e.preventDefault();
        e.stopPropagation(); // Prevent triggering other handlers
        
        const bandIndex = parseInt(this.getAttribute('data-band-index'));
        const band = visualizer.bands[bandIndex];
        
        // Skip for bands that don't support gain adjustment
        if (band.type === 'highpass' || band.type === 'lowpass') {
          return;
        }
        
        const initialY = e.clientY;
        const initialGain = band.gain;
        const boardHeight = movementBoard.clientHeight;
        
        function moveHandler(moveEvent) {
          // Vertical movement for gain (up = increase, down = decrease)
          const dy = initialY - moveEvent.clientY;
          
          // Scale movement to gain range
          // Full height movement = -15 to +15 dB (30dB range)
          const gainDelta = (dy / boardHeight) * 30;
          let newGain = Math.max(-15, Math.min(15, initialGain + gainDelta));
          
          // Update band gain
          visualizer.updateBand(bandIndex, { gain: newGain });
          
          // Update the marker overlay position
          updateOverlayPosition(overlay, bandIndex);
          
          // Update gain knob if this is the active band
          if (bandIndex === window.activeBandIndex) {
            const gainKnob = document.getElementById('wheel-ch1-gain-4');
            if (gainKnob) {
              const gainAngle = (newGain + 15) / 30 * 280 - 140;
              gainKnob.style.transform = `rotate(${gainAngle}deg)`;
              const progressPath = gainKnob.closest('.black-circle')?.querySelector('.progress-path');
              if (progressPath) {
                const percentage = (gainAngle + 140) / 280;
                const pathLength = 208.4;
                progressPath.style.strokeDashoffset = pathLength - (percentage * pathLength);
              }
            }
          }
        }
        
        function upHandler() {
          document.removeEventListener('mousemove', moveHandler);
          document.removeEventListener('mouseup', upHandler);
        }
        
        document.addEventListener('mousemove', moveHandler);
        document.addEventListener('mouseup', upHandler);
      });
      
      movementBoard.appendChild(overlay);
    });
    
    // Add touch functionality to all interactive elements after creation
    addTouchFunctionalityToMarkers();
  }
  
  // Function to update a single marker overlay position
  function updateOverlayPosition(overlay, bandIndex) {
    const visualizer = window.eqVisualizer;
    const band = visualizer.bands[bandIndex];
    const response = visualizer.calculateResponse();
    
    const x = visualizer.freqToX(band.freq);
    
    // Find closest response value
    let closestIndex = 0;
    let minDistance = Number.MAX_VALUE;
    for (let i = 0; i < visualizer.frequencies.length; i++) {
      const freqX = visualizer.freqToX(visualizer.frequencies[i]);
      const distance = Math.abs(freqX - x);
      if (distance < minDistance) {
        minDistance = distance;
        closestIndex = i;
      }
    }
    
    const y = visualizer.dbToY(response[closestIndex]);
    overlay.style.left = `${x - 10}px`;
    overlay.style.top = `${y - 10}px`;
  }
  
  // Create initial overlays
  createMarkerOverlays();
  
  // Update overlays when window is resized
  window.addEventListener('resize', function() {
    setTimeout(() => {
      createMarkerOverlays();
      addTouchFunctionalityToMarkers();
    }, 50); // Small delay to ensure visualizer has updated
  });
  
  // Extend the visualizer's draw method to update marker overlays
  const originalDraw = window.eqVisualizer.draw;
  window.eqVisualizer.draw = function() {
    originalDraw.call(this);
    
    // Update all marker overlays after drawing
    setTimeout(() => {
      document.querySelectorAll('.marker-overlay').forEach(overlay => {
        const bandIndex = parseInt(overlay.getAttribute('data-band-index'));
        updateOverlayPosition(overlay, bandIndex);
      });
    }, 0);
  };
  
  // Update marker overlays when bands are updated
  const originalUpdateBand = window.eqVisualizer.updateBand;
  window.eqVisualizer.updateBand = function(index, params) {
    originalUpdateBand.call(this, index, params);
    
    // Update the corresponding marker overlay
    setTimeout(() => {
      const overlay = document.querySelector(`.marker-overlay-${index + 1}`);
      if (overlay) {
        updateOverlayPosition(overlay, index);
      }
    }, 0);
  };
  
  // Extend the reset function to also reset marker overlays
  const originalReset = window.eqVisualizer.reset;
  window.eqVisualizer.reset = function() {
    originalReset.call(this);
    
    // Recreate marker overlays after reset
    setTimeout(createMarkerOverlays, 50);
  };
  
  // Handle filter marker horizontal dragging by also updating overlays
  document.querySelectorAll('.filter-marker').forEach((marker, index) => {
    const originalMouseDownHandler = marker.onmousedown;
    
    marker.addEventListener('mousedown', function(e) {
      // Let the original handler run first
      // Then ensure overlays update after freq changes
      document.addEventListener('mousemove', function updateOverlayOnFreqChange() {
        const overlay = document.querySelector(`.marker-overlay-${index + 1}`);
        if (overlay) {
          updateOverlayPosition(overlay, index);
        }
      });
      
      document.addEventListener('mouseup', function cleanupOverlayUpdates() {
        document.removeEventListener('mousemove', updateOverlayOnFreqChange);
        document.removeEventListener('mouseup', cleanupOverlayUpdates);
      });
    });
  });
  
  // Update overlays when the mode button is clicked (band type changes)
  const modeButton = document.getElementById('channel-one-eq-lshv');
  if (modeButton) {
    const originalClick = modeButton.onclick;
    modeButton.onclick = function(e) {
      if (originalClick) originalClick.call(this, e);
      setTimeout(createMarkerOverlays, 50);
    };
  }
  
  // Also connect to the band selector buttons
  ['channel-one-eq-low', 'channel-one-eq-low2', 'channel-one-eq-lomid', 'channel-one-eq-himid', 'channel-one-eq-high2', 'channel-one-eq-high'].forEach(id => {
    const button = document.getElementById(id);
    if (button) {
      const originalClick = button.onclick;
      button.onclick = function(e) {
        if (originalClick) originalClick.call(this, e);
        setTimeout(createMarkerOverlays, 50);
      };
    }
  });
  
}, 500); // Wait for visualizer to be fully initialized

// Add touch event handlers to filter markers
function addTouchFunctionalityToMarkers() {
  // Get all filter markers
  const filterMarkers = document.querySelectorAll('.filter-marker');
  const movementBoard = document.querySelector('.movement-board');
  
  if (!filterMarkers.length || !movementBoard) return;
  
  filterMarkers.forEach(filterMarker => {
    // Handle horizontal frequency dragging
    filterMarker.addEventListener('touchstart', function(e) {
      e.preventDefault();
      window.isDragging = false;
      const bandIndex = parseInt(this.getAttribute('data-band-index'));
      const initialX = e.touches[0].clientX;
      const initialLeft = parseInt(window.getComputedStyle(this).left) || 0;
      const boardWidth = movementBoard.clientWidth;
      
      function touchMoveHandler(moveEvent) {
        window.isDragging = true;
        const dx = moveEvent.touches[0].clientX - initialX;
        let newLeft = Math.max(0, Math.min(boardWidth - 20, initialLeft + dx));
        filterMarker.style.left = newLeft + 'px';
        const normalizedX = (newLeft + 10) / boardWidth;
        const minLog = Math.log10(20);
        const maxLog = Math.log10(20000);
        const freq = Math.pow(10, minLog + normalizedX * (maxLog - minLog));
        
        if (window.eqVisualizer) {
          window.eqVisualizer.updateBand(bandIndex, { freq });
          if (bandIndex === window.activeBandIndex) {
            const freqKnob = document.getElementById('wheel-ch1-freq');
            if (freqKnob) {
              const normalizedValue = (Math.log10(freq) - minLog) / (maxLog - minLog);
              const freqAngle = normalizedValue * 280 - 140;
              freqKnob.style.transform = `rotate(${freqAngle}deg)`;
              const progressPath = freqKnob.closest('.black-circle')?.querySelector('.progress-path');
              if (progressPath) {
                const percentage = (freqAngle + 140) / 280;
                const pathLength = 208.4;
                progressPath.style.strokeDashoffset = pathLength - (percentage * pathLength);
              }
            }
          }
        }
        
        // Update marker overlay position
        const overlay = document.querySelector(`.marker-overlay-${bandIndex + 1}`);
        if (overlay && window.updateOverlayPosition) {
          window.updateOverlayPosition(overlay, bandIndex);
        }
      }
      
      function touchEndHandler() {
        document.removeEventListener('touchmove', touchMoveHandler);
        document.removeEventListener('touchend', touchEndHandler);
        setTimeout(() => window.isDragging = false, 100);
      }
      
      document.addEventListener('touchmove', touchMoveHandler);
      document.addEventListener('touchend', touchEndHandler);
    });
    
    // Handle clicks on marker label and dotted line
    const markerLabel = filterMarker.querySelector('.marker-label');
    const dottedLine = filterMarker.querySelector('.dotted-line');
    
    if (markerLabel) {
      markerLabel.addEventListener('touchend', function(e) {
        if (!window.isDragging) {
          const bandIndex = parseInt(filterMarker.getAttribute('data-band-index'));
          const bandMap = {
            0: 'channel-one-eq-low',
            1: 'channel-one-eq-low2',
            2: 'channel-one-eq-lomid',
            3: 'channel-one-eq-himid',
            4: 'channel-one-eq-high2',
            5: 'channel-one-eq-high'
          };
          const button = document.getElementById(bandMap[bandIndex]);
          if (button) {
            button.click();
            document.querySelectorAll('.filter-marker').forEach(marker => marker.classList.remove('active-marker'));
            filterMarker.classList.add('active-marker');
          }
        }
      });
    }
    
    if (dottedLine) {
      dottedLine.addEventListener('touchend', function(e) {
        if (!window.isDragging) {
          const bandIndex = parseInt(filterMarker.getAttribute('data-band-index'));
          const bandMap = {
            0: 'channel-one-eq-low',
            1: 'channel-one-eq-low2',
            2: 'channel-one-eq-lomid',
            3: 'channel-one-eq-himid',
            4: 'channel-one-eq-high2',
            5: 'channel-one-eq-high'
          };
          const button = document.getElementById(bandMap[bandIndex]);
          if (button) {
            button.click();
            document.querySelectorAll('.filter-marker').forEach(marker => marker.classList.remove('active-marker'));
            filterMarker.classList.add('active-marker');
          }
        }
      });
    }
  });
  
  // Add touch functionality to marker overlays (for vertical gain adjustment)
  const markerOverlays = document.querySelectorAll('.marker-overlay');
  markerOverlays.forEach(overlay => {
    overlay.addEventListener('touchstart', function(e) {
      e.preventDefault();
      e.stopPropagation();
      
      const bandIndex = parseInt(this.getAttribute('data-band-index'));
      const band = window.eqVisualizer.bands[bandIndex];
      
      // Skip for bands that don't support gain adjustment
      if (band.type === 'highpass' || band.type === 'lowpass') {
        return;
      }
      
      const initialY = e.touches[0].clientY;
      const initialGain = band.gain;
      const boardHeight = movementBoard.clientHeight;
      
      function touchMoveHandler(moveEvent) {
        // Vertical movement for gain (up = increase, down = decrease)
        const dy = initialY - moveEvent.touches[0].clientY;
        
        // Scale movement to gain range
        // Full height movement = -15 to +15 dB (30dB range)
        const gainDelta = (dy / boardHeight) * 30;
        let newGain = Math.max(-15, Math.min(15, initialGain + gainDelta));
        
        // Update band gain
        window.eqVisualizer.updateBand(bandIndex, { gain: newGain });
        
        // Update the marker overlay position
        if (window.updateOverlayPosition) {
          window.updateOverlayPosition(overlay, bandIndex);
        }
        
        // Update gain knob if this is the active band
        if (bandIndex === window.activeBandIndex) {
          const gainKnob = document.getElementById('wheel-ch1-gain-4');
          if (gainKnob) {
            const gainAngle = (newGain + 15) / 30 * 280 - 140;
            gainKnob.style.transform = `rotate(${gainAngle}deg)`;
            const progressPath = gainKnob.closest('.black-circle')?.querySelector('.progress-path');
            if (progressPath) {
              const percentage = (gainAngle + 140) / 280;
              const pathLength = 208.4;
              progressPath.style.strokeDashoffset = pathLength - (percentage * pathLength);
            }
          }
        }
      }
      
      function touchEndHandler() {
        document.removeEventListener('touchmove', touchMoveHandler);
        document.removeEventListener('touchend', touchEndHandler);
      }
      
      document.addEventListener('touchmove', touchMoveHandler);
      document.addEventListener('touchend', touchEndHandler);
    });
  });
  
  // Add touch functionality to low-cut marker
  const lowcutMarker = document.querySelector('.lowcut-marker');
  if (lowcutMarker) {
    lowcutMarker.addEventListener('touchstart', function(e) {
      e.preventDefault();
      const initialX = e.touches[0].clientX;
      const initialLeft = parseInt(window.getComputedStyle(this).left) || 0;
      const boardWidth = movementBoard.clientWidth;
      
      function touchMoveHandler(moveEvent) {
        const dx = moveEvent.touches[0].clientX - initialX;
        let newLeft = Math.max(0, Math.min(boardWidth, initialLeft + dx));
        lowcutMarker.style.left = newLeft + 'px';
        const normalizedX = newLeft / boardWidth;
        const minLog = Math.log10(20);
        const maxLog = Math.log10(20000);
        const freq = Math.pow(10, minLog + normalizedX * (maxLog - minLog));
        
        if (window.eqVisualizer) {
          window.eqVisualizer.updateLowCut(freq);
          const lowCutRegion = document.querySelector('.low-cut-region');
          if (lowCutRegion && window.eqVisualizer.lowCut.enabled) {
            lowCutRegion.style.width = `${newLeft}px`;
          }
        }
      }
      
      function touchEndHandler() {
        document.removeEventListener('touchmove', touchMoveHandler);
        document.removeEventListener('touchend', touchEndHandler);
      }
      
      document.addEventListener('touchmove', touchMoveHandler);
      document.addEventListener('touchend', touchEndHandler);
    });
  }
}

// Make updateOverlayPosition accessible globally
window.updateOverlayPosition = function(overlay, bandIndex) {
  const visualizer = window.eqVisualizer;
  const band = visualizer.bands[bandIndex];
  const response = visualizer.calculateResponse();
  
  const x = visualizer.freqToX(band.freq);
  
  // Find closest response value
  let closestIndex = 0;
  let minDistance = Number.MAX_VALUE;
  for (let i = 0; i < visualizer.frequencies.length; i++) {
    const freqX = visualizer.freqToX(visualizer.frequencies[i]);
    const distance = Math.abs(freqX - x);
    if (distance < minDistance) {
      minDistance = distance;
      closestIndex = i;
    }
  }
  
  const y = visualizer.dbToY(response[closestIndex]);
  overlay.style.left = `${x - 10}px`;
  overlay.style.top = `${y - 10}px`;
};