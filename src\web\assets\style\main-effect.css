/* fx6 addons */

.fx6_inner_knobs .black-circle.lo-freq-knob::before,
.fx6_inner_knobs .black-circle.hifreq-knob::before {
display: none;
}
.fx6_inner_knobs .black-circle.lo-freq-knob::after,
.fx6_inner_knobs .black-circle.hifreq-knob::after {
    display: none;
}
/* fx6 end */
.knob-nod-wrap {
position: relative;
width: 57px;
    height: 57px;
  border-radius: 100px;
  background-color: #000;
    transform: rotate(-180deg);
}
.knob-nod {
    position: absolute;
        top: 30%;
        left: 65%;
        transform: rotate(90deg);
        pointer-events: none;
            /* Make knob-nod non-interactive */
            z-index: 5;
            /* Ensure the knob-nod is below the wheel-knob */
}
.black-circle.stand-below-circle::after {
    content: ' ';
    position: absolute;
    bottom: -5px;
    width: 29px;
    height: 10px;
    background: #1E1E1E;
}
.wheel-box .black-circle.lo-freq-knob,
.wheel-box .black-circle.attsel-knob {
    width: 80px;
    height: 80px;
} 
#wheel-ch1-26 {
        width: 50px !important;
            height: 50px!important;
}

.below_row_fx8 .black-circle::before {
    content: '-';
    color: #f7f7f7;
    z-index: 2;
        position: absolute;
    bottom: -5px;
    left: -5px;
}
.below_row_fx8 .black-circle::after {
    content: '+';
    color: #f7f7f7;
    z-index: 2;
        position: absolute;
        bottom: -5px;
        right: -5px;
}
.fx5-inner-1-content .single_stereo .black-circle::before {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    z-index: 2;
    position: absolute;
    bottom: 0;
    left: 0;
}
.fx5-inner-1-content .single_stereo .black-circle::after {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    z-index: 2;
    position: absolute;
    bottom: 0;
    right: 0;
}
/* POPUPS FOR ADDITIONAL SCREENS  */   
   .custom-popup {
       position: fixed;
       bottom: 0;
       left: 50%;
       transform: translateX(-50%);
       width: 97%;
       height: 50%;
       background-color: #121212;
       border-top-left-radius: 10px;
       border-top-right-radius: 10px;
       box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
       display: none;
       z-index: 999;
   }

   .custom-popup-content {
       height: 100%;
        overflow: hidden;
   }

   .swiper {
       width: 100%;
       height: calc(100% - 30px);
       /* Adjusted for bullets */
   }

   .swiper-slide {
       display: flex !important;
       flex-direction: column !important;
       justify-content: space-around !important;
       padding: 20px;
       box-sizing: border-box;
    height: 100%;
        min-height: 400px;
   }

   .swiper-slide .row {
       display: flex;
       justify-content: space-between;
   }
.txt-screen-title {
    text-align: left;
    width: 100%;
    max-width: 305px;
    margin: 15px 0 0 0;
    color: #f7f7f7;
}
   .swiper-slide .row > div {
       width: 23%;
       height: 100px;
       background-color: transparent;
       display: flex;
       flex-direction: column;
       align-items: center;
       justify-content: flex-start;
       cursor: pointer;
   }

   .swiper-slide .row img {
       width: 100%;
       max-width: 305px;
       height: 100px;
       display: flex;
       align-items: center;
       justify-content: center;
       cursor: pointer;
   }
.swiper-pagination {
        top: 40% !important;
        width: auto!important;
            height: auto !important;
            position: absolute !important;
            left: 50% !important;
            transform: translate(-50%, 0%) !important;
}
   .swiper-pagination-bullet {
       background: #7C7C7C !important;
       opacity: 1;
   }

   .swiper-pagination-bullet-active {
       background: #f7f7f7 !important;
   }

   /* additional screens */
   .inactive_effect-screen {
    display: none;
   }
   .inactive_effect-screen.active_effect-screen,
   .active_effect-screen {
       display: block !important;
   }
   .fx1_add-screen-1-present .fx1-inner-1-content .heading-board-effect,
   .fx_heading_ce181e .heading-board-effect {
    color: #ce181e !important;
   }
.fx1_add-screen-2-present .fx1-inner-1-content .heading-board-effect,
.fx2_add-screen-2-present .heading-board-effect,
.fx_heading_b9ffff .heading-board-effect {
    color: #b9ffff !important;
}

 .fx2_add-screen-3-present .fx1-inner-1-content .heading-board-effect,
 .fx_heading_72bf44 .heading-board-effect {
    color: #72bf44 !important;
                }
.fx_heading_F2A40A .heading-board-effect {
color: #F2A40A !important;
}
 
.fx_heading_FAD705 .heading-board-effect {
    color: #FAD705 !important;
}
.fx_heading_ce181e .effect-inner-row.main-effect-board,
.fx_heading_b9ffff .effect-inner-row.main-effect-board,
.fx_heading_72bf44 .effect-inner-row.main-effect-board,
.fx_heading_F2A40A .effect-inner-row.main-effect-board,
.fx_heading_FAD705 .effect-inner-row.main-effect-board
 {
background: url("/assets/icons-effects/bg-main-content.jpg") !important;
    background-size: cover;
    background-repeat: no-repeat;
    background-position: center;
}
.darken_bg .effect-inner-row.main-effect-board {
    background: #3f3f3f;
}
/* DOUBLE ROW TEMP */
.effects_double_down.wheel-item {
width: 100%;
}
.effects_double_down .wheel-board {
    height: 105px;
}
.effects_double_down.wheel-item .wheel-knob {
    width: 39px;
    height: 39px;
}
.effects_double_down.wheel-item .black-circle {
        top: 50%;
    width: 63px;
    height: 63px;
}
.effects_double_down.wheel-item .black-circle::after {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    z-index: 2;
    position: absolute;
    bottom: 2px;
        right: 10px;
}
.effects_double_down.wheel-item .black-circle::before {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    z-index: 2;
    position: absolute;
    bottom: 2px;
    left: 10px;
}
.dot-left {
    font-size: 14px;
    font-weight: 500;
        position: absolute;
        bottom: 0;
        left: 20px;
        color: #fff;
}
.dot-right {
        font-size: 14px;
            position: absolute;
            bottom: 0;
            right: 20px;
            color: #fff;
            font-weight: 500;
}
.bottom_control,
.top_control {
    color: #fff;
    font-size: 12px;
    font-weight: 600;
}
.bottom_control {
        position: absolute;
            bottom: 0;
            left: 50%;
            transform: translate(-50%, 8px);
}
.top-row_flex,
.bottom-row_flex {
    display: flex;
        padding-left: 20px;
        align-items: center;
        gap: 0;
}
.screen_text {
    min-width: 217px;
    width: 100%;
    height: 79px;
    display: flex;
    align-items: center;
    justify-content: center;
        font-family: "Digital-1", sans-serif !important;
        font-size: 32px;
        background-color: #000000;
}
.screen_text.heading_ce181e {
color: #ce181e;
}
.screen_text.heading_75C044 {
color: #75C044;
}
.screen_note {
    font-size: 12px;
    color: #fff;
    margin-bottom: 5px;
}
.screen_name_wrapper {
    margin: 0 30px 20px;
}
.top-row_flex .black-circle,
.bottom-row_flex .black-circle {
width: 84px !important;
height: 84px !important;
}
.top-row_flex .wheel-knob,
.bottom-row_flex .wheel-knob {
width: 54px !important;
    height: 54px !important;
}
.double-row_temp {
    display: flex;
        flex-direction: column;
        align-items: flex-start;
        justify-content: center;
        gap: 20px;
            width: 100%;
}
.bottom-row_flex > .effects_double_down,
.top-row_flex>.effects_double_down {
width: 11.1%;
}
.top-row_flex,
.bottom-row_flex {
    width: 100%;
}
.darken_bg .effect-inner-row.main-effect-board {
padding-left: 0;
padding-right: 0;
}
.top-row_flex {
        border-bottom: 1px solid #6D6C6B;
}
.empty-btn {
    width: 78px;
    height: auto;
    margin: 0 8px;
}
/* DOUBLE ROW WITH TITEL */
.title_wrap {
    font-size: 32px;
    font-weight: 700;
    color: #fff;
}
.double-row_temp_with_title {
    display: flex;
    align-items: center;
    gap: 50px;
    padding-left: 50px;
}
.double-row_temp_with_title .top-row_flex {
border: none !important;
}
.knobs_wrap,
.double-row_temp_with_title {
    width: 100%;
}
.double-row_temp_with_title .bottom-row_flex>.effects_double_down,
.double-row_temp_with_title .top-row_flex>.effects_double_down {
    width: 15.1%;
}

/* with image and title */
.double-row_temp_with_title_image {
    display: flex;
    align-items: center;
        gap: 10px;
        padding-left: 20px;
        width: 100%;
}
.double-row_temp_with_title_image .top-row_flex {
border: none !important;
}
.double-row_temp_with_title_image .top-row_flex>.effects_double_down,
.double-row_temp_with_title_image .bottom-row_flex>.effects_double_down {
width: 24%;
}
.double-row_temp_with_title_image .wrap_50 .bottom-row_flex>.effects_double_down,
.double-row_temp_with_title_image .wrap_50 .top-row_flex>.effects_double_down  {
width: 48%;

}
.double-row_temp_with_title_image .knobs_wrap {
    width: 55%;
 
}
.double-row_temp_with_title_image .knobs_wrap.wrap_50{
width: 30%;
}
.double-row_temp_with_title_image  .bottom-row_flex,
.double-row_temp_with_title_image  .top-row_flex {
padding-left: 0 !important;
}
.double-row_temp_with_title_image .top-row_flex .wheel-knob,
.double-row_temp_with_title_image .bottom-row_flex .wheel-knob {
    width: 42px !important;
    height: 42px !important;
} 
.double-row_temp_with_title_image .top-row_flex .black-circle,
.double-row_temp_with_title_image .bottom-row_flex .black-circle {
    width: 68px !important;
    height: 68px !important;
}
.double-row_temp_with_title_image .effects_double_down.wheel-item .black-circle::after {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    z-index: 2;
    position: absolute;
    bottom: 0px;
    right: 5px;
}
.double-row_temp_with_title_image .effects_double_down.wheel-item .black-circle::before {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
        z-index: 2;
        position: absolute;
        bottom: 0px;
            left: 5px;
}
.board_img {
    max-width: 275px;
}

.double-row_temp_with_title_image .bottom-row_flex .top_control {
margin-top: 20px;
}
/* longer screen tab 3 */
.letter_off .dot-left {
    bottom: -6px;
        left: 14px;
} 
.letter_off .dot-right {
        bottom: -6px;
            right: 14px;
}
.screen_text.longer_screen {
width: 355px;
}

/* sub octaver */
.double-row_temp_with_title_image.sub_octaver .knobs_wrap {
    width: 25% !important;
}
.double-row_temp_with_title_image.sub_octaver .top-row_flex>.effects_double_down,
.double-row_temp_with_title_image.sub_octaver .bottom-row_flex>.effects_double_down {
width: 30% !important;
}
.double-row_temp_with_title_image.sub_octaver .title_wrap {
width: 22% !important;
}
.double-row_temp_with_title_image.sub_octaver .switcher_wrap {
    width: 12% !important;
}
.double-row_temp_with_title_image.sub_octaver .knobs_wrap.wrap_50 .top-row_flex,
 .double-row_temp_with_title_image.sub_octaver .knobs_wrap.wrap_50 .bottom-row_flex {
justify-content: space-around;
}
.double-row_temp_with_title_image.sub_octaver .title_wrap {
    padding-left: 40px;
}
.top_control.letter_control {
font-size: 32px;
font-weight: 700;
}
.double-row_temp_with_title_image.sub_octaver .octa_bottom .effects_double_down.wheel-item {
    width: 48% !important;
}
.double-row_temp_with_title_image.sub_octaver .top-row_flex .wheel-knob,
.double-row_temp_with_title_image.sub_octaver .bottom-row_flex .wheel-knob {
width: 53px !important;
    height: 53px !important;
}
.double-row_temp_with_title_image.sub_octaver .top-row_flex .black-circle,
.double-row_temp_with_title_image.sub_octaver .bottom-row_flex .black-circle {
    width: 85px !important;
    height: 85px !important;
}

.double-row_temp_with_title_image.sub_octaver .dot-left {
    bottom: -10px;
        left: 10px;
}

.double-row_temp_with_title_image.sub_octaver .dot-right {
    bottom: -10px;
        right: 10px;
}

.top_switcher,
.bottom_switcher {
    display: flex;
    justify-content: center;;
    height: 69px;
    gap: 10px;
}
.switcher_wrap {
    display: flex;
   flex-direction: column;
   gap: 30px;
    justify-content: space-between;
        height: 100%;
        max-height: 260px;
}
.points_button {
    background-color: #1e1e1e;
}
.points_text {
    color: #f7f7f7;
    font-weight: 500;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
}
.points_button  {
    overflow: hidden;
}
.points_button img {
cursor: pointer;
}
.points_button {
    position: relative;
    /* Container remains relatively positioned */
    overflow: hidden;
    /* Prevents the image from moving outside the container */
    height: 69px;
        width: 43px;
    /* Ensure the container has a fixed height */
}
.switcher_track_img {
    z-index: 3;
    position: absolute;
    /* Allows the image to move within the container */
    cursor: grab;
    /* Indicates draggable element */
    top: 0;
    left: 0;
}
.switcher_track_img:active {
    cursor: grabbing;
}
.sub_octaver  .title_wrap{
text-align: center;
}
.sub_octaver .title_wrap > div {
    display: inline-block;
    padding-top: 20px;
    padding-bottom: 20px;
    border-top: 1px solid #D1221D;
    border-bottom: 1px solid #D1221D;
}

/* stero GEQ and EQ */
.double-row_temp_with_title.stero_eq_and_duals {
    display: flex;
        flex-direction: column;
        gap: 10px;
        padding-left: 20px;
}
.dragger-effect-board.bottom_eq_drag_wrap {
        height: 245px;
            display: flex;
            align-items: flex-start;
    gap: 7px;
}
.top_title_clear {
    display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 0;
        width: 90%;
      
}
.reverse.reset_eq {
    width: 53px;
        height: 53px;
        border: 3px solid #f7f7f7;
        border-radius: 4px;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
}
.double-row_temp_with_title.stero_eq_and_duals .effect-board-route {
    height: 175px;
}

.double-row_temp_with_title.stero_eq_and_duals .effects-dragger {
    left: -4px;
    top: 78px;
}
.bott_title_lean {
        color: #f7f7f7;
            font-size: 14px;
            transform: rotate(-90deg);
                margin-top: 10px;
                    margin-left: 0px;
                    writing-mode: vertical-rl;
                        /* Vertical text, top to bottom */
                        
                        /* Keeps letters upright */
                        transform: rotate(-180deg);
}
.double-row_temp_with_title.stero_eq_and_duals .filled-column {
display: flex;
    gap: 3px;
}
.points_drag_stops {
    display: flex;
        flex-direction: column;
        justify-content: space-between;
        max-height: 170px;
            align-items: center;
            padding-top: 6px;
}
.points_drag_stops div {
width: 7px;
    height: 3px;
    background: #f7f7f7;
 
}
/* different title screens like 60 */
.double-row_temp_with_title.triple_col_wrap {
gap: 20px;
}
.double-row_temp_with_title.triple_col_wrap .top-row_flex,
.double-row_temp_with_title.triple_col_wrap .bottom-row_flex{
    justify-content: center;
}
.double-row_temp_with_title.triple_col_wrap .knobs_wrap {
width: 65%;
}
.double-row_temp_with_title.triple_col_wrap .knob_with_title,
.double-row_temp_with_title.triple_col_wrap .bottom_knob_wrap {
width: 16%;
}
.double-row_temp_with_title.triple_col_wrap .knob_with_title {
    display: flex;
        flex-direction: column;
        align-items: stretch;
        justify-content: space-between;
        height: 304px;
}
.double-row_temp_with_title.triple_col_wrap .knob_with_title .bottom-row_flex {
justify-content: flex-end;
}
.double-row_temp_with_title.triple_col_wrap .bottom_knob_wrap .bottom-row_flex {
    justify-content: flex-start;
}
.double-row_temp_with_title.triple_col_wrap .knob_with_title .effects_double_down.wheel-item,
.double-row_temp_with_title.triple_col_wrap .bottom_knob_wrap .effects_double_down.wheel-item {
width: 134px !important;
}
.double-row_temp_with_title.triple_col_wrap .bottom_knob_wrap {
    display: flex;
        flex-direction: column;
        justify-content: space-between;
        margin: auto;
        height: 304px;
}
.double-row_temp_with_title.triple_col_wrap .bottom-row_flex>.effects_double_down,
.double-row_temp_with_title.triple_col_wrap .top-row_flex>.effects_double_down {
    width: 19.1%;
}
.double-row_temp_with_title.triple_col_wrap .top-row_flex,
.double-row_temp_with_title.triple_col_wrap .bottom-row_flex {
padding-left: 0px;
}    
.double-row_temp_with_title.triple_col_wrap .knob_with_title .bottom-row_flex>.effects_double_down,
.double-row_temp_with_title.triple_col_wrap .bottom_knob_wrap .bottom-row_flex>.effects_double_down {
    width: 100%;
}

/* screens 58 59 */
.double-row_temp_with_title.title_out_wrap .top-row_flex,
 .double-row_temp_with_title.title_out_wrap .bottom-row_flex {
width: 80%;
    justify-content: flex-end;
    margin: auto;

}
.double-row_temp_with_title.title_out_wrap .title_wrap {
    position: absolute;
        z-index: 3;
        bottom: 75px;
        left: 12%;
}

/* one row setup */ 
.effect-inner-row.main-effect-board.one_row_setup {
        height: 240px;
            margin-bottom: 100px;
            margin-top: 50px;
}
.effect-inner-row.main-effect-board.one_row_setup .title_wrap {
    font-size: 20px;
    position: absolute;
    z-index: 5;
    bottom: 20px;
    right: 120px;
} 
.effect-inner-row.main-effect-board.one_row_setup .top-row_flex {
padding-left: 0 !important;
    width: 100%;
}

.one_row_setup .double-row_temp_with_title .top-row_flex>.effects_double_down {
    width: 12.1%;
}
.one_row_setup .double-row_temp_with_title {
gap: 0 !important;
}
.one_row_setup .knobs_wrap {
width: 82%;
}
.one_row_single_col {
        display: flex;
            align-items: center;
            gap: 10px;
}
.red_btn_text {
    color: #fff;
    font-weight: 500;
}
.left_wall {
    background: #252525;
        position: absolute;
        top: 0;
        left: 0;
        width: 55px;
        height: 100%;
}
.right_wall {
    background: #252525;
        position: absolute;
        top: 0;
        right: 0;
        width: 55px;
        height: 100%;
}
.one_row_setup .screw-bottom-left {
    position: absolute;
    bottom: 10px;
    left: 15px;
    z-index: 1;
} 
.one_row_setup .screw-bottom-right {
    position: absolute;
    bottom: 10px;
    right: 15px;
    z-index: 1;
}
.one_row_setup .screw-top-left {
    position: absolute;
    top: 10px;
    left: 15px;
    z-index: 1;
}
.one_row_setup .screw-top-right {
    position: absolute;
    top: 10px;
    right: 15px;
    z-index: 1;
}
  .red-btn-in {
background: #464646 !important;
  }    


.red-btn-in.red_active {
        /* box-shadow: 0px 0px 4px 5px #f23737; */
        background: #ce181e !important;
}

/* screen 55 */
.one_row_setup.ab_double .left_wall {
width: 169px;
}
.effect-inner-row.main-effect-board.one_row_setup.ab_double .title_wrap {
    font-size: 20px;
    position: absolute;
    z-index: 5;
    top: 36%;
    left: 20px;
    right: unset !important;
    bottom: unset !important;
}
.one_row_setup.ab_double .double-row_temp_with_title {
padding-left: 190px;
}
.one_row_setup.ab_double .one_row_single_col {
display: flex;
flex-direction: column;
height: 140px;
justify-content: space-between;
}
.one_row_setup.ab_double .red_btn_text {
    font-size: 26px;
}
.one_row_setup.ab_double .knobs_wrap {
width: 40%;
}
.one_row_setup.ab_double .one_row_single_col {
width: 10%;
}
.one_row_setup.ab_double .double-row_temp_with_title .top-row_flex>.effects_double_down {
        width: 27.1%;
}
.double-row_temp_with_title.tab-inner-content-hidden {
    display: none;
}
.double-row_temp_with_title.tab-inner-content-hidden.active-detail-tab {
        display: flex;
}
/* 53 screen */
.knobs_wrap.forth_div {
width: 50% !important;
}
.one_row_setup.ab_double .double-row_temp_with_title .knobs_wrap.forth_div .top-row_flex>.effects_double_down {
    width: 25.1%;
}

.title_down .top_control {
    margin-top: 20px;
}

/* solo modes */
.solo_modes .one_row_single_col {
flex-direction: column;
gap: 10px;
height: 160px;
align-items: center;
justify-content: flex-end;
}
.solo_modes .red_btn_text:first-of-type {
    margin-bottom: auto;
    font-size: 24px;
}
.solo_modes .red_btn_text.sm_txt {
font-size: 14px;
}
.solo_modes .effects_double_down.wheel-item {
    margin-bottom: 0;
}
.solo_modes .knobs_wrap {
margin-left: 30px;
}

.solo_modes .red-btn-out {
background-color: transparent;
border: none;
}
.solo_modes .red-btn-in {
background-color: #2D2D2F !important;
box-shadow: none !important;
border: 3px solid #f7f7f7;
}
.solo_modes .red-btn-in.red_active {
    background: #ce181e !important;
}
.solo_modes .double-row_temp_with_title {
padding-left: 80px;
}
.solo_modes .double-row_temp_with_title .title_wrap {
    bottom:120px !important;
        right: 97% !important;

}

/* animations */
.bond_extend {
    position: relative;
        width: 10px;
        height: 180px;
        margin: auto;
}
.bottom_eq_drag_wrap .relative-effect-board {
position: unset;
}
.double-row_temp_with_title.stero_eq_and_duals .effects-dragger {
        top: -96px;
            left: -9px;
}
.double-row_temp_with_title.stero_eq_and_duals .effect-board-route {

        margin-top: -3px;
}

.stero_eq_and_duals .empty_null {
    color: #fff;
    font-size: 36px;
    font-weight: bold;
}

/* dimension mode */
.main-mode-wrap {
        width: 80%;
            display: flex;
                height: 154px;
}
.main_modes {
    width: 80%;
    display: flex;
}
.power_mode {
    width: 20%;
}
.stereo_dry_mode {
    width: 30%;
        display: flex;
            justify-content: center;
            gap: 30px;
            color: #fff;
            font-weight: bold;
}
.dimension_swap .effect-inner-row.main-effect-board {
height: 300px;
}
.dimension_swap .title_wrap {
margin-bottom: auto;
margin-top: 70px;
}
.off_4_mode {
width: 50%;
    display: flex;
    justify-content: flex-start;
    align-items: center;
        gap: 15px;
    color: #fff;
    font-weight: bold;
    flex-direction: column;
}
.modes_options {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 3px;
}
.power_mode {
    width: 20%;
    display: flex;
    justify-content: center;
    align-items: self-end;
    color: #fff;
    font-weight: bold;
}
.stereo_mode {
        display: flex;
            flex-direction: column;
            justify-content: flex-start;
            align-items: center;
            gap: 15px;
}
.stereo_btn,
.dry_btn,
.mode_btn,
.power_btn {
        border-radius: 4px;
            border: 3px solid #f5f5f5;
            background: #2d2d2f;
            color: #fff;
            text-transform: uppercase;
            font-size: 14px;
            height: 40px;
            width: 54px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
}

.active-btn-background {
    background: #f5f5f5 !important;
    color: #222;
    font-weight: 600;
}
.active-btn-shadow {
    box-shadow: 0 0 9.2px 1px #f5f5f5 !important;
}
.red_bulb {
background: #CE181E;
    width: 20px;
    height: 20px;
    border-radius: 50px;
    border: 3px solid #171717;
}
.modes_wrapper {
    text-align: center;
}
.mode_txt {
    margin-top: 10px;
    font-size: 14px;
}

/* 18 mood screen */
.double-row_temp_with_title.mood_switcher .knobs_wrap{
width: 80%;
}
.mood_switcher_wrap .toggle-box .toggle-modes {
    width: 74px;
    height: 38px;
}
.mood_switcher_wrap .toggle-box .dark-mode .circle {
    transform: translateX(0%);
}
.mood_switcher_wrap .toggle-box .circle {
    transform: translateX(118%);
}
.mood_switcher_wrap > div {
        display: flex;
}
.mood_switcher_wrap {
    color: #f7f7f7;
}
.mood_up_24 {
    text-align: center;
        padding-top: 15px;
}
.mood_switcher_wrap .text-detail {
    margin-bottom: 3px;
}
.mood_switcher_wrap .toggle-box .text-detail {
font-size: 14px;
margin-top: 3px;
}
.mood_switcher_wrap .red_bulb {
    background: #CE181E;
    width: 16px;
    height: 16px;
}


/* tap screens 12-13 */
.tap_wrappers .title_wrap {
width: 11%;
    display: flex;
        flex-direction: column;
        height: 300px;
        justify-content: space-around;
        padding-left: 30px;
}
.tap_wrappers .knobs_wrap {
width: 82%;
}
.double-row_temp_with_title_image.tap_wrappers .top-row_flex>.effects_double_down {
    width: 21%;
}
.double-row_temp_with_title_image.tap_wrappers .bottom-row_flex>.effects_double_down {
    width: 12%;
}
.two_btns_flex_tap {
    color: #f7f7f7;
        font-weight: 500;
        display: flex;
        flex-direction: column;
        gap: 40px;
}
.dry_btn_wrap {
        display: flex;
            align-items: center;
            justify-content: center;
            gap: 20px;
}
.btn_dry_tap {
    width: 33px;
        height: 33px;
        border: 3px solid #f7f7f7;
        border-radius: 4px;
        background: #2d2d2f;
        cursor: pointer;
}
.dry_btn_wrap > div {
    font-size: 16px;
}

.tap_wrappers .top-row_flex .top_control,
.tap_wrappers .bottom-row_flex .top_control {
    margin-top: 10px;
}
.tap_wrappers.tap_four .top-row_flex>.effects_double_down,
.tap_wrappers.tap_four .bottom-row_flex>.effects_double_down {
    width: 16.5%;
}
.tap_wrappers.tap_four .two_btns_flex_tap {
gap: 10px;
justify-content: flex-start;
align-items: flex-start;
}
.tap_wrappers.tap_four .title_wrap {
    gap: 20px;
}
.tap_wrappers.tap_four .two_btns_flex_tap,
.tap_wrappers.tap_four .tap_bottom_title {
padding-left: 50px;
}

.tap_wrappers.tap_four .title_wrap {
    width: 18%; 
}
.tap_wrappers.tap_four .knobs_wrap {
    width: 75%;
}
.fx1_add-screen-13-present .effect-inner-row.main-effect-board {
height: 390px !important;
}
.tap_wrappers.tap_four .title_wrap {
    height: 330px;
}
.btn_dry_tap.circle_btn {
width: 29px;
height: 29px;
border-radius: 50px;
border: 4px solid #171717;
}
.btn_dry_tap.circle_btn.active-btn-background-tap {
    box-shadow: none !important;
    background: #72BF44 !important;
}
/* enhancer stereo */
.enhancer_stereo .double-row_temp_with_title {
flex-direction: column;
align-items: flex-start;
    justify-content: center;
}
.solo_modes.enhancer_stereo .double-row_temp_with_title {
padding-left: 60px;
}
.fx1_49_label {
    color: #f7f7f7;
        font-size: 24px;
        font-weight: 500;
        position: absolute;
        left: 70px;
        top: 81px;
        z-index: 4;
}
.solo_modes.enhancer_stereo .fx5-title {
    padding-left: 30px;
}
 .effect-inner-row.main-effect-board.one_row_setup.enhancer_stereo {
    height: 288px;
    gap: 50px !important;
}
.effect-inner-row.main-effect-board.one_row_setup.enhancer_stereo .double-row_temp_with_title{
    height: 288px;
    gap: 14px !important;
        padding-bottom: 20px;
}

/* vse electronic screens */
.effect-inner-row.main-effect-board.vse_electronic {
height: 394px;
}
.effect-inner-row.main-effect-board.vse_electronic .vse_electronic_inner {
    width: 95%;
    margin: auto;
    padding: 0 20px 0 55px;
}
.vse_electronic_inner .sm-title-box {
text-align: right;
}
.vse_electronic_inner .boxed.title-box {
padding: 12px;
}
.vse_electronic .left_wall {
    background: #252525;
    position: absolute;
    top: 0;
    left: 0;
    width: 77px;
    height: 100%;
}
.vse_electronic .right_wall {
    background: #252525;
    position: absolute;
    top: 0;
    right: 0;
    width: 77px;
    height: 100%;
}
.vse_electronic .screw-top-left {
    position: absolute;
    top: 10px;
    left: 25px;
    z-index: 1;
}
.vse_electronic .screw-top-right {
    position: absolute;
    top: 10px;
    right: 25px;
    z-index: 1;
}
.vse_electronic .screw-bottom-right {
    position: absolute;
    bottom: 10px;
    right: 25px;
    z-index: 1;
}
.vse_electronic .screw-bottom-left {
    position: absolute;
    bottom: 10px;
    left: 25px;
    z-index: 1;
}
/* enhancer screen 54 */
.enhancer-x1 .dot-left,
.enhancer-x1 .dot-right
/* .effects_double_down.wheel-item .black-circle::before,
.effects_double_down.wheel-item .black-circle::after  */
{
display: none !important;
}
.effect-inner-row.main-effect-board.one_row_setup.ab_double .title_wrap {
    font-size: 20px;
    position: absolute;
    z-index: 5;
    top: 44%;
    left: 20px;
    right: unset !important;
    bottom: unset !important;
}
.single_preset_item.float_bottom_right {
position: absolute;
z-index: 5;
bottom: 40px;
right: 10%;
}
 .enhancer-x1 .single_preset_item {
color: #f7f7f7;
text-align: center;
display: flex;
flex-direction: column;
justify-content: center;
align-items: center;
gap: 4px;
font-size: 14px;
}
 .enhancer-x1 .btn-block{
width: 36px;
height: 35px;
}
.one_row_setup.ab_double.enhancer-x1 .knobs_wrap {
width: 82%;;
}
.title_wrap_ex {
    font-size: 24px;
    font-weight: 600;
    color: #fff;
}
.enhancer-x1 .top-row_flex {
    justify-content: center;
    gap: 50px;
}
.enhancer-x1 .knobs_wrap {
        display: flex;
            flex-direction: column;
            gap: 20px;
}
.enhancer-x1 .volumer_board {
    display: flex;
        align-items: center;
        border-radius: 14px;
            margin-bottom: 0;
        background: linear-gradient(to left, #D1221D 0%,
                    /* Third color occupies 37% */
                    #D1221D 37%, #FAD705 37%,
                    /* Second color occupies 30% */
                    #FAD705 63%, #75C044 63%,
                    /* First color occupies 35% */
                    #75C044 100%);
            position: relative;
            overflow: hidden;
            z-index: 0;
}
.enhancer-x1 .volumer_board .overlay {
        position: absolute;
            border-radius: 0;
            top: 0;
            right: 0;
            left: unset;
}
/* vse compress screens 42-44 */

.vse_electronic.vse_compress .vse_electronic_inner {
    justify-content: flex-start;
/* display: flex; */
    width: 100% !important;
    padding: 0 40px;
}  

.vse_electronic.vse_compress .vse-electronic-col {
    display: flex;
    width: 35% !important;
    flex-wrap: nowrap;
    flex-direction: column;
    justify-content: space-around;
}
.vse_electronic.vse_compress .vse-limit-col {
    width: 65%;
    padding-top: 20px;
}
.effect-inner-row.main-effect-board.vse_electronic.vse_compress .vse_electronic_inner {
margin: 0 !important;
}
.vse_electronic.vse_compress .vse-limit-col .boxed {
    width: 30%;
}
.vse_electronic.vse_compress .vse-limit-col .double_wheel_fx8 .boxed {
    width: 50% !important;
}
.vse_electronic.vse_compress .double_wheel_fx8 .wheel-box .black-circle {
    width: 77px;
    height: 77px;
}
.vse_electronic.vse_compress .double_wheel_fx8 .wheel-knob {
    width: 57px;
    height: 57px;
}
.vse_electronic.vse_compress .wheel-box .black-circle {
    width: 155px;
    height: 155px;
}
.vse_electronic.vse_compress .wheel-knob {
    width: 100px;
    height: 100px;
}
.vse_electronic.vse_compress .wheel-box .wheel-numbers {
    max-width: 219px;
        margin-top: 7px;
}
.vse_electronic.vse_compress .wheel-board {
width: 216px;
    height: 193px;
}
.vse_electronic.vse_compress .double_wheel_fx8 .wheel-box .wheel-numbers {
    max-width: 140px;
    margin-top: 19px;
}
.vse_electronic.vse_compress.vse_ultimo .double_wheel_fx8 .wheel-box .wheel-numbers {
    max-width: 140px;
    margin-top: 31px;
}

.vse_electronic.vse_compress .double_wheel_fx8 .wheel-board {
    width: 140px;
    height: 140px;
}
.vse_electronic.vse_compress .below_row_fx8 .wheel-board {
    width: 100px;
    height: 80px;
}
.vse_electronic.vse_compress .vse-indicator-col {
    width: 100%;
}
.vse_electronic.vse_compress .sm-title-box {
    text-align: left;
    font-size: 32px;
    font-weight: 500;
    padding-top: 30px;
    width: 100% !important;
}
.vse_electronic.vse_compress .vse_electronic_inner {
        padding: 0 20px 0 30px !important;
}
.wheel-fx1-compressor-stereo-4 {
    width: 50px !important;
    height: 50px !important;
}
/* ultimo compress */
.vse_electronic.vse_compress.vse_ultimo .vse-limit-col {
align-items: center;
padding-top: 0;
}
.vse_electronic.vse_compress.vse_ultimo .double_wheel_fx8 {
flex-direction: column;
}
.vse_electronic.vse_compress.vse_ultimo .wheel-box .detail-txt {
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    margin-top: 0;
}
.vse_electronic.vse_compress.vse_ultimo .vse_electronic_inner > .vse-limit-col > .boxed.wheel-box .detail-txt {
    color: #fff;
    font-weight: 500;
    font-size: 16px;
    margin-top: 20px;
}
.vse_electronic.vse_compress.vse_ultimo .indicator_wrap {
width: 285px;
}
.vse_electronic.vse_compress.vse_ultimo .vse-electronic-col {
    justify-content: center;
    align-items: center;
}
.vse_electronic.vse_compress.vse_ultimo .sm-title-box {
    text-align: center;
    font-size: 14px;
}
.vse_electronic.vse_compress.vse_ultimo .combine_on_inficator {
gap: 15px;
}

.volume-board.fill_up_bar {
width: 24px;
height: 232px;
position: relative;
border-radius: 8px;
z-index: 1;
margin-bottom: 0;
}
.bar_wrap  {
color: #fff;
font-size: 14px;
text-align: center;
display: flex;
 gap: 4px;
 justify-content: flex-end;
align-items: flex-end;
 padding-top: 30px;
 position: relative;
}
.bar_wrap .label_bar {
display: flex;
flex-direction: column;
justify-content: flex-end;
height: 232px;
width: 17px;
}
.label_bar_four {
margin-bottom: 40px;
}
.label_bar_three {
    margin-bottom: 30px;
}
.label_bar_two {
    margin-bottom: 30px;
}
.label_bar_one {
    margin-bottom: 30px;
}
.progress_bar_wrap {
    display: flex;
        flex-direction: column;
        align-items: center;
            position: relative;
                overflow: visible;
}
.volume-board.fill_up_bar .overlay {
background: #000;
}
.progress_bar_wrap .top_bar_lab {
position: absolute;
    z-index: 2;
    top: -30px;
    width: 80px;
}
.vse_electronic.vse_compress.vse_ultimo .sm-title-box {
padding-top: 0;
margin-top: -30px;
    width: auto !important;
}
.label_bar_one_line {
    width: 18px;
        height: 1px;
        position: absolute;
        z-index: 4;
        background: #fff;
        bottom: 37px;
        left: 50%;
        transform: translateX(-50%);
}
.label_bar_two_line {
    width: 18px;
    height: 1px;
    position: absolute;
    z-index: 4;
    background: #fff;
    bottom: 87px;
    left: 50%;
    transform: translateX(-50%);
}
.label_bar_three_line {
    width: 18px;
    height: 1px;
    position: absolute;
    z-index: 4;
    background: #fff;
    bottom: 135px;
    left: 50%;
    transform: translateX(-50%);
}
.label_bar_four_line {
    width: 18px;
    height: 1px;
    position: absolute;
    z-index: 4;
    background: #fff;
    bottom: 194px;
    left: 50%;
    transform: translateX(-50%);
}
.float_top_right {
    position: absolute;
        z-index: 4;
        top: 20px;
        left: 70px;
        color: #fff;
        font-size: 28px;
}

/* xtec */
.effect-inner-row.main-effect-board.vse_xtec {
        height: 394px !important;
}
.vse_xtec .wheel-box .detail-txt {
    font-size: 14px;
    font-weight: 500;
    margin-top: 5px;
}
.vse_xtec.toggle-box {
    flex-direction: row;
    width: 50% !important;
    padding: 0;
}
.vse_xtec .boxed.double-box {
    width: 40% !important;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: 55px;
    padding-left: 30px;
}
.vse_xtec.vse_xtec_eq5 .boxed.double-box {
    gap: 30px;
}
.vse_xtec .vse-limit-col {
    width: 70%;
    padding-left: 60px;
} 
.vse_xtec .vse_electronic_inner {
        justify-content: space-around;
            width: 100%;
                padding: 0 40px;
}
.vse_xtec .vse-limit-col .boxed {
    width: 20%;
}
.vse_xtec .screw-top-left,
.vse_xtec .screw-top-right {
    top: 30%;
}

.vse_xtec .screw-bottom-left,
.vse_xtec .screw-bottom-right {
    bottom: 30%;
}
.vse_xtec .toggle-box .toggle-modes {
    width: 74px;
    height: 38px;
}
.vse_xtec .toggle-box .dark-mode .circle {
    transform: translateX(0);
}
.vse_xtec .toggle-box .circle {
    transform: translateX(118%);
}
.hi_freq_wrap .wheel-board {
    position: relative;
    text-align: center;
    width: 151px;
    height: 140px;
}
.hi_freq_wrap.wheel-box .wheel-numbers {
    max-width: 156px;
    margin-top: 5px;
}
.on-transf {
        position: absolute;
            top: 0;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            z-index: 2;
}
.off-transf {
position: absolute;
    top: 57%;
    right: -15px;
    transform: translateY(-50%);
    color: #fff;
    z-index: 2;
}
.vse_xtec .wheel-knob {
    width: 53px;
    height: 53px;
}
.wheel-knob.knob_partial {
width: 57px;
    height: 57px;
}
.fx6-att-col .wheel-box:last-of-type .black-circle {
    width: 80px;
    height: 80px;
}
.black-circle.transf-knob::after {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
        z-index: 2;
        position: absolute;
        top: -8px;
        left: 50%;
        transform: translateX(-50%);
}
.black-circle.transf-knob::before {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
        z-index: 2;
        position: absolute;
        bottom: 50%;
        right: -7px;
}
.black-circle.attsel-knob {
color: #fff;
}
.attsel-five {
        position: absolute;
            bottom: -18px;
            left: -13px;
            z-index: 3;
}
.attsel-ten {
    position: absolute;
        top: -35px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 3;
}
.attsel-twenty {
        position: absolute;
            bottom: -19px;
            right: -16px;
            z-index: 3;
}
.attsel-five::before {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
        z-index: 2;
        position: absolute;
        bottom: 18px;
        right: -12px;
}

.attsel-ten::before {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
        z-index: 2;
        position: absolute;
    bottom: -8px;
            right: 7px;
}

.attsel-twenty::before {
    content: ' ';
        width: 3px;
        height: 3px;
        border-radius: 50px;
        background-color: #f7f7f7;
            z-index: 2;
            position: absolute;
            bottom: 18px;
            right: 22px;
}
.fx6_inner_knobs .vse-limit-col .black-circle::after {
    content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    color: #f7f7f7;
    z-index: 2;
    position: absolute;
    bottom: 2px;
    right: 10px;
}

.fx6_inner_knobs .vse-limit-col .black-circle::before {
content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    color: #f7f7f7;
    z-index: 2;
    position: absolute;
    bottom: 2px;
    left: 10px;
}
.vse_xtec .dot-right {
    font-size: 14px;
    font-weight: 500;
    position: absolute;
    bottom: -6px;
    right: 17px;
    color: #fff;
}
.vse_xtec .dot-left {
    font-size: 14px;
    font-weight: 500;
    position: absolute;
    bottom: -6px;
    left: 16px;
    color: #fff;
}
.vse_xtec .wheel-box:not(.hi_freq_wrap) .detail-txt {
    margin-top: 23px;
}
.vse_xtec .fx6-att-col .wheel-box .detail-txt {
    margin-top: 3px;
}

/* point knob partial knobs */
.knob-nod-wrap.wheel-xtec-stereo-6 {
    transform: rotate(-220deg);
}
.knob-nod-wrap.wheel-xtec-stereo-9 {
transform: rotate(-230deg);
}
.knob-nod-wrap.wheel-xtec-stereo-10,
.wheel-xteceq5a-stereo-8,
.wheel-xteceq5b-stereo-8 {
transform: rotate(-87deg);
}

/* EQ5 */
.vse_xtec_eq5 .fx6-att-col {
    justify-content: flex-end;
    width: 10%;
}
.vse_xtec.vse_xtec_eq5 .vse-limit-col {
    width: 80%;
}
.vse_xtec.vse_xtec_eq5 .boxed.double-box {
width: 28% !important;
}
.vse_xtec.vse_xtec_eq5 .vse-limit-col .boxed.wheel-box:first-of-type {
width: 22% !important;
}
.ex5_knobs::before,
.ex5_knobs::after {
    display: none;
}
.vse_xtec .boxed.toggle-box {
    width: 142px;
        flex-direction: row;
        padding-top: 0;
}
.vse_xtec .boxed.toggle-box .text-detail {
margin-bottom: 0;
}
.vse_xtec .wheel-box .wheel-numbers {
    max-width: 160px;
    margin-top: 4px;
    margin-left: -13px;
}
.vse_xtec .wheel-box .wheel-numbers.hi_freq_xtex {
    max-width: 160px;
    margin-top: -2px;
    margin-left: -22px;
}
.vse_xtec .wheel-box .wheel-numbers.mid_freq_xtex {
    max-width: 160px;
    margin-top: 2px;
    margin-left: -18px;
}
.vse_xtec .vse-limit-col .boxed {
    display: flex;
    align-items: center;
}
.knob-nod-wrap.wheel-xtec-stereo-2 {
transform: rotate(-155deg);
}
/* .knob-nod-wrap.wheel-xtec-stereo-3 {
transform: rotate(-220deg);
} */
.knob-nod-wrap.wheel-xtec-stereo-4 {
transform: rotate(-155deg);
}
.knob-nod-wrap.wheel-xtec-stereo-8 {
transform: rotate(-87deg);
}

.wheel-xtec-stereo-3.wheel-xtec-stereo_full {
    position: relative;
    width: 57px;
    height: 57px;
    border-radius: 100px;
    background-color: #000;
    transform: rotate(-185deg);
}
.ex5_knobs .wheel-xtec-stereo_full .knob-nod {
    position: absolute;
    top: 1%;
    left: 55%;
    transform: rotate(47deg);
    pointer-events: none;
    z-index: 5;
}
/* designer wave */
.effect-inner-row.main-effect-board.designer_wave {
height: 394px;
}
.designer_wave .title_wrap {
width: 100%;
text-align: center;
}
.designer_wave .double-row_temp_with_title {
    flex-wrap: wrap;
        justify-content: center;
}
.designer_wave .knobs_wrap {
    width: 485px;
        border: 1px solid #fff;
            padding: 15px 25px 10px;
            margin: 0 20px;
            text-align: center;
}
.designer_wave .top-row_flex {
    justify-content: center;
    padding: 0 !important;
}
.designer_wave .double-row_temp_with_title {
gap: 0 !important;
}
.designer_wave .double-row_temp_with_title .top-row_flex>.effects_double_down {
    width: 31.1%;
}
.designer_wave .red_btn_text {
font-size: 40px;
font-weight: 500;
}
.designer_wave .wheel-item {
    margin-bottom: 20px;
}
.designer_wave .top_control {
    margin-top: 20px;
}
.dot-left {
    font-size: 14px;
    font-weight: 500;
    position: absolute;
    bottom: -8px;
    left: 9px;
    color: #fff;
}
.dot-right {
    font-size: 14px;
    font-weight: 500;
    position: absolute;
    bottom: -8px;
    right: 9px;
    color: #fff;
}
.designer_wave .double-row_temp_with_title .black-circle::after {
    content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    color: #f7f7f7;
    z-index: 4;
    position: absolute;
    bottom: 2px;
    right: 10px;
    display: block !important;
}
.designer_wave .double-row_temp_with_title .black-circle::before {
    content: ' ';
    width: 3px;
    height: 3px;
    border-radius: 50px;
    background-color: #f7f7f7;
    color: #f7f7f7;
    z-index: 4;
    position: absolute;
    bottom: 2px;
    left: 10px;
    display: block !important;
}

/* deisgn precision screen */
.knobs_wrap.btns_design {
width: 30%;
    display: flex;
        justify-content: center;
        align-items: center;
        gap: 30px;
        color: #fff;
        text-align: center;
        font-weight: bold;
}
.knobs_wrap.btns_design .single_preset_item {
display: flex;  
flex-direction: column;
align-items: center;
gap: 15px;

}
.knobs_wrap.btns_design .btn-block {
width: 52px;
height: 51px;
}
.knobs_wrap.drag_wrap_design {
    width: 65%;
    display: flex;
    gap:40px;   
    flex-direction: column;
}
.knobs_wrap.drag_wrap_design .top-row_flex {
justify-content: flex-start;
padding-left: 0;
    gap: 45px;
}
.knobs_wrap.drag_wrap_design .relative-effect-board {
margin: 0;
    background: #5A5A5A;
        width: 60px;
        height: 190px;
        border-radius: 13px;
        display: flex;
        align-items: center;
        justify-content: center;
}
.design_precision {
    height: 394px !important;
}
.title-box.design_title_below {
font-size: 14px;
display: flex;
text-align: center;
justify-content: center;
align-items: center; 
    height: 37px;
        width: 65px;
margin-top: 15px;
font-weight: bold;
}
.design_precision .effect-board-route {
height: 158px;
}
.design_precision .effects-dragger {
    top: -29px;
    left: -9px;
}
.design_precision .bond_extend {
    position: relative;
    width: 10px;
    height: 163px;
    margin: auto;
}
/* rotary screen */
.rotary_wrap .toggle-box .toggle-modes {
    width: 74px;
    height: 38px;
}

.rotary_wrap .toggle-box .dark-mode .circle {
    transform: translateX(0);
}

.rotary_wrap .toggle-box .circle {
    transform: translateX(118%);
}
.rotary_wrap .boxed.toggle-box,
.rotary_wrap .one_row_single_col {
    width: 24.1%;
        padding-left: 20px;
        padding-top: 30px;
}
.rotary_wrap .bottom-row_flex {
        gap: 50px;
}
.rotary_wrap .toggle-box {
    flex-direction: row;
}
.rotary_wrap .toggle-box .text-detail {
margin: 0;
}
.rotary_wrap .screw-bottom-left {
    position: absolute;
    bottom: 30%;
    left: 15px;
    z-index: 1;
}

.rotary_wrap .screw-bottom-right {
    position: absolute;
    bottom: 30%;
    right: 15px;
    z-index: 1;
}

.rotary_wrap .screw-top-left {
    position: absolute;
    top: 30%;
    left: 15px;
    z-index: 1;
}

.rotary_wrap .screw-top-right {
    position: absolute;
    top: 30%;
    right: 15px;
    z-index: 1;
}
/* DSS screen */
.dss_wrap .vse-limit-col {
width: 40%;
    justify-content: center;
}
.dss_wrap .vse-limit-col-middle {
width: 12%;
display: flex;
flex-direction: column;
justify-content: space-between;
align-items: center;
text-align: center;
}
.title-box.big_title_dss {
font-size: 40px;
margin-top: 80px;
}
.title-box.sm_title_dss {
font-size: 14px;
margin-bottom: 20px;
}
.vse_electronic.vse_compress.dss_wrap .wheel-board {
    width: 200px;
    height: 180px;
   
}
.dss_wrap .detail-txt {
text-align: center;
}
.title-box.sm_title {
text-align: center;
font-size: 20px;
}
.triple_btn_flex {
        display: flex;
            gap: 10px;
            color: #fff;
}
.buttons_box {
    height: 320px;
        display: flex;
        align-items: center;
        flex-direction: column;
        justify-content: space-between;
}
.buttons_box .title-box.sm_title:first-of-type {
    margin-top: 50px;
}
.dss_wrap .single_preset_item > .btn_knob_control,
.dss_wrap .single_preset_item>.btn-block.male_btn {
border-radius: 4px;
    border: 3px solid #f5f5f5;
    background: #2d2d2f;
    color: #fff;
    text-transform: uppercase;
    font-size: 14px;
    height: 51px;
    width: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-bottom: 2px;
}
.dss_wrap .single_preset_item {
    text-align: center;
}

/* vintage screen */
.vintage_rev {
    height: 370px !important;
}
.vintage_rev .title_wrap {
    font-size: 20px;
    font-weight: 500;
    position: absolute;
    z-index: 4;
    top:15px;
    left: 50%;
    transform: translateX(-50%);
} 
.knobs_wrap.drag_wrap_design.vintage_drag {
    justify-content: center;
}
.vintage_btns {
    width: 15%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: space-between;
            height: 250px;
}
.vintage_drag {
    width: 80%;
}
.vintage_drag .design_signle_wrap {
width: 24%;
}
.knobs_wrap.drag_wrap_design.vintage_drag .top-row_flex {
    align-items: flex-end;
        margin-bottom: 20px;
}
/* 2-4 */
.vintage_drag .top-row_flex .design_signle_wrap,
.vintage_drag .top-row_flex .design_signle_wrap .relative-effect-board {
    height: 276px;
}

.vintage_drag .top-row_flex .design_signle_wrap .effect-board-route {
    height: 241px;
}

.vintage_drag .top-row_flex .design_signle_wrap .bond_extend {
    height: 246px;
}
/* 1 */
.vintage_drag .top-row_flex .design_signle_wrap:first-of-type,
.vintage_drag .top-row_flex .design_signle_wrap:first-of-type .relative-effect-board {
height: 317px;
}
.vintage_drag .top-row_flex .design_signle_wrap:first-of-type .effect-board-route {
height: 287px;
}
.vintage_drag .top-row_flex .design_signle_wrap:first-of-type .bond_extend {
height: 292px;
}
.vintage_drag .effect-board-route {
background: #000;
}
   .vintage_wrap {
    width: 90px;
    height: 26px;
    border: 1px solid #fff;
    color: #fff;
    border-radius: 13px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 2px 8px;
    gap: 5px;
      
   }
.vintage_wrap.vint_db  {
height: 78px;
display: flex;
flex-direction: column;
align-items: flex-start;
justify-content: center;
}
.vintage_wrap.vint_db > div {
    display: flex;
    gap: 5px;
}
.btn_vint,
.vintage_light .green_light_vint,
.vintage_light .red_light_vint {
    width: 15px;
        height: 15px;
        border-radius: 50px;
        border: 2px solid #171717;
        box-shadow: inset 0 23px 0px -21px #f2ede933;
        background: #CE181E;
            opacity: .6;
}
.front_vint .btn_vint,
.rear_vint .btn_vint,
.green_db,
.vintage_light .green_light_vint {
background: #72BF44;
}

.vintage_wrap.vintage_btn,
.vintage_wrap.front_vint,
.vintage_wrap.rear_vint {
cursor: pointer;
}
.design_signle_wrap.vintage_up {
    display: flex;
        align-items: center;
}
.vintage_up .vintage_wrap {
    width: 52px;
    height: 19px;
    border: none;
    color: #fff;
    border-radius: 0;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    padding: 4px 0px 0 6px;
    gap: 5px;
}
.red_control_vintage .bottom_sm_title {
    font-weight: 500;
        font-size: 14px;
        color: #fff;
        text-align: right;
        padding: 5px 3px 0 0;
        position: absolute;
        bottom: -27px;
        right: 0;
}
.green_control_vintage .bottom_sm_title {
    font-weight: 500;
    font-size: 14px;
    color: #fff;
    text-align: right;
    padding: 5px 3px 0 0;
    position: absolute;
    bottom: -56px;
    right: 0;
}
.bottom_sm_title::before {
    content: ' ';
    width: 1px;
    height: 5px;
    background-color: #fff;
    position: absolute;
z-index: 3;
right: 14px;
top: 3px;
}
.green_control_vintage .bottom_sm_title::before {
    content: ' ';
    width: 1px;
    height: 26px;
    background-color: #fff;
    position: absolute;
    z-index: 3;
    right: 38px;
        top: -22px;
}
.green_control_vintage,
.red_control_vintage {
position: relative;
}

.d_vint_wrap.vintage_up .vintage_wrap {
width: 70px !important;
}
.d_vint_wrap.vintage_up .red_control_vintage .vintage_wrap {
    padding-left: 0;
        justify-content: flex-end;
            padding-right: 15px;
}
.d_vint_wrap.vintage_up .green_control_vintage .vintage_wrap {
padding-left: 10px;
}
.d_vint_wrap.vintage_up .green_control_vintage,
.d_vint_wrap.vintage_up .red_control_vintage  {
    display: flex;
    flex-direction: column;
gap: 20px;
}
.d_vint_wrap.vintage_up .red_control_vintage .bottom_sm_title {
    font-weight: 500;
    font-size: 14px;
    color: #fff;
    text-align: right;
    padding: 5px 3px 0 0;
    position: absolute;
    bottom: -92px;
        right: 3px;
}
.d_vint_wrap.vintage_up .red_control_vintage .bottom_sm_title::before {
    content: ' ';
    width: 1px;
    height: 72px;
    background-color: #fff;
    position: absolute;
    z-index: 3;
    right: 19px;
        top: -65px;
}
.d_vint_wrap.vintage_up .green_control_vintage .bottom_sm_title {
    font-weight: 500;
    font-size: 14px;
    color: #fff;
    text-align: right;
    padding: 5px 3px 0 0;
    position: absolute;
    bottom: -92px;
        right: 19px;
}
.d_vint_wrap.vintage_up .green_control_vintage .bottom_sm_title::before {
    content: ' ';
    width: 1px;
    height: 72px;
    background-color: #fff;
    position: absolute;
    z-index: 3;
    right: 33px;
        top: -65px;
}

/* modulation screen */
.modulation_screen .effects_double_down.wheel-item .black-circle::before,
.modulation_screen .effects_double_down.wheel-item .black-circle::after,
.modulation_screen .bottom_control,
.modulation_screen .dot-left,
.modulation_screen .dot-right,
.fx_delay .effects_double_down.wheel-item .black-circle::before,
.fx_delay .effects_double_down.wheel-item .black-circle::after,
.fx_delay .bottom_control,
.fx_delay .dot-left,
.fx_delay .dot-right
 {
display: none;
}
.modulation_screen .top-row_flex,
.fx_delay .top-row_flex{
    border-bottom: none;
}
.modulation_screen .bottom-row_flex .effects_double_down:nth-of-type(4),
.modulation_screen .bottom-row_flex .effects_double_down:nth-of-type(6) {
margin-right: 50px;
}
.modulation_screen .top-row_flex {
justify-content: space-around;
}
.modulation_screen .top-row_flex,
.modulation_screen .bottom-row_flex {
padding: 0;
}
.modulation_screen .bottom-row_flex {
    justify-content: center;
}
.button_mod_flex {
    width: 225px;
    height: 111px;
}
.upper_btn_selector,
.bottom_btn_selector {
    display: flex;
    width: 100%;
    color: #fff;
    font-weight: 500;
}
.button_mod_flex {
    border: 3px solid #fff;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
.upper_btn_selector > div,
.bottom_btn_selector .ser_select,
.one_btn_option > div {
   width: 55px;
   height: 53px;
   border: 2px solid #fff;
   display: flex;
   justify-content: center;
   align-items: center;
   position: relative;
   padding-top: 10px;
   cursor: pointer;
}
.active_module_btn {
    color: #2D2D2F;
    background-color: #fff;
}
.upper_btn_selector>div::before,
.bottom_btn_selector .ser_select::before,
.one_btn_option>div::before {
    content: ' ';
background-color: #ce181e79;
width: 42px;
height: 7px;
border-radius: 4px;
position: absolute;
top: 7px;
font-weight: 500;
}
.active_module_btn::before {
    background-color: #CE181E !important;
}

.one_btn_option {
    display: flex;
}
.screen_number_module,
.count_wrapper {
    width: 100%;
        max-width: 457px;
        height: 150px;
}
 .count_wrapper {
    display: flex;
    align-items: center;
    gap: 5px;
        flex-direction: column;
        background: #000;
        color: #fff;
        justify-content: center;
 }
 .labels_top {
    display: flex;
   width: 100%;
   max-width: 413px;
   align-items: center;
   justify-content: space-around;
 }
 .labels_top_count_bot {
    display: flex;
    width: 414px;
    height: 90px;
    color: #2D2D2F;
    background-color: #fff;
 }
  .count_single_wrap {
    width: 30%;
    display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
  }
  .count_single_wrap:nth-of-type(2) {
width: 40%;
  }
 .count_single_wrap > div {
    font-weight: 500;
    font-size: 32px;
    text-align: center;

 }
   .count_single_wrap input {
        font-size: 32px;
            font-family: "Digital-1", sans-serif !important;
            background: transparent;
            color: #2D2D2F;
            outline: none !important;
            border: 0;
            text-align: center;
            display: inline-block;
            width: 86px;
 }

 /* fx felay chorus fal screen */
 .fx_delay {
    height: 394px !important;
 }
  .fx_delay .labels_top_count_bot,
  .full_row_count,
   .fx_delay .screen_number_module,
   .bottom_text_row {
width: 100%;
    max-width: 95%;
 }
 .bottom_text_row {
    display: flex;
 }
  .fx_delay .bottom-row_flex .wheel-knob {
width: 42.2px !important;
height: 42.2px !important;
 }
   .fx_delay .effects_double_down .wheel-board {
     height: 95px !important;
        width: 110px !important;
 }
  .fx_delay .bottom-row_flex .black-circle {
    width: 66px !important;
    height: 66px !important;
 }
 .fx_delay .bottom-row_flex>.effects_double_down {
width: 8.2% !important;
 }
  .fx_delay .bottom-row_flex {
     width: 95%;
 }
  .fx_delay .double-row_temp {
    padding-left: 35px;
    gap: 0;
 }
   .fx_delay .title_wrap.fx_title {
    font-size: 24px;
    font-weight: 500;
 }
  .fx_delay .bottom_text_row {
    color: #fff;
 }
   .fx_delay .title_wrap.sm_text {
font-size: 14px;
font-weight: 500;
width: 50px;
margin: 0 45px;
text-align: center;
 }
   .fx_delay .count_single_wrap > div,
   .fx_delay .count_single_wrap input  {
    font-size: 32px;
        font-family: "Digital-1", sans-serif !important;
 }
   .fx_delay  .bottom_text_row {
    padding-left: 30px;
  }
  .fx_delay .bottom_text_row .text_background {
width: 35%;
height: 35px;
display: flex;
justify-content: center;
align-items: center;
background-color: #1E1E1E;
border-radius: 5px;
 }
  .fx_delay .screen_number_module,
        .fx_delay .title_wrap.fx_title {
margin-bottom: 15px;
 }

 /* combinator screens */
 .combinator_stereo .knobs_wrap {
width: 59%;
 }
  .combinator_stereo .band_gain_column {
width: 35%;
display: flex;
align-items: center;
justify-content: center;
 }
  .combinator_stereo .double-row_temp_with_title .top-row_flex>.effects_double_down,
  .combinator_stereo .double-row_temp_with_title .bottom-row_flex>.effects_double_down {
      width: 14%;
  }
 .combinator_stereo .double-row_temp_with_title {
    gap: 15px;
    padding-left: 20px;
}
  .band_gain_bg {
    width: 440px;
    height: 334px;
    background-color: #000;
  }
  .progress_line_bar {
    max-width: 347px;
    height: 8px;
    margin: auto;
        display: flex;
  }
  .progress_line_bar>div {
      transition: width 0.3s ease;
  }
  .label_numb_wrapper {
    max-width: 347px;
    margin: 8px auto 0;
    color: #fff;
    font-size: 11px;
    display: flex;
  }
  .label_numb_wrapper div:nth-of-type(1) {
    margin-right: 22px;
  }
  .label_numb_wrapper div:nth-of-type(2) {
      margin-right: 22px;
  }
  .label_numb_wrapper div:nth-of-type(3) {
      margin-right: 22px;
  }

  .label_numb_wrapper div:nth-of-type(4) {
      margin-right: 22px;
  }
  .label_numb_wrapper div:nth-of-type(5) {
      margin-right: 22px;
  }
  .label_numb_wrapper div:nth-of-type(6) {
      margin-right: 40px;
  }
   .label_numb_wrapper div:nth-of-type(7) {
       margin-right: 30px;
   }
        .label_numb_wrapper div:nth-of-type(8) {
            margin-right: 30px;
        }
  .high_progress {
        background-color: #75C044;
        width: 33%;
        height: 100%;
  }
  .himid_progress {
      background-color: #B8CC24;
        width: 24%;
            height: 100%;
  }
  .mid_progress {
      background-color: #FAD705;
        width: 17%;
            height: 100%;
  }
  .lomid_progress {
      background-color: #EA850F;
        width: 13%;
            height: 100%;
  }
  .low_progress {
      background-color: #D1221D;
        width: 13%;
            height: 100%;
  }
.single_reduction_col {
    max-width: 48px;
    display: flex;
    color: #fff;
    font-size: 11px;
    gap: 4px;
}
.label_marker {
        display: flex;
            flex-direction: column;
            justify-content: space-between;
}
.color_item_stop {
    width: 26px;
    height: 8px;
    opacity: .6;
}
.low_col_stop .color_item_stop {
    background-color: #D1221D;
}
.lomid_col_stop .color_item_stop {
    background-color: #EA850F;
}
.mid_col_stop .color_item_stop {
    background-color: #FAD705;
}
.himid_col_stop .color_item_stop {
    background-color: #B8CC24;
}
.high_col_stop .color_item_stop {
    background-color: #75C044;
}
.color_item_stop.active_light {
    opacity: 1 !important;
}
.col_filler {
    display: flex;
        flex-direction: column;
        gap: 2px;
}
.reduction_wrapper {
    max-width: 347px;
    margin: 0 auto 10px;
    display: flex;
    justify-content: space-between;
}
  /*  */
  .combinator_stereo .top_control {
    height: 35px;
    text-align: center;
    margin-top: 0px;
  }
  .combinator_stereo .bottom-row_flex .top_control {
margin-top: 12px;
  }

 .combinator_stereo .top-row_flex .auto_btn_on {
width: 18%;
  }
  .combinator_stereo .double-row_temp_with_title .bottom-row_flex>.effects_double_down {
      width: 18%;
  }
   .combinator_stereo .bottom-row_flex .auto_btn_on {
     position: absolute;
     z-index: 4;
        left: -18px;
            top: 10px;
   }
  .combinator_stereo .auto_mode_on {
display: flex;
flex-direction: column;
align-items: center;
justify-content: center;
gap: 10px;
  }
.combinator_stereo .orange_bulb {
    background: #EA850F;
        width: 16px;
        height: 16px;
        border-radius: 50px;
        border: 2px solid #171717;
}
 .combinator_stereo .auto_mode_on {
    color: #fff;
    font-size: 12px;
 }
 .combinator_stereo .red-btn-in {
     width: 20px;
     height: 20px;
     background: #ce181e;
     border-radius: 100px;
     box-shadow: inset 0 24px 0px -21px #f2ede933;
     cursor: pointer;
 }
 .add_lock_color_xo {
        width: 20px;
            height: 20px;
            background: #464646 !important;
            border-radius: 100px;
            box-shadow: inset 0 24px 0px -21px #f2ede933;
            cursor: pointer;
 }
                .add_lock_color_xo.red_active {
                    background: #ce181e !important;
                }
 .combinator_stereo .red-btn-out {
     width: 30px;
     height: 30px;
     border-radius: 100px;
     background: #171717;
     border: 1px solid #585858;
     display: flex;
     justify-content: center;
     align-items: center;
 }
 .control_spec_wrapper,
  .control_spec_wrapper_b {
    width: 26%;
 }

 .combinator_stereo .wheel-item {
margin-bottom: 22px;
 }
 .float_lock_btn {
  position: relative;
 }
 .combinator_stereo .auto_mode_on {
    gap: 3px
 }
 .combinator_stereo .top-row_flex .black-circle,
  .combinator_stereo .bottom-row_flex .black-circle{
width: 70px !important;
height: 70px !important;
 }
  .combinator_stereo .top-row_flex .black-circle .wheel-knob,
  .combinator_stereo .bottom-row_flex .black-circle .wheel-knob {
      width: 44px !important;
      height: 44px !important;
  }
        .combinator_stereo .bottom-row_flex  {
        padding-left: 0;
        }
  .combinator_stereo .dot-left {
    font-size: 12px;
        bottom: 3px;
        left: 22px;
  }
  .combinator_stereo .dot-right {
      font-size: 12px;
      bottom: 3px;
      right: 22px;
  }
 .combinator_stereo .bottom-row_flex {
    position: relative;
  }
  .combinator_stereo .float_title.title_wrap {
    font-size: 12px;
        text-align: center;
        position: absolute;
        top: -10px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 4;
  }
  /* reductor field */
.title_wrap.band_gain_title {
    font-size: 12px;
    text-align: center;
    padding: 15px;
}

/* control middle center */
.wrap_for_drop_select {
    position: relative;
}
.source-effect_xover.dropdown-detail,
.source-effect_four.dropdown-detail {
    width: 62px;
        height: 29px;
        text-transform: uppercase;
        font-size: 12px;
}
.wrap_for_drop_select .dropdown-menu {
    right: 50%;
        transform: translateX(50%);
        top: -250px;
            width: 222px;
            max-height: 240px;
}
.wrap_for_drop_select.wrap_xover_drop .dropdown-menu {
    top: -231px;
        width: 222px;
        max-height: 222px;
}
.wrap_for_drop_select .dropdown-arrow {
    right: 24px !important;
        top: -27px !important;
            transform: rotate(-90deg);
}
.wrap_for_drop_select .dropdown-option {
    display: flex;
        align-items: center;
        gap: 30px;
}
.bottom_control_select {
    display: flex;
    gap: 15px;
        justify-content: center;
}
.bottom_control_select > .dropdown-detail {
 width: 62px;
 height: 29px;
 text-transform: uppercase;
 font-size: 12px;

}
.control_spec_wrapper,
 .control_spec_wrapper_b {
    display: flex;
    flex-direction: column;
    gap: 10px;
        margin-top: 20px;
}
.top_control_show {
    display: flex;
    gap: 8px;

        justify-content: center;
            max-width: 204px;
            margin: auto;
}
.top_control_show .four_option {
    width: 84px;
    height: 120px;
    background-color: #1E1E1E;
    padding: 10px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    color: #fff;
}
.top_control_show .four_option {
    font-size: 10px;
}
.single_opt {
    display: flex;
    gap: 5px;
}
.single_opt_color.sbc_opt,
.single_opt_color.sbc2_opt {
background-color: #3B6022;
}
.single_opt_color.db48_opt,
.single_opt_color.db482_opt {
    background-color: #7D6C03;
}
.single_opt_color.solo_opt,
.single_opt_color.solo2_opt {
    background-color: #754308;
}
.single_opt_color.byp_opt,
.single_opt_color.byp2_opt {
    background-color: #D1221D;
}
.single_opt_color {
    width: 29px;
    height: 15px;
    border-radius: 4px;
        opacity: .6;
}
.xover_option {
    color: #fff;
    font-size: 10px;
  
}
.single_xover {
    height: 20%;
    display: flex;
    align-items: center;
    gap: 5px;
}
.single_xo_color {
    width: 34px;
    height: 24px;
    opacity: .6;
}
.single_xo_color.high_col {
background-color: #75C044;
}
.single_xo_color.himid_col {
    background-color: #B8CC24;
}
.single_xo_color.mid_col {
    background-color: #FAD705;
}
.single_xo_color.lomid_col {
    background-color: #EA850F;
}
.single_xo_color.low_col {
    background-color: #D1221D;
}
.single_opt_color.active_light,
.single_xo_color.active_light {
opacity: 1;
}
.single_xo_color.active_xo {
    position: relative;
}
.single_xo_color.active_xo::before {
content: ' ';
width: 8px;
    height: 8px;
    background-color: transparent;
    border-radius: 50px;
    border: 3px solid #000;
    position: absolute;
    z-index: 4;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}
.hide_knob {
    display: none;
}
.hide_knob.active_knob {
    display: block;
}
/* animation for vintage */
.green_control {
    display: none;
}
.active_light,
.btn_vint.active_light,
.vintage_light .green_light_vint.active_light,
.vintage_light .red_light_vint.active_light {
    opacity: 1;
}
.effects-dragger {
    cursor: pointer;
}

.effect-insert-block-row .overlay {
    position: absolute;
    border-radius: 0;
    top: 0;
    right: 0;
    left: unset;
}
/*  */
/* animate small btns */
.inactive_red_dot {
    background: #8b060a;
}

/* custom-popup-content preset_popup */
.custom-popup-content.preset_popup {
    display: flex;
        align-items: center;
        justify-content: center;
        padding: 40px;
}
#custom-popup-2 {
bottom: 50px;
    border-top-left-radius: 0;
        border-top-right-radius: 0;
        height: 400px;
}
.custom-popup-content.preset_popup .single-list.preset-second {
    padding-top: 0 !important;
        width: 80% !important;
}
.custom-popup-content.preset_popup .single-list.preset-third {
padding-top: 0 !important;
}
.custom-popup-content.preset_popup .scroll-items {
height: 308px !important;
}
.custom-popup-content.preset_popup .single-list.preset-third .single_preset_item {
gap: 12px;
}
.custom-popup-content.preset_popup .single-item {
    height: 44px;
}