/**
 * VSE Session Manager - Client-side session management
 * Handles room-based sessions with proper isolation
 */

class VSESessionManager {
    constructor() {
        this.socket = null;
        this.sessionKey = null;
        this.userId = null;
        this.role = null; // 'host' or 'client'
        this.isConnected = false;
        
        // Event callbacks
        this.onSessionCreated = null;
        this.onSessionJoined = null;
        this.onUserJoined = null;
        this.onUserLeft = null;
        this.onError = null;
        this.onOSCCommand = null;
        this.onAwaitingApproval = null;
        this.onConnectionApproved = null;
        this.onConnectionDenied = null;
    }

    /**
     * Initialize SocketIO connection
     */
    init() {
        if (this.socket) {
            this.socket.disconnect();
        }

        this.socket = io();
        
        this.socket.on('connect', () => {
            console.log('Connected to VSE server');
            this.isConnected = true;
        });

        this.socket.on('disconnect', () => {
            console.log('Disconnected from VSE server');
            this.isConnected = false;
        });

        this.socket.on('error', (data) => {
            console.error('Session error:', data.message);
            if (this.onError) {
                this.onError(data.message);
            }
        });

        this.socket.on('awaiting_approval', (data) => {
            console.log('Awaiting host approval:', data);
            if (this.onAwaitingApproval) {
                this.onAwaitingApproval(data);
            }
        });

        this.socket.on('connection_approved', (data) => {
            console.log('Connection approved:', data);
            if (this.onConnectionApproved) {
                this.onConnectionApproved(data);
            }
        });

        this.socket.on('connection_denied', (data) => {
            console.log('Connection denied:', data);
            if (this.onConnectionDenied) {
                this.onConnectionDenied(data);
            }
        });

        this.socket.on('join_room_approved', (data) => {
            console.log('Join room approved:', data);
            // Actually join the session room now
            this.socket.emit('client_join_approved', {
                session_key: data.session_key,
                user_id: data.user_id
            });
        });

        this.socket.on('session_joined', (data) => {
            console.log('Joined session:', data);
            if (this.onSessionJoined) {
                this.onSessionJoined(data);
            }
        });

        this.socket.on('user_joined', (data) => {
            console.log('User joined session:', data);
            if (this.onUserJoined) {
                this.onUserJoined(data);
            }
        });

        this.socket.on('user_left', (data) => {
            console.log('User left session:', data);
            if (this.onUserLeft) {
                this.onUserLeft(data);
            }
        });

        this.socket.on('osc_command', (data) => {
            console.log('OSC command received:', data);
            if (this.onOSCCommand) {
                this.onOSCCommand(data);
            }
        });

        this.socket.on('osc_executed', (data) => {
            const endTime = performance.now();
            const clientStartTime = data.client_timestamp;
            
            if (clientStartTime) {
                const latency = endTime - clientStartTime;
                console.log(`🚀 OSC LATENCY: ${latency.toFixed(2)}ms - ${data.result}`);
                
                // Log latency in a visually distinct way
                if (window.log) {
                    const latencyColor = latency < 50 ? 'success' : latency < 150 ? 'warning' : 'error';
                    const latencyStatus = latency < 50 ? 'EXCELLENT' : latency < 150 ? 'GOOD' : 'HIGH';
                    window.log(`⚡ LATENCY: ${latency.toFixed(2)}ms (${latencyStatus}) - ${data.result}`, latencyColor);
                }
            }
        });

        this.socket.on('board_state_update', (data) => {
            console.log('Received board state update:', data);
            if (this.onBoardStateUpdate) {
                this.onBoardStateUpdate(data.board_state);
            }
        });

        // WebRTC signaling events
        this.socket.on('offer', (data) => {
            console.log('WebRTC offer received:', data);
            // Handle WebRTC offer
        });

        this.socket.on('answer', (data) => {
            console.log('WebRTC answer received:', data);
            // Handle WebRTC answer
        });

        this.socket.on('ice-candidate', (data) => {
            console.log('ICE candidate received:', data);
            // Handle ICE candidate
        });
    }

    /**
     * Create a new session (host)
     */
    async createSession(userId = null) {
        try {
            const response = await fetch('/create-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    role: 'host',
                    user_id: userId
                })
            });

            const data = await response.json();
            
            if (response.ok) {
                this.sessionKey = data.session_key;
                this.userId = data.host_user_id;
                this.role = 'host';
                
                // Join the SocketIO room
                this.joinSessionRoom();
                
                // Trigger callback if set
                if (this.onSessionCreated) {
                    this.onSessionCreated({
                        session_key: this.sessionKey,
                        user_id: this.userId,
                        role: this.role
                    });
                }
                
                return {
                    success: true,
                    session_key: this.sessionKey,
                    sessionKey: this.sessionKey,  // Keep both for compatibility
                    userId: this.userId
                };
            } else {
                throw new Error(data.error || 'Failed to create session');
            }
        } catch (error) {
            console.error('Error creating session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Join an existing session (client)
     */
    async joinSession(sessionKey, userId = null) {
        try {
            // First validate the session exists
            const validateResponse = await fetch('/validate-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_key: sessionKey
                })
            });

            const validateData = await validateResponse.json();
            
            if (!validateResponse.ok || !validateData.valid) {
                throw new Error(validateData.message || 'Invalid session');
            }

            // Generate user ID if not provided
            if (!userId) {
                userId = 'client_' + Math.random().toString(36).substr(2, 9);
            }

            // Join the session
            const joinResponse = await fetch('/join-session', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    session_key: sessionKey,
                    user_id: userId,
                    role: 'client'
                })
            });

            const joinData = await joinResponse.json();
            
            if (joinResponse.ok) {
                this.sessionKey = sessionKey;
                this.userId = userId;
                this.role = 'client';
                
                // Join the SocketIO room
                this.joinSessionRoom();
                
                return {
                    success: true,
                    sessionKey: this.sessionKey,
                    userId: this.userId,
                    sessionStatus: joinData.session_status
                };
            } else {
                throw new Error(joinData.error || 'Failed to join session');
            }
        } catch (error) {
            console.error('Error joining session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Join the SocketIO room for the session
     */
    joinSessionRoom() {
        if (!this.socket || !this.sessionKey || !this.userId) {
            console.error('Cannot join room: missing socket, session key, or user ID');
            return;
        }

        // Gather client info for host approval
        const clientInfo = {
            user_agent: navigator.userAgent,
            platform: navigator.platform,
            language: navigator.language,
            timestamp: new Date().toISOString()
        };

        this.socket.emit('join_session_room', {
            session_key: this.sessionKey,
            user_id: this.userId,
            role: this.role,
            client_info: clientInfo
        });
    }

    /**
     * Leave the current session
     */
    async leaveSession() {
        try {
            if (this.sessionKey && this.userId) {
                // Leave SocketIO room
                this.socket.emit('leave_session_room', {
                    session_key: this.sessionKey,
                    user_id: this.userId
                });

                // Leave session on server
                const response = await fetch('/leave-session', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        user_id: this.userId
                    })
                });

                if (response.ok) {
                    this.sessionKey = null;
                    this.userId = null;
                    this.role = null;
                    
                    return { success: true };
                } else {
                    const data = await response.json();
                    throw new Error(data.error || 'Failed to leave session');
                }
            }
        } catch (error) {
            console.error('Error leaving session:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * Send OSC command to the session
     */
    sendOSCCommand(command) {
        if (!this.socket || !this.sessionKey || !this.userId) {
            console.error('Cannot send OSC command: not in a session');
            return false;
        }

        this.socket.emit('osc_command', {
            session_key: this.sessionKey,
            user_id: this.userId,
            command: command
        });

        return true;
    }

    /**
     * Send OSC command with timestamp for latency measurement
     */
    sendOSCCommandWithTimestamp(command, startTime) {
        if (!this.socket || !this.sessionKey || !this.userId) {
            console.error('Cannot send OSC command: not in a session');
            return false;
        }

        this.socket.emit('osc_command', {
            session_key: this.sessionKey,
            user_id: this.userId,
            command: command,
            client_timestamp: startTime
        });

        return true;
    }

    /**
     * Send WebRTC offer
     */
    sendOffer(offer) {
        if (!this.socket || !this.sessionKey) {
            console.error('Cannot send offer: not in a session');
            return false;
        }

        this.socket.emit('offer', {
            session_key: this.sessionKey,
            user_id: this.userId,
            offer: offer
        });

        return true;
    }

    /**
     * Send WebRTC answer
     */
    sendAnswer(answer) {
        if (!this.socket || !this.sessionKey) {
            console.error('Cannot send answer: not in a session');
            return false;
        }

        this.socket.emit('answer', {
            session_key: this.sessionKey,
            user_id: this.userId,
            answer: answer
        });

        return true;
    }

    /**
     * Send ICE candidate
     */
    sendIceCandidate(candidate) {
        if (!this.socket || !this.sessionKey) {
            console.error('Cannot send ICE candidate: not in a session');
            return false;
        }

        this.socket.emit('ice-candidate', {
            session_key: this.sessionKey,
            user_id: this.userId,
            candidate: candidate
        });

        return true;
    }

    /**
     * Get current session status
     */
    async getSessionStatus() {
        if (!this.sessionKey) {
            return { error: 'No active session' };
        }

        try {
            const response = await fetch(`/session-status/${this.sessionKey}`);
            const data = await response.json();
            
            if (response.ok) {
                return { success: true, ...data };
            } else {
                return { success: false, error: data.error };
            }
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * Disconnect from the session and server
     */
    disconnect() {
        if (this.socket) {
            this.socket.disconnect();
            this.socket = null;
        }
        
        this.sessionKey = null;
        this.userId = null;
        this.role = null;
        this.isConnected = false;
    }
}

// Create global instance
window.vseSessionManager = new VSESessionManager();
